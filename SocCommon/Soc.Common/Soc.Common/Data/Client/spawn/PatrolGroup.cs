//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.spawn
{
    public sealed partial class PatrolGroup : Bright.Config.BeanBase 
    {
        public PatrolGroup() { }

        public PatrolGroup(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { int _v = default; if (_json.TryGetValue("id", ref _v)) Id = _v; }
            { int _v = default; if (_json.TryGetValue("groupId", ref _v)) GroupId = _v; }
            { int _v = default; if (_json.TryGetValue("templateId", ref _v)) TemplateId = _v; }
            { string _v = default; if (_json.TryGetValue("entityType", ref _v)) EntityType = _v; }
            { PatrolEntityType _v = default; if (_json.TryGetValue("fllowerType", ref _v)) FllowerType = _v; }
            { float _v = default; if (_json.TryGetValue("xPositionOffset", ref _v)) XPositionOffset = _v; }
            { float _v = default; if (_json.TryGetValue("yPositionOffset", ref _v)) YPositionOffset = _v; }
            { float _v = default; if (_json.TryGetValue("zPositionOffset", ref _v)) ZPositionOffset = _v; }
            { float _v = default; if (_json.TryGetValue("yRotationOffset", ref _v)) YRotationOffset = _v; }
            { float _v = default; if (_json.TryGetValue("radiusPursuit", ref _v)) RadiusPursuit = _v; }
#else
            _json.TryGetValue("id", ref Id);
            _json.TryGetValue("groupId", ref GroupId);
            _json.TryGetValue("templateId", ref TemplateId);
            _json.TryGetValue("entityType", ref EntityType);
            _json.TryGetValue("fllowerType", ref FllowerType);
            _json.TryGetValue("xPositionOffset", ref XPositionOffset);
            _json.TryGetValue("yPositionOffset", ref YPositionOffset);
            _json.TryGetValue("zPositionOffset", ref ZPositionOffset);
            _json.TryGetValue("yRotationOffset", ref YRotationOffset);
            _json.TryGetValue("radiusPursuit", ref RadiusPursuit);
#endif
            PostInit();
        }

        public static PatrolGroup DeserializePatrolGroup(JSONNode _json)
        {
            return new spawn.PatrolGroup(false, _json);
        }

        /// <summary>
        /// 唯一ID
        /// </summary>
#if UNITY_EDITOR
        public int Id { get; private set; }
#else
        public int Id;
#endif
        /// <summary>
        /// 实体组编号
        /// </summary>
#if UNITY_EDITOR
        public int GroupId { get; private set; }
#else
        public int GroupId;
#endif
        /// <summary>
        /// 实体id
        /// </summary>
#if UNITY_EDITOR
        public int TemplateId { get; private set; }
#else
        public int TemplateId;
#endif
        /// <summary>
        /// 实体类型
        /// </summary>
#if UNITY_EDITOR
        public string EntityType { get; private set; }
#else
        public string EntityType;
#endif
#if UNITY_EDITOR
        public PatrolEntityType FllowerType { get; private set; }
#else
        public PatrolEntityType FllowerType;
#endif
        /// <summary>
        /// x轴位置偏移值
        /// </summary>
#if UNITY_EDITOR
        public float XPositionOffset { get; private set; }
#else
        public float XPositionOffset;
#endif
        /// <summary>
        /// y轴位置偏移值
        /// </summary>
#if UNITY_EDITOR
        public float YPositionOffset { get; private set; }
#else
        public float YPositionOffset;
#endif
        /// <summary>
        /// z轴位置偏移值
        /// </summary>
#if UNITY_EDITOR
        public float ZPositionOffset { get; private set; }
#else
        public float ZPositionOffset;
#endif
        /// <summary>
        /// y轴旋转偏移值
        /// </summary>
#if UNITY_EDITOR
        public float YRotationOffset { get; private set; }
#else
        public float YRotationOffset;
#endif
        /// <summary>
        /// 追击半径
        /// </summary>
#if UNITY_EDITOR
        public float RadiusPursuit { get; private set; }
#else
        public float RadiusPursuit;
#endif

        public const int __ID__ = 157640064;
        public override int GetTypeId() => __ID__;



	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"GroupId\":" + GroupId + ","
            + "\"TemplateId\":" + TemplateId + ","
            + "\"EntityType\":" + $"\"{EntityType}\"" + ","
            + "\"FllowerType\":" + (int)FllowerType + ","
            + "\"XPositionOffset\":" + XPositionOffset + ","
            + "\"YPositionOffset\":" + YPositionOffset + ","
            + "\"ZPositionOffset\":" + ZPositionOffset + ","
            + "\"YRotationOffset\":" + YRotationOffset + ","
            + "\"RadiusPursuit\":" + RadiusPursuit
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
