//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.monument
{
    public sealed partial class BuildingVolumeConfig : Bright.Config.BeanBase 
    {
        public BuildingVolumeConfig() { }

        public BuildingVolumeConfig(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { string _v = default; if (_json.TryGetValue("name", ref _v)) Name = _v; }
            { System.Collections.Generic.List<BuildingVolume> _v = default; if (_json.TryGetValue("buildingVolumes", ref _v)) BuildingVolumes = _v; }
#else
            _json.TryGetValue("name", ref Name);
            _json.TryGetValue("buildingVolumes", ref BuildingVolumes);
#endif
            PostInit();
        }

        public static BuildingVolumeConfig DeserializeBuildingVolumeConfig(JSONNode _json)
        {
            return new monument.BuildingVolumeConfig(false, _json);
        }

        /// <summary>
        /// 关卡名字
        /// </summary>
#if UNITY_EDITOR
        public string Name { get; private set; }
#else
        public string Name;
#endif
        /// <summary>
        /// 关卡区域信息
        /// </summary>
#if UNITY_EDITOR
        public System.Collections.Generic.List<BuildingVolume> BuildingVolumes { get; private set; }
#else
        public System.Collections.Generic.List<BuildingVolume> BuildingVolumes;
#endif

        public const int __ID__ = 753509303;
        public override int GetTypeId() => __ID__;

            

        public override void Resolve(Dictionary<string, BaseTable> _tables)
        {
            foreach(var _e in BuildingVolumes) { _e?.Resolve(_tables); }
            PostResolve();
        }

	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"Name\":" + $"\"{Name}\"" + ","
            + "\"BuildingVolumes\":" + Bright.Common.StringUtil.CollectionToString(BuildingVolumes)
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
