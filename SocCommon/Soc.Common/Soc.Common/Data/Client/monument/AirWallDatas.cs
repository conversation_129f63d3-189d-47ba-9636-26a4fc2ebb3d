//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.monument
{
    public sealed partial class AirWallDatas : Bright.Config.BeanBase 
    {
        public AirWallDatas() { }

        public AirWallDatas(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { int _v = default; if (_json.TryGetValue("MapId", ref _v)) MapId = _v; }
            { System.Collections.Generic.List<AirWallData> _v = default; if (_json.TryGetValue("airWalls", ref _v)) AirWalls = _v; }
#else
            _json.TryGetValue("MapId", ref MapId);
            _json.TryGetValue("airWalls", ref AirWalls);
#endif
            PostInit();
        }

        public static AirWallDatas DeserializeAirWallDatas(JSONNode _json)
        {
            return new monument.AirWallDatas(false, _json);
        }

        /// <summary>
        /// 地图Id
        /// </summary>
#if UNITY_EDITOR
        public int MapId { get; private set; }
#else
        public int MapId;
#endif
        /// <summary>
        /// 空气墙数据
        /// </summary>
#if UNITY_EDITOR
        public System.Collections.Generic.List<AirWallData> AirWalls { get; private set; }
#else
        public System.Collections.Generic.List<AirWallData> AirWalls;
#endif

        public const int __ID__ = -991300836;
        public override int GetTypeId() => __ID__;

            

        public override void Resolve(Dictionary<string, BaseTable> _tables)
        {
            foreach(var _e in AirWalls) { _e?.Resolve(_tables); }
            PostResolve();
        }

	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"MapId\":" + MapId + ","
            + "\"AirWalls\":" + Bright.Common.StringUtil.CollectionToString(AirWalls)
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
