//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data
{
    public abstract partial class ActionBase : Bright.Config.BeanBase 
    {
        public ActionBase() { }

        public ActionBase(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
#else
#endif
            PostInit();
        }

        public static ActionBase DeserializeActionBase(JSONNode _json)
        {
            string type = _json["$type"];
            switch (type)
            {
                case "PlayUXAction": return new PlayUXAction(false, _json);
                case "PlayEffectAction": return new PlayEffectAction(false, _json);
                case "CauseDamageAction": return new CauseDamageAction(false, _json);
                case "AddBuffAction": return new AddBuffAction(false, _json);
                case "ReduceSpeedAction": return new ReduceSpeedAction(false, _json);
                case "CreateMagicFieldAction": return new CreateMagicFieldAction(false, _json);
	            default: throw new SerializationException();
            }
        }





	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
