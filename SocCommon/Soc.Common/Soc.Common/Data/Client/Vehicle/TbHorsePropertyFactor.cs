//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Vehicle
{
    public sealed partial class TbHorsePropertyFactor: BaseTable, IConfigTable
    {
        public static int HashCode = 341905575;

        private Vehicle.HorsePropertyFactor _data;

        public Bright.Config.BeanBase Data => _data;

        public TbHorsePropertyFactor(JSONNode _json)
        {
            if (_json["data"].Count != 1)
            {
                throw new SerializationException("Deserialize TbHorsePropertyFactor error: TableMode = one, but json size != 1.");
            }

            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbHorsePropertyFactor error: HashCode not equal, Sheet may be changed!");
            }
            try
            {
                _data = Vehicle.HorsePropertyFactor.DeserializeHorsePropertyFactor(_json["data"][0]);
            }
            catch (SerializationException ex)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbHorsePropertyFactor error: " + ex.Message);
            }
            PostInit();
        }

        /// <summary>
        /// 马匹摇杆死区y
        /// </summary>
        public int HorseDeadZoneY => _data.HorseDeadZoneY;
        /// <summary>
        /// 马匹摇杆死区x
        /// </summary>
        public int HorseDeadZoneX => _data.HorseDeadZoneX;
        /// <summary>
        /// 马摇杆前后向控制角度
        /// </summary>
        public float ToleranceAngleFB => _data.ToleranceAngleFB;
        /// <summary>
        /// 马摇杆左右向控制角度
        /// </summary>
        public float ToleranceAngleLR => _data.ToleranceAngleLR;
        /// <summary>
        /// 马匹冲刺时间
        /// </summary>
        public float HorseIdle2SprintTime => _data.HorseIdle2SprintTime;
        /// <summary>
        /// 马匹后退时间
        /// </summary>
        public float HorseIdle2BackTime => _data.HorseIdle2BackTime;
        /// <summary>
        /// 马匹退出冲刺区域(不急停)
        /// </summary>
        public float HorseSprint2RunZone => _data.HorseSprint2RunZone;
        /// <summary>
        /// 马匹转向减速系数
        /// </summary>
        public float HorseSteeringBackFactor => _data.HorseSteeringBackFactor;
        /// <summary>
        /// 马匹后退减速系数
        /// </summary>
        public float HorseBackFactor => _data.HorseBackFactor;
        /// <summary>
        /// 马匹转向减速度
        /// </summary>
        public float HorseSteeringAC => _data.HorseSteeringAC;
        /// <summary>
        /// 马匹后退转向减速度
        /// </summary>
        public float HorseSteeringBackAC => _data.HorseSteeringBackAC;
        /// <summary>
        /// 马匹冲刺转向减速度
        /// </summary>
        public float HorseSprintSteeringAC => _data.HorseSprintSteeringAC;
        /// <summary>
        /// 跳跃时旋转速度
        /// </summary>
        public float HorseJumpSteerRate => _data.HorseJumpSteerRate;
        /// <summary>
        /// 跳跃冷却
        /// </summary>
        public float HorseJumpFrozen => _data.HorseJumpFrozen;
        /// <summary>
        /// 马匹模拟重力加速度系数
        /// </summary>
        public float HorseGravityFactor => _data.HorseGravityFactor;
        /// <summary>
        /// 起跳阈值
        /// </summary>
        public float HorseJumpThreshold => _data.HorseJumpThreshold;
        /// <summary>
        /// jumpFroward阈值
        /// </summary>
        public float JumpFrowardThreshold => _data.JumpFrowardThreshold;
        /// <summary>
        /// JumpCanter阈值
        /// </summary>
        public float JumpCanterThreshold => _data.JumpCanterThreshold;
        /// <summary>
        /// JumpGallop阈值
        /// </summary>
        public float JumpGallopThreshold => _data.JumpGallopThreshold;
        /// <summary>
        /// JumpSprint阈值
        /// </summary>
        public float JumpSprintThreshold => _data.JumpSprintThreshold;
        /// <summary>
        /// jumpFroward起跳速度
        /// </summary>
        public float JumpFrowardSpeedForZAxis => _data.JumpFrowardSpeedForZAxis;
        /// <summary>
        /// JumpCanter起跳速度
        /// </summary>
        public float JumpCanterSpeedForZAxis => _data.JumpCanterSpeedForZAxis;
        /// <summary>
        /// JumpGallop起跳速度
        /// </summary>
        public float JumpGallopSpeedForZAxis => _data.JumpGallopSpeedForZAxis;
        /// <summary>
        /// JumpSprint起跳速度
        /// </summary>
        public float JumpSprintSpeedForZAxis => _data.JumpSprintSpeedForZAxis;
        /// <summary>
        /// 马落地宽容距离
        /// </summary>
        public float HorseFalThresholdl => _data.HorseFalThresholdl;
        /// <summary>
        /// 急停减速度
        /// </summary>
        public float HorseBrakeSpeed => _data.HorseBrakeSpeed;
        /// <summary>
        /// 急停阈值
        /// </summary>
        public float HorseBrakeThreshold => _data.HorseBrakeThreshold;
        /// <summary>
        /// 急停时间
        /// </summary>
        public float HorseBrakeTime => _data.HorseBrakeTime;
        /// <summary>
        /// 跳跃耐力消耗
        /// </summary>
        public int JumpStaminaUsed => _data.JumpStaminaUsed;
        /// <summary>
        /// sprint摇杆拖拽角度
        /// </summary>
        public float Run2SprintAngle => _data.Run2SprintAngle;
        /// <summary>
        /// sprint摇杆拖拽距离
        /// </summary>
        public float Run2SprintLength => _data.Run2SprintLength;
        /// <summary>
        /// Idle动画时长
        /// </summary>
        public float IdleAnimTime => _data.IdleAnimTime;
        /// <summary>
        /// 后退速度动画映射值
        /// </summary>
        public float BackSpeedAnimMatching => _data.BackSpeedAnimMatching;
        /// <summary>
        /// Walk速度动画映射值
        /// </summary>
        public float WalkSpeedAnimMatching => _data.WalkSpeedAnimMatching;
        /// <summary>
        /// Trot速度动画映射值
        /// </summary>
        public float TrotSpeedAnimMatching => _data.TrotSpeedAnimMatching;
        /// <summary>
        /// Canter速度动画映射值
        /// </summary>
        public float CanterSpeedAnimMatching => _data.CanterSpeedAnimMatching;
        /// <summary>
        /// Gallop速度动画映射值
        /// </summary>
        public float GallopSpeedAnimMatching => _data.GallopSpeedAnimMatching;
        /// <summary>
        /// Sprints速度动画映射值
        /// </summary>
        public float SprintSpeedAnimMatching => _data.SprintSpeedAnimMatching;
        /// <summary>
        /// CC坡度
        /// </summary>
        public float SlopeLimit => _data.SlopeLimit;
        /// <summary>
        /// CC台阶高度
        /// </summary>
        public float StepOffset => _data.StepOffset;
        /// <summary>
        /// CC半径
        /// </summary>
        public float Radius => _data.Radius;
        /// <summary>
        /// CC高度
        /// </summary>
        public float CcHeight => _data.CcHeight;
        /// <summary>
        /// CC载人高度
        /// </summary>
        public float CcRiderHeight => _data.CcRiderHeight;
        /// <summary>
        /// 浅水区速度
        /// </summary>
        public float InfluentSpeed => _data.InfluentSpeed;
        /// <summary>
        /// 深水区速度
        /// </summary>
        public float FloodedSpeed => _data.FloodedSpeed;
        /// <summary>
        /// 死亡时间
        /// </summary>
        public float DeadTime => _data.DeadTime;
        /// <summary>
        /// 马匹最大跟随距离
        /// </summary>
        public float HorseMaxFollowDir => _data.HorseMaxFollowDir;
        /// <summary>
        /// 马匹最小跟随距离（不退出跟随状态）
        /// </summary>
        public float HorseMinFollowDir => _data.HorseMinFollowDir;
        /// <summary>
        /// 栓马桩停止距离
        /// </summary>
        public float HorseFeedingbarsStop => _data.HorseFeedingbarsStop;
        /// <summary>
        /// 野外食物停止距离
        /// </summary>
        public float HorseFruitStop => _data.HorseFruitStop;
        /// <summary>
        /// 丢弃食物停止距离
        /// </summary>
        public float HorseDropsStop => _data.HorseDropsStop;
        /// <summary>
        /// 马匹最大跟随运动速度
        /// </summary>
        public float HorseMaxFollowSpeed => _data.HorseMaxFollowSpeed;
        /// <summary>
        /// 马匹跟随运动加速度
        /// </summary>
        public float HorseFollowAC => _data.HorseFollowAC;
        /// <summary>
        /// 马匹跟随旋转角度（1s）
        /// </summary>
        public float HorseFollowSteerRate => _data.HorseFollowSteerRate;
        /// <summary>
        /// 马匹跟随旋转最大速度（与马匹run一致）
        /// </summary>
        public float HorseFollowMaxSteerRate => _data.HorseFollowMaxSteerRate;
        /// <summary>
        /// 马匹吃食片段时长
        /// </summary>
        public float HorseEatTime => _data.HorseEatTime;
        /// <summary>
        /// 是否启用骑手足部IK
        /// </summary>
        public bool RiderIkControl => _data.RiderIkControl;
        /// <summary>
        /// 受伤状态伤害阈值
        /// </summary>
        public float DamageThreshold => _data.DamageThreshold;
        /// <summary>
        /// 受伤状态时长
        /// </summary>
        public float HorseGetHitTime => _data.HorseGetHitTime;
        /// <summary>
        /// 受伤打断时长
        /// </summary>
        public float HorseGetHitCutTime => _data.HorseGetHitCutTime;
        /// <summary>
        /// 虚弱状态血量百分比
        /// </summary>
        public float HorseWeakHpPercent => _data.HorseWeakHpPercent;
        /// <summary>
        /// 马喂食卡路里血量系数
        /// </summary>
        public float HorseFeedCaloriesHpFactor => _data.HorseFeedCaloriesHpFactor;
        /// <summary>
        /// 马喂食水分血量系数
        /// </summary>
        public float HorseFeedHydrationHpFactor => _data.HorseFeedHydrationHpFactor;
        /// <summary>
        /// 马喂食卡路里耐力系数
        /// </summary>
        public float HorseFeedCaloriesStaminaFactor => _data.HorseFeedCaloriesStaminaFactor;
        /// <summary>
        /// 马喂食卡路里耐力系数
        /// </summary>
        public float HorseFeedHydrationStaminaFactor => _data.HorseFeedHydrationStaminaFactor;
        /// <summary>
        /// 不可冲刺体力阈值
        /// </summary>
        public float HorsSprintThreshold => _data.HorsSprintThreshold;
        /// <summary>
        /// 最小体力上限
        /// </summary>
        public float MinStaminaThreshold => _data.MinStaminaThreshold;
        /// <summary>
        /// 基础体力恢复速度
        /// </summary>
        public float BaseStaminaRate => _data.BaseStaminaRate;
        /// <summary>
        /// 主idle播放次数
        /// </summary>
        public int MainIdleCount => _data.MainIdleCount;
        /// <summary>
        /// 上马动作角度
        /// </summary>
        public float MountDirAngle => _data.MountDirAngle;
        /// <summary>
        /// 上马动作时间
        /// </summary>
        public float MountTime => _data.MountTime;
        /// <summary>
        /// CC滑移水平速度
        /// </summary>
        public float HorseFallenHorizon => _data.HorseFallenHorizon;
        /// <summary>
        /// CC滑移垂直速度
        /// </summary>
        public float HorseFallenVertical => _data.HorseFallenVertical;

        public override void Resolve(Dictionary<string, BaseTable> _tables)
        {
            _data?.Resolve(_tables);
            PostResolve();
        }

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            _data.Update(_json);
            TriggerDataUpdateEvent();
        }

        partial void PostInit();
    }
}

#endif