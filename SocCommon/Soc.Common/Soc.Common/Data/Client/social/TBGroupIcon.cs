//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.social
{
    public sealed partial class TBGroupIcon: BaseTable
    {
        public static int HashCode = 1550293732;

        public readonly Dictionary<int, social.OBJGroupIcon> DataMap = new();
        public readonly List<social.OBJGroupIcon> DataList = new();

        public TBGroupIcon(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TBGroupIcon error: HashCode not equal, Sheet may be changed!");
            }
			
            var dataNode = _json["data"];
            DataMap.EnsureCapacity(dataNode.Count);
            DataList.Capacity = dataNode.Count;
            foreach (JSONNode _row in dataNode.Children)
            {
                try
                {
                    var _v = social.OBJGroupIcon.DeserializeOBJGroupIcon(_row);
                    DataList.Add(_v);
                    DataMap.Add(_v.ID, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TBGroupIcon error: " + ex.Message);
                }
            }
            enumer = DataMap.Values;
            PostInit();
        }

        public social.OBJGroupIcon GetOrDefault(int key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

			int ID = default;
			_json.TryGetValue("ID", ref ID);
			
            if (DataMap.TryGetValue(ID, out var v0))
            {
                v0.Update(_json);
            }
            else
            {
                var v1 = social.OBJGroupIcon.DeserializeOBJGroupIcon(_json);
                DataList.Add(v1);
                DataMap.Add(v1.ID, v1);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();
    }
}

#endif