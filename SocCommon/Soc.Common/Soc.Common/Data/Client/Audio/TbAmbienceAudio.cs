//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Audio
{
    public sealed partial class TbAmbienceAudio: BaseTable
    {
        public static int HashCode = 1176948521;

        public readonly Dictionary<int, Audio.AmbienceAudio> DataMap = new();
        public readonly List<Audio.AmbienceAudio> DataList = new();

        public TbAmbienceAudio(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbAmbienceAudio error: HashCode not equal, Sheet may be changed!");
            }
			
            var dataNode = _json["data"];
            DataMap.EnsureCapacity(dataNode.Count);
            DataList.Capacity = dataNode.Count;
            foreach (JSONNode _row in dataNode.Children)
            {
                try
                {
                    var _v = Audio.AmbienceAudio.DeserializeAmbienceAudio(_row);
                    DataList.Add(_v);
                    DataMap.Add(_v.Id, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TbAmbienceAudio error: " + ex.Message);
                }
            }
            enumer = DataMap.Values;
            PostInit();
        }

        public Audio.AmbienceAudio GetOrDefault(int key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

			int Id = default;
			_json.TryGetValue("id", ref Id);
			
            if (DataMap.TryGetValue(Id, out var v0))
            {
                v0.Update(_json);
            }
            else
            {
                var v1 = Audio.AmbienceAudio.DeserializeAmbienceAudio(_json);
                DataList.Add(v1);
                DataMap.Add(v1.Id, v1);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();
    }
}

#endif