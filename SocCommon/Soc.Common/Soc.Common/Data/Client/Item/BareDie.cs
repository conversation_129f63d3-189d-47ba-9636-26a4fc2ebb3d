//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Item
{
    public sealed partial class BareDie : Bright.Config.BeanBase 
    {
        public BareDie() { }

        public BareDie(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { int _v = default; if (_json.TryGetValue("belongPosition", ref _v)) BelongPosition = _v; }
            { string _v = default; if (_json.TryGetValue("tpPrefab", ref _v)) TpPrefab = _v; }
            { string _v = default; if (_json.TryGetValue("newTpPrefab", ref _v)) NewTpPrefab = _v; }
            { string _v = default; if (_json.TryGetValue("fpPrefab", ref _v)) FpPrefab = _v; }
            { string _v = default; if (_json.TryGetValue("bpPrefab", ref _v)) BpPrefab = _v; }
            { string _v = default; if (_json.TryGetValue("lbPrefab", ref _v)) LbPrefab = _v; }
            { int[] _v = default; if (_json.TryGetValue("occupyPosition", ref _v)) OccupyPosition = _v; }
#else
            _json.TryGetValue("belongPosition", ref BelongPosition);
            _json.TryGetValue("tpPrefab", ref TpPrefab);
            _json.TryGetValue("newTpPrefab", ref NewTpPrefab);
            _json.TryGetValue("fpPrefab", ref FpPrefab);
            _json.TryGetValue("bpPrefab", ref BpPrefab);
            _json.TryGetValue("lbPrefab", ref LbPrefab);
            _json.TryGetValue("occupyPosition", ref OccupyPosition);
#endif
            PostInit();
        }

        public static BareDie DeserializeBareDie(JSONNode _json)
        {
            return new Item.BareDie(false, _json);
        }

        /// <summary>
        /// 归属部位
        /// </summary>
#if UNITY_EDITOR
        public int BelongPosition { get; private set; }
#else
        public int BelongPosition;
#endif
        /// <summary>
        /// 第三人称预制路径
        /// </summary>
#if UNITY_EDITOR
        public string TpPrefab { get; private set; }
#else
        public string TpPrefab;
#endif
        /// <summary>
        /// 新第三人称预制路径
        /// </summary>
#if UNITY_EDITOR
        public string NewTpPrefab { get; private set; }
#else
        public string NewTpPrefab;
#endif
        /// <summary>
        /// 第一人称预制路径
        /// </summary>
#if UNITY_EDITOR
        public string FpPrefab { get; private set; }
#else
        public string FpPrefab;
#endif
        /// <summary>
        /// 背包界面预制路径
        /// </summary>
#if UNITY_EDITOR
        public string BpPrefab { get; private set; }
#else
        public string BpPrefab;
#endif
        /// <summary>
        /// 大厅预制路径
        /// </summary>
#if UNITY_EDITOR
        public string LbPrefab { get; private set; }
#else
        public string LbPrefab;
#endif
        /// <summary>
        /// 占用部位
        /// </summary>
#if UNITY_EDITOR
        public int[] OccupyPosition { get; private set; }
#else
        public int[] OccupyPosition;
#endif

        public const int __ID__ = 624092307;
        public override int GetTypeId() => __ID__;



	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"BelongPosition\":" + BelongPosition + ","
            + "\"TpPrefab\":" + $"\"{TpPrefab}\"" + ","
            + "\"NewTpPrefab\":" + $"\"{NewTpPrefab}\"" + ","
            + "\"FpPrefab\":" + $"\"{FpPrefab}\"" + ","
            + "\"BpPrefab\":" + $"\"{BpPrefab}\"" + ","
            + "\"LbPrefab\":" + $"\"{LbPrefab}\"" + ","
            + "\"OccupyPosition\":" + Bright.Common.StringUtil.CollectionToString(OccupyPosition)
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
