//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.mall
{
    public sealed partial class TBLotteryProgressRewards: BaseTable
    {
        public static int HashCode = 2112213694;

        public readonly List<mall.OBJLotteryProgressRewards> DataList;

        private Dictionary<(int, int), mall.OBJLotteryProgressRewards> DataMapUnion;

        public TBLotteryProgressRewards(JSONNode _json)
        {
            DataList = new List<mall.OBJLotteryProgressRewards>();

            foreach(JSONNode _row in _json["data"].Children)
            {
                var _v = mall.OBJLotteryProgressRewards.DeserializeOBJLotteryProgressRewards(_row);
                DataList.Add(_v);
            }
            DataMapUnion = new Dictionary<(int, int), mall.OBJLotteryProgressRewards>();
            foreach(var _v in DataList)
            {
                DataMapUnion.Add((_v.LotteryID, _v.ProgressID), _v);
            }
            enumer = DataList;
            PostInit();
        }

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            var _v = mall.OBJLotteryProgressRewards.DeserializeOBJLotteryProgressRewards(_json);

            if (DataMapUnion.TryGetValue((_v.LotteryID, _v.ProgressID), out var existing))
            {
                existing.Update(_json);
            }
            else
            {
                DataMapUnion.Add((_v.LotteryID, _v.ProgressID), _v);
                DataList.Add(_v);
            }
            TriggerDataUpdateEvent();
        }

        public mall.OBJLotteryProgressRewards Get(int lotteryID, int progressID) => DataMapUnion.TryGetValue((lotteryID, progressID), out mall.OBJLotteryProgressRewards __v) ? __v : null;

        partial void PostInit();
    }
}

#endif