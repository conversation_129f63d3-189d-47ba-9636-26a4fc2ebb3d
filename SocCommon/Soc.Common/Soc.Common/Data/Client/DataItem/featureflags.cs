//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

namespace WizardGames.Soc.Common.Data.DataItem
{
    public enum featureflags
    {
        /// <summary>
        /// 一级科技工作台
        /// </summary>
        Level1Workbench = 0,
        /// <summary>
        /// 二级科技工作台
        /// </summary>
        Level2Workbench = 1,
        /// <summary>
        /// 三级科技工作台
        /// </summary>
        Level3Workbench = 2,
        /// <summary>
        /// 防御全身
        /// </summary>
        ProtectFullBody = 3,
        /// <summary>
        /// 防御头部
        /// </summary>
        ProtectHead = 4,
        /// <summary>
        /// 防御胸部
        /// </summary>
        ProtectChest = 5,
        /// <summary>
        /// 防御下半身
        /// </summary>
        ProtectLowerBody = 6,
        /// <summary>
        /// 箭矢
        /// </summary>
        UseArrows = 7,
        /// <summary>
        /// 火箭弹弹药
        /// </summary>
        UseRocketLaunchers = 8,
        /// <summary>
        /// 手枪子弹
        /// </summary>
        UsePistolAmmo = 9,
        /// <summary>
        /// 12号霰弹
        /// </summary>
        Use12GaugeShotgunAmmo = 10,
        /// <summary>
        /// 5.56毫米步枪弹药
        /// </summary>
        Use556mmRifleAmmo = 11,
        /// <summary>
        /// 高速子弹
        /// </summary>
        HighBulletVelocity = 12,
        /// <summary>
        /// 火焰子弹
        /// </summary>
        AChanceToIgniteFire = 13,
        /// <summary>
        /// 爆炸子弹
        /// </summary>
        MayCauseAnExplosion = 14,
        /// <summary>
        /// 少量近战防御
        /// </summary>
        WeakMeleeDefense = 15,
        /// <summary>
        /// 适量近战防御
        /// </summary>
        ModerateMeleeDefense = 16,
        /// <summary>
        /// 显著近战防御
        /// </summary>
        StrongMeleeDefense = 17,
        /// <summary>
        /// 少量弹药防御
        /// </summary>
        WeakAmmunitionDefense = 18,
        /// <summary>
        /// 适量弹药防御
        /// </summary>
        ModerateAmmunitionDefense = 19,
        /// <summary>
        /// 显著弹药防御
        /// </summary>
        StrongAmmunitionDefense = 20,
        /// <summary>
        /// 少量寒冷防御
        /// </summary>
        WeakColdResistance = 21,
        /// <summary>
        /// 适量寒冷防御
        /// </summary>
        ModerateColdResistance = 22,
        /// <summary>
        /// 显著寒冷防御
        /// </summary>
        StrongColdResistance = 23,
        /// <summary>
        /// 少量辐射防御
        /// </summary>
        WeakRadiationProtection = 24,
        /// <summary>
        /// 适量辐射防御
        /// </summary>
        ModerateRadiationProtection = 25,
        /// <summary>
        /// 显著辐射防御
        /// </summary>
        StrongRadiationProtection = 26,
        /// <summary>
        /// 少量湿度防御
        /// </summary>
        WeakMoistureDefense = 27,
        /// <summary>
        /// 适量湿度防御
        /// </summary>
        ModerateMoistureDefense = 28,
        /// <summary>
        /// 显著湿度防御
        /// </summary>
        StrongMoistureDefense = 29,
        /// <summary>
        /// 鱼叉弹药
        /// </summary>
        Usespeargunammo = 30,
        /// <summary>
        /// 40mm榴弹
        /// </summary>
        Usegrenade40mm = 31,
        /// <summary>
        /// 钉枪弹药
        /// </summary>
        Usenailgunammo = 32,
        /// <summary>
        /// 烟雾弹（火箭弹、40mm榴弹专属）
        /// </summary>
        Usesmokerocket = 33,
    }
}

#endif