//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data
{
    public sealed partial class BeansDSConstructionGroup : Bright.Config.BeanBase 
    {
        public BeansDSConstructionGroup() { }

        public BeansDSConstructionGroup(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { int _v = default; if (_json.TryGetValue("Id", ref _v)) Id = _v; }
            { System.Collections.Generic.List<BeansDSConstructionData> _v = default; if (_json.TryGetValue("ConstructionData", ref _v)) ConstructionData = _v; }
            { System.Collections.Generic.List<BeansWireData> _v = default; if (_json.TryGetValue("WireData", ref _v)) WireData = _v; }
#else
            _json.TryGetValue("Id", ref Id);
            _json.TryGetValue("ConstructionData", ref ConstructionData);
            _json.TryGetValue("WireData", ref WireData);
#endif
            PostInit();
        }

        public static BeansDSConstructionGroup DeserializeBeansDSConstructionGroup(JSONNode _json)
        {
            return new BeansDSConstructionGroup(false, _json);
        }

        /// <summary>
        /// 占位，无意义，不走表
        /// </summary>
#if UNITY_EDITOR
        public int Id { get; private set; }
#else
        public int Id;
#endif
#if UNITY_EDITOR
        public System.Collections.Generic.List<BeansDSConstructionData> ConstructionData { get; private set; }
#else
        public System.Collections.Generic.List<BeansDSConstructionData> ConstructionData;
#endif
#if UNITY_EDITOR
        public System.Collections.Generic.List<BeansWireData> WireData { get; private set; }
#else
        public System.Collections.Generic.List<BeansWireData> WireData;
#endif

        public const int __ID__ = -2019023908;
        public override int GetTypeId() => __ID__;

            
            

        public override void Resolve(Dictionary<string, BaseTable> _tables)
        {
            foreach(var _e in ConstructionData) { _e?.Resolve(_tables); }
            foreach(var _e in WireData) { _e?.Resolve(_tables); }
            PostResolve();
        }

	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"ConstructionData\":" + Bright.Common.StringUtil.CollectionToString(ConstructionData) + ","
            + "\"WireData\":" + Bright.Common.StringUtil.CollectionToString(WireData)
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
