//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data
{
    public sealed partial class TableRecord : Bright.Config.BeanBase 
    {
        public TableRecord() { }

        public TableRecord(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { string _v = default; if (_json.TryGetValue("full_name", ref _v)) FullName = _v; }
            { string _v = default; if (_json.TryGetValue("value_type", ref _v)) ValueType = _v; }
            { bool _v = default; if (_json.TryGetValue("define_from_file", ref _v)) DefineFromFile = _v; }
            { string[] _v = default; if (_json.TryGetValue("input", ref _v)) Input = _v; }
            { string _v = default; if (_json.TryGetValue("index", ref _v)) Index = _v; }
            { string _v = default; if (_json.TryGetValue("mode", ref _v)) Mode = _v; }
            { string[] _v = default; if (_json.TryGetValue("group", ref _v)) Group = _v; }
            { string _v = default; if (_json.TryGetValue("comment", ref _v)) Comment = _v; }
            { string[] _v = default; if (_json.TryGetValue("patch_input", ref _v)) PatchInput = _v; }
            { string[] _v = default; if (_json.TryGetValue("tags", ref _v)) Tags = _v; }
            { string _v = default; if (_json.TryGetValue("output", ref _v)) Output = _v; }
#else
            _json.TryGetValue("full_name", ref FullName);
            _json.TryGetValue("value_type", ref ValueType);
            _json.TryGetValue("define_from_file", ref DefineFromFile);
            _json.TryGetValue("input", ref Input);
            _json.TryGetValue("index", ref Index);
            _json.TryGetValue("mode", ref Mode);
            _json.TryGetValue("group", ref Group);
            _json.TryGetValue("comment", ref Comment);
            _json.TryGetValue("patch_input", ref PatchInput);
            _json.TryGetValue("tags", ref Tags);
            _json.TryGetValue("output", ref Output);
#endif
            PostInit();
        }

        public static TableRecord DeserializeTableRecord(JSONNode _json)
        {
            return new TableRecord(false, _json);
        }

        /// <summary>
        /// 全名(包含模块和名字)
        /// </summary>
#if UNITY_EDITOR
        public string FullName { get; private set; }
#else
        public string FullName;
#endif
        /// <summary>
        /// 记录类名
        /// </summary>
#if UNITY_EDITOR
        public string ValueType { get; private set; }
#else
        public string ValueType;
#endif
        /// <summary>
        /// 从excel读取定义
        /// </summary>
#if UNITY_EDITOR
        public bool DefineFromFile { get; private set; }
#else
        public bool DefineFromFile;
#endif
        /// <summary>
        /// 文件列表
        /// </summary>
#if UNITY_EDITOR
        public string[] Input { get; private set; }
#else
        public string[] Input;
#endif
        /// <summary>
        /// 表id字段
        /// </summary>
#if UNITY_EDITOR
        public string Index { get; private set; }
#else
        public string Index;
#endif
        /// <summary>
        /// 模式
        /// </summary>
#if UNITY_EDITOR
        public string Mode { get; private set; }
#else
        public string Mode;
#endif
        /// <summary>
        /// 分组
        /// </summary>
#if UNITY_EDITOR
        public string[] Group { get; private set; }
#else
        public string[] Group;
#endif
        /// <summary>
        /// 注释
        /// </summary>
#if UNITY_EDITOR
        public string Comment { get; private set; }
#else
        public string Comment;
#endif
        /// <summary>
        /// 分支文件列表
        /// </summary>
#if UNITY_EDITOR
        public string[] PatchInput { get; private set; }
#else
        public string[] PatchInput;
#endif
#if UNITY_EDITOR
        public string[] Tags { get; private set; }
#else
        public string[] Tags;
#endif
        /// <summary>
        /// 输出文件名
        /// </summary>
#if UNITY_EDITOR
        public string Output { get; private set; }
#else
        public string Output;
#endif

        public const int __ID__ = 1888528799;
        public override int GetTypeId() => __ID__;



	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"FullName\":" + $"\"{FullName}\"" + ","
            + "\"ValueType\":" + $"\"{ValueType}\"" + ","
            + "\"DefineFromFile\":" + DefineFromFile.ToString().ToLower() + ","
            + "\"Input\":" + Bright.Common.StringUtil.CollectionToString(Input) + ","
            + "\"Index\":" + $"\"{Index}\"" + ","
            + "\"Mode\":" + $"\"{Mode}\"" + ","
            + "\"Group\":" + Bright.Common.StringUtil.CollectionToString(Group) + ","
            + "\"Comment\":" + $"\"{Comment}\"" + ","
            + "\"PatchInput\":" + Bright.Common.StringUtil.CollectionToString(PatchInput) + ","
            + "\"Tags\":" + Bright.Common.StringUtil.CollectionToString(Tags) + ","
            + "\"Output\":" + $"\"{Output}\""
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
