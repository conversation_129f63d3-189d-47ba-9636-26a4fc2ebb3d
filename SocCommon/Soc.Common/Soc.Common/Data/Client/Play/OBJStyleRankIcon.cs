//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Play
{
    public sealed partial class OBJStyleRankIcon : Bright.Config.BeanBase 
    {
        public OBJStyleRankIcon() { }

        public OBJStyleRankIcon(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { int _v = default; if (_json.TryGetValue("styleID", ref _v)) StyleID = _v; }
            { int _v = default; if (_json.TryGetValue("largeRank", ref _v)) LargeRank = _v; }
            { string _v = default; if (_json.TryGetValue("largeRankIcon", ref _v)) LargeRankIcon = _v; }
            { string _v = default; if (_json.TryGetValue("smallRankIcon", ref _v)) SmallRankIcon = _v; }
            { string _v = default; if (_json.TryGetValue("nameRankIcon", ref _v)) NameRankIcon = _v; }
            { int _v = default; if (_json.TryGetValue("rankName", ref _v)) RankName = _v; }
#else
            _json.TryGetValue("styleID", ref StyleID);
            _json.TryGetValue("largeRank", ref LargeRank);
            _json.TryGetValue("largeRankIcon", ref LargeRankIcon);
            _json.TryGetValue("smallRankIcon", ref SmallRankIcon);
            _json.TryGetValue("nameRankIcon", ref NameRankIcon);
            _json.TryGetValue("rankName", ref RankName);
#endif
            PostInit();
        }

        public static OBJStyleRankIcon DeserializeOBJStyleRankIcon(JSONNode _json)
        {
            return new Play.OBJStyleRankIcon(false, _json);
        }

        /// <summary>
        /// 风格ID
        /// </summary>
#if UNITY_EDITOR
        public int StyleID { get; private set; }
#else
        public int StyleID;
#endif
        /// <summary>
        /// 大段位
        /// </summary>
#if UNITY_EDITOR
        public int LargeRank { get; private set; }
#else
        public int LargeRank;
#endif
        /// <summary>
        /// 大段位图标
        /// </summary>
#if UNITY_EDITOR
        public string LargeRankIcon { get; private set; }
#else
        public string LargeRankIcon;
#endif
        /// <summary>
        /// 小段位图标
        /// </summary>
#if UNITY_EDITOR
        public string SmallRankIcon { get; private set; }
#else
        public string SmallRankIcon;
#endif
        /// <summary>
        /// 姓名板图标
        /// </summary>
#if UNITY_EDITOR
        public string NameRankIcon { get; private set; }
#else
        public string NameRankIcon;
#endif
        /// <summary>
        /// 段位名称
        /// </summary>
#if UNITY_EDITOR
        public int RankName { get; private set; }
#else
        public int RankName;
#endif

        public const int __ID__ = -537685159;
        public override int GetTypeId() => __ID__;



	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"StyleID\":" + StyleID + ","
            + "\"LargeRank\":" + LargeRank + ","
            + "\"LargeRankIcon\":" + $"\"{LargeRankIcon}\"" + ","
            + "\"SmallRankIcon\":" + $"\"{SmallRankIcon}\"" + ","
            + "\"NameRankIcon\":" + $"\"{NameRankIcon}\"" + ","
            + "\"RankName\":" + RankName
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
