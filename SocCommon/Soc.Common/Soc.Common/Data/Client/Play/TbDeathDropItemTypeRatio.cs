//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Play
{
    public sealed partial class TbDeathDropItemTypeRatio: BaseTable
    {
        public static int HashCode = 1220253774;

        public readonly List<Play.DeathDropItemTypeRatio> DataList;

        private Dictionary<(int, int), Play.DeathDropItemTypeRatio> DataMapUnion;

        public TbDeathDropItemTypeRatio(JSONNode _json)
        {
            DataList = new List<Play.DeathDropItemTypeRatio>();

            foreach(JSONNode _row in _json["data"].Children)
            {
                var _v = Play.DeathDropItemTypeRatio.DeserializeDeathDropItemTypeRatio(_row);
                DataList.Add(_v);
            }
            DataMapUnion = new Dictionary<(int, int), Play.DeathDropItemTypeRatio>();
            foreach(var _v in DataList)
            {
                DataMapUnion.Add((_v.ItemGroupId, _v.ItemType), _v);
            }
            enumer = DataList;
            PostInit();
        }

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            var _v = Play.DeathDropItemTypeRatio.DeserializeDeathDropItemTypeRatio(_json);

            if (DataMapUnion.TryGetValue((_v.ItemGroupId, _v.ItemType), out var existing))
            {
                existing.Update(_json);
            }
            else
            {
                DataMapUnion.Add((_v.ItemGroupId, _v.ItemType), _v);
                DataList.Add(_v);
            }
            TriggerDataUpdateEvent();
        }

        public Play.DeathDropItemTypeRatio Get(int ItemGroupId, int ItemType) => DataMapUnion.TryGetValue((ItemGroupId, ItemType), out Play.DeathDropItemTypeRatio __v) ? __v : null;

        partial void PostInit();
    }
}

#endif