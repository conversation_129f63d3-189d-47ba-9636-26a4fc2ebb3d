//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.gun
{
    public sealed partial class PCGunViewkick : Bright.Config.BeanBase 
    {
        public PCGunViewkick() { }

        public PCGunViewkick(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
#if UNITY_EDITOR
            { int _v = default; if (_json.TryGetValue("vkid", ref _v)) Vkid = _v; }
            { int _v = default; if (_json.TryGetValue("vktype", ref _v)) Vktype = _v; }
            { int _v = default; if (_json.TryGetValue("stage", ref _v)) Stage = _v; }
            { string _v = default; if (_json.TryGetValue("rotation", ref _v)) Rotation = _v; }
            { bool _v = default; if (_json.TryGetValue("iscos", ref _v)) Iscos = _v; }
            { float _v = default; if (_json.TryGetValue("amplitudemax", ref _v)) Amplitudemax = _v; }
            { float _v = default; if (_json.TryGetValue("amplitudemin", ref _v)) Amplitudemin = _v; }
            { bool _v = default; if (_json.TryGetValue("randompositive", ref _v)) Randompositive = _v; }
            { float _v = default; if (_json.TryGetValue("cycle", ref _v)) Cycle = _v; }
            { bool _v = default; if (_json.TryGetValue("cyclerandom", ref _v)) Cyclerandom = _v; }
            { float _v = default; if (_json.TryGetValue("cyclemax", ref _v)) Cyclemax = _v; }
            { float _v = default; if (_json.TryGetValue("cyclemin", ref _v)) Cyclemin = _v; }
            { float _v = default; if (_json.TryGetValue("damping", ref _v)) Damping = _v; }
            { bool _v = default; if (_json.TryGetValue("dampingrandom", ref _v)) Dampingrandom = _v; }
            { float _v = default; if (_json.TryGetValue("dampingmax", ref _v)) Dampingmax = _v; }
            { float _v = default; if (_json.TryGetValue("dampingmin", ref _v)) Dampingmin = _v; }
            { float _v = default; if (_json.TryGetValue("phase", ref _v)) Phase = _v; }
            { bool _v = default; if (_json.TryGetValue("phaserandom", ref _v)) Phaserandom = _v; }
            { float _v = default; if (_json.TryGetValue("phasemax", ref _v)) Phasemax = _v; }
            { float _v = default; if (_json.TryGetValue("phasemin", ref _v)) Phasemin = _v; }
            { float _v = default; if (_json.TryGetValue("livetimemax", ref _v)) Livetimemax = _v; }
            { float _v = default; if (_json.TryGetValue("livetimemin", ref _v)) Livetimemin = _v; }
            { float _v = default; if (_json.TryGetValue("returnVelocity", ref _v)) ReturnVelocity = _v; }
            { bool _v = default; if (_json.TryGetValue("inherit", ref _v)) Inherit = _v; }
#else
            _json.TryGetValue("vkid", ref Vkid);
            _json.TryGetValue("vktype", ref Vktype);
            _json.TryGetValue("stage", ref Stage);
            _json.TryGetValue("rotation", ref Rotation);
            _json.TryGetValue("iscos", ref Iscos);
            _json.TryGetValue("amplitudemax", ref Amplitudemax);
            _json.TryGetValue("amplitudemin", ref Amplitudemin);
            _json.TryGetValue("randompositive", ref Randompositive);
            _json.TryGetValue("cycle", ref Cycle);
            _json.TryGetValue("cyclerandom", ref Cyclerandom);
            _json.TryGetValue("cyclemax", ref Cyclemax);
            _json.TryGetValue("cyclemin", ref Cyclemin);
            _json.TryGetValue("damping", ref Damping);
            _json.TryGetValue("dampingrandom", ref Dampingrandom);
            _json.TryGetValue("dampingmax", ref Dampingmax);
            _json.TryGetValue("dampingmin", ref Dampingmin);
            _json.TryGetValue("phase", ref Phase);
            _json.TryGetValue("phaserandom", ref Phaserandom);
            _json.TryGetValue("phasemax", ref Phasemax);
            _json.TryGetValue("phasemin", ref Phasemin);
            _json.TryGetValue("livetimemax", ref Livetimemax);
            _json.TryGetValue("livetimemin", ref Livetimemin);
            _json.TryGetValue("returnVelocity", ref ReturnVelocity);
            _json.TryGetValue("inherit", ref Inherit);
#endif
            PostInit();
        }

        public static PCGunViewkick DeserializePCGunViewkick(JSONNode _json)
        {
            return new gun.PCGunViewkick(false, _json);
        }

        /// <summary>
        /// viewkickid
        /// </summary>
#if UNITY_EDITOR
        public int Vkid { get; private set; }
#else
        public int Vkid;
#endif
        /// <summary>
        /// viewkicktype
        /// </summary>
#if UNITY_EDITOR
        public int Vktype { get; private set; }
#else
        public int Vktype;
#endif
        /// <summary>
        /// 阶段
        /// </summary>
#if UNITY_EDITOR
        public int Stage { get; private set; }
#else
        public int Stage;
#endif
        /// <summary>
        /// 旋转轴
        /// </summary>
#if UNITY_EDITOR
        public string Rotation { get; private set; }
#else
        public string Rotation;
#endif
        /// <summary>
        /// iscos
        /// </summary>
#if UNITY_EDITOR
        public bool Iscos { get; private set; }
#else
        public bool Iscos;
#endif
        /// <summary>
        /// 振幅max
        /// </summary>
#if UNITY_EDITOR
        public float Amplitudemax { get; private set; }
#else
        public float Amplitudemax;
#endif
        /// <summary>
        /// 振幅min
        /// </summary>
#if UNITY_EDITOR
        public float Amplitudemin { get; private set; }
#else
        public float Amplitudemin;
#endif
        /// <summary>
        /// 正负是否随机
        /// </summary>
#if UNITY_EDITOR
        public bool Randompositive { get; private set; }
#else
        public bool Randompositive;
#endif
        /// <summary>
        /// 周期
        /// </summary>
#if UNITY_EDITOR
        public float Cycle { get; private set; }
#else
        public float Cycle;
#endif
        /// <summary>
        /// 周期是否随机
        /// </summary>
#if UNITY_EDITOR
        public bool Cyclerandom { get; private set; }
#else
        public bool Cyclerandom;
#endif
        /// <summary>
        /// 周期max
        /// </summary>
#if UNITY_EDITOR
        public float Cyclemax { get; private set; }
#else
        public float Cyclemax;
#endif
        /// <summary>
        /// 周期min
        /// </summary>
#if UNITY_EDITOR
        public float Cyclemin { get; private set; }
#else
        public float Cyclemin;
#endif
        /// <summary>
        /// 衰减
        /// </summary>
#if UNITY_EDITOR
        public float Damping { get; private set; }
#else
        public float Damping;
#endif
        /// <summary>
        /// 衰减是否随机
        /// </summary>
#if UNITY_EDITOR
        public bool Dampingrandom { get; private set; }
#else
        public bool Dampingrandom;
#endif
        /// <summary>
        /// 衰减max
        /// </summary>
#if UNITY_EDITOR
        public float Dampingmax { get; private set; }
#else
        public float Dampingmax;
#endif
        /// <summary>
        /// 衰减min
        /// </summary>
#if UNITY_EDITOR
        public float Dampingmin { get; private set; }
#else
        public float Dampingmin;
#endif
        /// <summary>
        /// 相位
        /// </summary>
#if UNITY_EDITOR
        public float Phase { get; private set; }
#else
        public float Phase;
#endif
        /// <summary>
        /// 相位是否随机
        /// </summary>
#if UNITY_EDITOR
        public bool Phaserandom { get; private set; }
#else
        public bool Phaserandom;
#endif
        /// <summary>
        /// 相位max
        /// </summary>
#if UNITY_EDITOR
        public float Phasemax { get; private set; }
#else
        public float Phasemax;
#endif
        /// <summary>
        /// 相位min
        /// </summary>
#if UNITY_EDITOR
        public float Phasemin { get; private set; }
#else
        public float Phasemin;
#endif
        /// <summary>
        /// 持续时间max
        /// </summary>
#if UNITY_EDITOR
        public float Livetimemax { get; private set; }
#else
        public float Livetimemax;
#endif
        /// <summary>
        /// 持续时间min
        /// </summary>
#if UNITY_EDITOR
        public float Livetimemin { get; private set; }
#else
        public float Livetimemin;
#endif
        /// <summary>
        /// 归零速度
        /// </summary>
#if UNITY_EDITOR
        public float ReturnVelocity { get; private set; }
#else
        public float ReturnVelocity;
#endif
        /// <summary>
        /// 是否继承
        /// </summary>
#if UNITY_EDITOR
        public bool Inherit { get; private set; }
#else
        public bool Inherit;
#endif

        public const int __ID__ = 1009181610;
        public override int GetTypeId() => __ID__;



	#if UNITY_EDITOR
        public override string ToString() => "{ "
            + "\"Vkid\":" + Vkid + ","
            + "\"Vktype\":" + Vktype + ","
            + "\"Stage\":" + Stage + ","
            + "\"Rotation\":" + $"\"{Rotation}\"" + ","
            + "\"Iscos\":" + Iscos.ToString().ToLower() + ","
            + "\"Amplitudemax\":" + Amplitudemax + ","
            + "\"Amplitudemin\":" + Amplitudemin + ","
            + "\"Randompositive\":" + Randompositive.ToString().ToLower() + ","
            + "\"Cycle\":" + Cycle + ","
            + "\"Cyclerandom\":" + Cyclerandom.ToString().ToLower() + ","
            + "\"Cyclemax\":" + Cyclemax + ","
            + "\"Cyclemin\":" + Cyclemin + ","
            + "\"Damping\":" + Damping + ","
            + "\"Dampingrandom\":" + Dampingrandom.ToString().ToLower() + ","
            + "\"Dampingmax\":" + Dampingmax + ","
            + "\"Dampingmin\":" + Dampingmin + ","
            + "\"Phase\":" + Phase + ","
            + "\"Phaserandom\":" + Phaserandom.ToString().ToLower() + ","
            + "\"Phasemax\":" + Phasemax + ","
            + "\"Phasemin\":" + Phasemin + ","
            + "\"Livetimemax\":" + Livetimemax + ","
            + "\"Livetimemin\":" + Livetimemin + ","
            + "\"ReturnVelocity\":" + ReturnVelocity + ","
            + "\"Inherit\":" + Inherit.ToString().ToLower()
            + "}";
	#endif
	
        partial void PostInit();
    }
}

#endif
