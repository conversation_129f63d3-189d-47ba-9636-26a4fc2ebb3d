//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.units
{
    public sealed partial class TbGatherResourcesOre: BaseTable
    {
        public static int HashCode = 526813581;

        public readonly Dictionary<long, units.GatherResourcesOre> DataMap = new();
        public readonly List<units.GatherResourcesOre> DataList = new();

        public TbGatherResourcesOre(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbGatherResourcesOre error: HashCode not equal, Sheet may be changed!");
            }
			
            var dataNode = _json["data"];
            DataMap.EnsureCapacity(dataNode.Count);
            DataList.Capacity = dataNode.Count;
            foreach (JSONNode _row in dataNode.Children)
            {
                try
                {
                    var _v = units.GatherResourcesOre.DeserializeGatherResourcesOre(_row);
                    DataList.Add(_v);
                    DataMap.Add(_v.Id, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TbGatherResourcesOre error: " + ex.Message);
                }
            }
            enumer = DataMap.Values;
            PostInit();
        }

        public units.GatherResourcesOre GetOrDefault(long key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

			long Id = default;
			_json.TryGetValue("id", ref Id);
			
            if (DataMap.TryGetValue(Id, out var v0))
            {
                v0.Update(_json);
            }
            else
            {
                var v1 = units.GatherResourcesOre.DeserializeGatherResourcesOre(_json);
                DataList.Add(v1);
                DataMap.Add(v1.Id, v1);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();
    }
}

#endif