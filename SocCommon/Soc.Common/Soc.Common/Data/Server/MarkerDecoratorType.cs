//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

namespace WizardGames.Soc.Common.Data
{
    [System.Flags]
    public enum MarkerDecoratorType
    {
        /// <summary>
        /// 默认
        /// </summary>
        Default = 0,
        /// <summary>
        /// 大地图
        /// </summary>
        BigMap = 1,
        /// <summary>
        /// 死亡地图
        /// </summary>
        DeathMap = 2,
        /// <summary>
        /// Hud罗盘
        /// </summary>
        HudDirection = 4,
        /// <summary>
        /// 屏显
        /// </summary>
        Hud = 8,
        /// <summary>
        /// 喀秋莎
        /// </summary>
        Katyusha = 16,
        /// <summary>
        /// 小地图
        /// </summary>
        MiniMap = 32,
    }
}

#endif