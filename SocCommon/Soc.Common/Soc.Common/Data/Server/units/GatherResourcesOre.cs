//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.units
{
    public sealed partial class GatherResourcesOre : Bright.Config.BeanBase 
    {
        public GatherResourcesOre() { }

        public GatherResourcesOre(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("name")) { var _j = _json["name"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Name_l10n_index = _j["index"]; } else { throw new SerializationException("Name.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("startHealth")) { var _j = _json["startHealth"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  StartHealth  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("combatComponent")) { var _j = _json["combatComponent"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  CombatComponent  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BaseProtection")) { var _j = _json["BaseProtection"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  BaseProtection  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("stages")) { var _j = _json["stages"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; Stages = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  Stages[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("gatherType")) { var _j = _json["gatherType"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  GatherType  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("dropId")) { var _j = _json["dropId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  DropId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("maxDestroyFractionForFinishBonus")) { var _j = _json["maxDestroyFractionForFinishBonus"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MaxDestroyFractionForFinishBonus  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("finishBonusDropId")) { var _j = _json["finishBonusDropId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  FinishBonusDropId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("resPath")) { var _j = _json["resPath"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ResPath  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("shatterPath")) { var _j = _json["shatterPath"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ShatterPath  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("colliderResPath")) { var _j = _json["colliderResPath"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ColliderResPath  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("isShowUI")) { var _j = _json["isShowUI"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { IsShowUI = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("UIType")) { var _j = _json["UIType"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  UIType  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("highLightId")) { var _j = _json["highLightId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  HighLightId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("heightOffset")) { var _j = _json["heightOffset"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  HeightOffset  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static GatherResourcesOre DeserializeGatherResourcesOre(JSONNode _json)
        {
            return new units.GatherResourcesOre(false, _json);
        }

        /// <summary>
        /// ID
        /// </summary>
        public long Id { get; private set; }
        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name
        {
            get
            {
                MgrTables.Log.Error("Please use Name_l10n_index instead of Name. Table: GatherResourcesOre");
                return string.Empty;
            }
        }
        public int Name_l10n_index { get; private set; }
        /// <summary>
        /// 初始生命
        /// </summary>
        public int StartHealth { get; private set; }
        /// <summary>
        /// 应用战斗模块id
        /// </summary>
        public int CombatComponent { get; private set; }
        /// <summary>
        /// 防御类型
        /// </summary>
        public int BaseProtection { get; private set; }
        /// <summary>
        /// 缩模型阶段
        /// </summary>
        public float[] Stages { get; private set; }
        /// <summary>
        /// 采集类型
        /// </summary>
        public string GatherType { get; private set; }
        /// <summary>
        /// 基础采集获得
        /// </summary>
        public int DropId { get; private set; }
        /// <summary>
        /// 获得结束额外采集的DestroyFraction限制
        /// </summary>
        public float MaxDestroyFractionForFinishBonus { get; private set; }
        /// <summary>
        /// 结束额外获得
        /// </summary>
        public int FinishBonusDropId { get; private set; }
        /// <summary>
        /// 渲染预制路径
        /// </summary>
        public string ResPath { get; private set; }
        /// <summary>
        /// 破碎预制路径
        /// </summary>
        public string ShatterPath { get; private set; }
        /// <summary>
        /// 碰撞预制路径
        /// </summary>
        public string ColliderResPath { get; private set; }
        /// <summary>
        /// 是否显示视觉焦点信息
        /// </summary>
        public bool IsShowUI { get; private set; }
        /// <summary>
        /// 视觉焦点信息样式类型
        /// </summary>
        public int UIType { get; private set; }
        /// <summary>
        /// 套用高亮模板ID
        /// </summary>
        public int HighLightId { get; private set; }
        /// <summary>
        /// 圆点高度偏移
        /// </summary>
        public float HeightOffset { get; private set; }

        public const int __ID__ = 72852759;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"Name\":" + Name + ","
            + "\"StartHealth\":" + StartHealth + ","
            + "\"CombatComponent\":" + CombatComponent + ","
            + "\"BaseProtection\":" + BaseProtection + ","
            + "\"Stages\":" + Bright.Common.StringUtil.CollectionToString(Stages) + ","
            + "\"GatherType\":" + $"\"{GatherType}\"" + ","
            + "\"DropId\":" + DropId + ","
            + "\"MaxDestroyFractionForFinishBonus\":" + MaxDestroyFractionForFinishBonus + ","
            + "\"FinishBonusDropId\":" + FinishBonusDropId + ","
            + "\"ResPath\":" + $"\"{ResPath}\"" + ","
            + "\"ShatterPath\":" + $"\"{ShatterPath}\"" + ","
            + "\"ColliderResPath\":" + $"\"{ColliderResPath}\"" + ","
            + "\"IsShowUI\":" + IsShowUI.ToString().ToLower() + ","
            + "\"UIType\":" + UIType + ","
            + "\"HighLightId\":" + HighLightId + ","
            + "\"HeightOffset\":" + HeightOffset
            + "}";

        partial void PostInit();
    }
}

#endif