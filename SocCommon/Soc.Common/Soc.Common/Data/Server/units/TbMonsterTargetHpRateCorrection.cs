//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.units
{
    public sealed partial class TbMonsterTargetHpRateCorrection: BaseTable
    {
        public static int HashCode = 1428638766;

        public IReadOnlyDictionary<int, units.MonsterTargetHpRateCorrection> DataMap => dataMap;
        private Dictionary<int, units.MonsterTargetHpRateCorrection> dataMap = new();
        public IReadOnlyList<units.MonsterTargetHpRateCorrection> DataList => dataList;
        private List<units.MonsterTargetHpRateCorrection> dataList = new();


        public TbMonsterTargetHpRateCorrection(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbMonsterTargetHpRateCorrection error: HashCode not equal, Sheet may be changed!");
            }
            foreach (JSONNode _row in _json["data"].Children)
            {
                try
                {
                    var _v = units.MonsterTargetHpRateCorrection.DeserializeMonsterTargetHpRateCorrection(_row);
                    dataList.Add(_v);
                    dataMap.Add(_v.Id, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TbMonsterTargetHpRateCorrection error: " + ex.Message);
                }
            }
            enumer = dataMap.Values;
            PostInit();
        }

        public units.MonsterTargetHpRateCorrection GetOrDefault(int key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            int Id = default;
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }

            if (dataMap.ContainsKey(Id))
            {
                var _v = dataMap[Id];
                _v.Update(_json);
            }
            else
            {
                var _v = units.MonsterTargetHpRateCorrection.DeserializeMonsterTargetHpRateCorrection(_json);
                dataList.Add(_v);
                dataMap.Add(_v.Id, _v);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();

        public override Bright.Config.BeanBase GetByKey(long key) => dataMap.TryGetValue((int)key, out var ret) ? ret : default;
    }
}

#endif