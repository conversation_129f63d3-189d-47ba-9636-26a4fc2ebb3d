//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.units
{
    public sealed partial class MonumentTemplateID : Bright.Config.BeanBase 
    {
        public MonumentTemplateID() { }

        public MonumentTemplateID(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("templateID")) { var _j = _json["templateID"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  TemplateID  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("prefabPath")) { var _j = _json["prefabPath"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  PrefabPath  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("sinkFxId")) { var _j = _json["sinkFxId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SinkFxId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("monumentName")) { var _j = _json["monumentName"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  MonumentName  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("floatAttri")) { var _j = _json["floatAttri"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  FloatAttri  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static MonumentTemplateID DeserializeMonumentTemplateID(JSONNode _json)
        {
            return new units.MonumentTemplateID(false, _json);
        }

        /// <summary>
        /// 模组id
        /// </summary>
        public long TemplateID { get; private set; }
        /// <summary>
        /// 预制体路径
        /// </summary>
        public string PrefabPath { get; private set; }
        /// <summary>
        /// 下沉特效id
        /// </summary>
        public int SinkFxId { get; private set; }
        /// <summary>
        /// 遗迹名
        /// </summary>
        public string MonumentName { get; private set; }
        /// <summary>
        /// 浮力属性
        /// </summary>
        public int FloatAttri { get; private set; }

        public const int __ID__ = -643896517;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"TemplateID\":" + TemplateID + ","
            + "\"PrefabPath\":" + $"\"{PrefabPath}\"" + ","
            + "\"SinkFxId\":" + SinkFxId + ","
            + "\"MonumentName\":" + $"\"{MonumentName}\"" + ","
            + "\"FloatAttri\":" + FloatAttri
            + "}";

        partial void PostInit();
    }
}

#endif