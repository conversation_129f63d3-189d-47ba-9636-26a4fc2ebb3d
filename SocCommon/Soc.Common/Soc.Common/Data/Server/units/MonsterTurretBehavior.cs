//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.units
{
    public sealed partial class MonsterTurretBehavior : Bright.Config.BeanBase 
    {
        public MonsterTurretBehavior() { }

        public MonsterTurretBehavior(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("behaviorTreeId")) { var _j = _json["behaviorTreeId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  BehaviorTreeId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("useAnimator")) { var _j = _json["useAnimator"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  UseAnimator  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("peaceModeCamp")) { var _j = _json["peaceModeCamp"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PeaceModeCamp  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("standardModeCamp")) { var _j = _json["standardModeCamp"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  StandardModeCamp  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("turretChangeTargetInterval")) { var _j = _json["turretChangeTargetInterval"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; TurretChangeTargetInterval = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  TurretChangeTargetInterval[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("turretScanAngle")) { var _j = _json["turretScanAngle"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; TurretScanAngle = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  TurretScanAngle[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("turretScanInterval")) { var _j = _json["turretScanInterval"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; TurretScanInterval = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  TurretScanInterval[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("turretScanActionInterval")) { var _j = _json["turretScanActionInterval"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; TurretScanActionInterval = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  TurretScanActionInterval[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("turretIdleTime")) { var _j = _json["turretIdleTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; TurretIdleTime = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  TurretIdleTime[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("idleAudioInterval")) { var _j = _json["idleAudioInterval"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; IdleAudioInterval = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  IdleAudioInterval[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("ClientRotateLerpScale")) { var _j = _json["ClientRotateLerpScale"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ClientRotateLerpScale  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("turretIdleAngleSpeed")) { var _j = _json["turretIdleAngleSpeed"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; TurretIdleAngleSpeed = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  TurretIdleAngleSpeed[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("turretTrackAngleSpeed")) { var _j = _json["turretTrackAngleSpeed"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  TurretTrackAngleSpeed  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("turretTurnOnAngleSpeed")) { var _j = _json["turretTurnOnAngleSpeed"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  TurretTurnOnAngleSpeed  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("turretTurnOffAngleSpeed")) { var _j = _json["turretTurnOffAngleSpeed"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  TurretTurnOffAngleSpeed  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("canShoot")) { var _j = _json["canShoot"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { CanShoot = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ShootAttackDamage")) { var _j = _json["ShootAttackDamage"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ShootAttackDamage  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("useRangedWeapon")) { var _j = _json["useRangedWeapon"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  UseRangedWeapon  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("launchShootAttackRange")) { var _j = _json["launchShootAttackRange"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; LaunchShootAttackRange = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  LaunchShootAttackRange[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("warningShootBulletHitRate")) { var _j = _json["warningShootBulletHitRate"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; WarningShootBulletHitRate = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  WarningShootBulletHitRate[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("warningShootCD")) { var _j = _json["warningShootCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; WarningShootCD = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  WarningShootCD[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("warningShootBulletNum")) { var _j = _json["warningShootBulletNum"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; WarningShootBulletNum = new int[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { int __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  WarningShootBulletNum[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("atkAimTime")) { var _j = _json["atkAimTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AtkAimTime  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("atkSustainTime")) { var _j = _json["atkSustainTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; AtkSustainTime = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  AtkSustainTime[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("AtkTimeInterval")) { var _j = _json["AtkTimeInterval"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; AtkTimeInterval = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  AtkTimeInterval[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("HitRate")) { var _j = _json["HitRate"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; HitRate = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  HitRate[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("HitRange")) { var _j = _json["HitRange"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; HitRange = new float[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { float __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  HitRange[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("targetHpRate")) { var _j = _json["targetHpRate"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  TargetHpRate  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("minHitRate")) { var _j = _json["minHitRate"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MinHitRate  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("standEyeHeight")) { var _j = _json["standEyeHeight"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  StandEyeHeight  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("crouchEyeHeight")) { var _j = _json["crouchEyeHeight"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  CrouchEyeHeight  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("standBodyHeight")) { var _j = _json["standBodyHeight"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  StandBodyHeight  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("crouchBodyHeight")) { var _j = _json["crouchBodyHeight"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  CrouchBodyHeight  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static MonsterTurretBehavior DeserializeMonsterTurretBehavior(JSONNode _json)
        {
            return new units.MonsterTurretBehavior(false, _json);
        }

        /// <summary>
        /// 特殊适用范围：
        /// </summary>
        public int Id { get; private set; }
        public long BehaviorTreeId { get; private set; }
        public string UseAnimator { get; private set; }
        public int PeaceModeCamp { get; private set; }
        public int StandardModeCamp { get; private set; }
        public float[] TurretChangeTargetInterval { get; private set; }
        public float[] TurretScanAngle { get; private set; }
        public float[] TurretScanInterval { get; private set; }
        public float[] TurretScanActionInterval { get; private set; }
        public float[] TurretIdleTime { get; private set; }
        public float[] IdleAudioInterval { get; private set; }
        public float ClientRotateLerpScale { get; private set; }
        public float[] TurretIdleAngleSpeed { get; private set; }
        public float TurretTrackAngleSpeed { get; private set; }
        public float TurretTurnOnAngleSpeed { get; private set; }
        public float TurretTurnOffAngleSpeed { get; private set; }
        public bool CanShoot { get; private set; }
        public float ShootAttackDamage { get; private set; }
        public int UseRangedWeapon { get; private set; }
        public float[] LaunchShootAttackRange { get; private set; }
        public float[] WarningShootBulletHitRate { get; private set; }
        public float[] WarningShootCD { get; private set; }
        public int[] WarningShootBulletNum { get; private set; }
        public float AtkAimTime { get; private set; }
        public float[] AtkSustainTime { get; private set; }
        public float[] AtkTimeInterval { get; private set; }
        public float[] HitRate { get; private set; }
        public float[] HitRange { get; private set; }
        public int TargetHpRate { get; private set; }
        public float MinHitRate { get; private set; }
        public float StandEyeHeight { get; private set; }
        public float CrouchEyeHeight { get; private set; }
        public float StandBodyHeight { get; private set; }
        public float CrouchBodyHeight { get; private set; }

        public const int __ID__ = 585713789;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"BehaviorTreeId\":" + BehaviorTreeId + ","
            + "\"UseAnimator\":" + $"\"{UseAnimator}\"" + ","
            + "\"PeaceModeCamp\":" + PeaceModeCamp + ","
            + "\"StandardModeCamp\":" + StandardModeCamp + ","
            + "\"TurretChangeTargetInterval\":" + Bright.Common.StringUtil.CollectionToString(TurretChangeTargetInterval) + ","
            + "\"TurretScanAngle\":" + Bright.Common.StringUtil.CollectionToString(TurretScanAngle) + ","
            + "\"TurretScanInterval\":" + Bright.Common.StringUtil.CollectionToString(TurretScanInterval) + ","
            + "\"TurretScanActionInterval\":" + Bright.Common.StringUtil.CollectionToString(TurretScanActionInterval) + ","
            + "\"TurretIdleTime\":" + Bright.Common.StringUtil.CollectionToString(TurretIdleTime) + ","
            + "\"IdleAudioInterval\":" + Bright.Common.StringUtil.CollectionToString(IdleAudioInterval) + ","
            + "\"ClientRotateLerpScale\":" + ClientRotateLerpScale + ","
            + "\"TurretIdleAngleSpeed\":" + Bright.Common.StringUtil.CollectionToString(TurretIdleAngleSpeed) + ","
            + "\"TurretTrackAngleSpeed\":" + TurretTrackAngleSpeed + ","
            + "\"TurretTurnOnAngleSpeed\":" + TurretTurnOnAngleSpeed + ","
            + "\"TurretTurnOffAngleSpeed\":" + TurretTurnOffAngleSpeed + ","
            + "\"CanShoot\":" + CanShoot.ToString().ToLower() + ","
            + "\"ShootAttackDamage\":" + ShootAttackDamage + ","
            + "\"UseRangedWeapon\":" + UseRangedWeapon + ","
            + "\"LaunchShootAttackRange\":" + Bright.Common.StringUtil.CollectionToString(LaunchShootAttackRange) + ","
            + "\"WarningShootBulletHitRate\":" + Bright.Common.StringUtil.CollectionToString(WarningShootBulletHitRate) + ","
            + "\"WarningShootCD\":" + Bright.Common.StringUtil.CollectionToString(WarningShootCD) + ","
            + "\"WarningShootBulletNum\":" + Bright.Common.StringUtil.CollectionToString(WarningShootBulletNum) + ","
            + "\"AtkAimTime\":" + AtkAimTime + ","
            + "\"AtkSustainTime\":" + Bright.Common.StringUtil.CollectionToString(AtkSustainTime) + ","
            + "\"AtkTimeInterval\":" + Bright.Common.StringUtil.CollectionToString(AtkTimeInterval) + ","
            + "\"HitRate\":" + Bright.Common.StringUtil.CollectionToString(HitRate) + ","
            + "\"HitRange\":" + Bright.Common.StringUtil.CollectionToString(HitRange) + ","
            + "\"TargetHpRate\":" + TargetHpRate + ","
            + "\"MinHitRate\":" + MinHitRate + ","
            + "\"StandEyeHeight\":" + StandEyeHeight + ","
            + "\"CrouchEyeHeight\":" + CrouchEyeHeight + ","
            + "\"StandBodyHeight\":" + StandBodyHeight + ","
            + "\"CrouchBodyHeight\":" + CrouchBodyHeight
            + "}";

        partial void PostInit();
    }
}

#endif