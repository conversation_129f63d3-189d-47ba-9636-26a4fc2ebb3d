//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.constraction
{
    public sealed partial class Door : Bright.Config.BeanBase 
    {
        public Door() { }

        public Door(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("Id")) { var _j = _json["Id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("name")) { var _j = _json["name"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Name  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static Door DeserializeDoor(JSONNode _json)
        {
            return new constraction.Door(false, _json);
        }

        /// <summary>
        /// 皮肤
        /// </summary>
        public long Id { get; private set; }
        /// <summary>
        /// 皮肤备注
        /// </summary>
        public string Name { get; private set; }

        public const int __ID__ = -286153801;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"Name\":" + $"\"{Name}\""
            + "}";

        partial void PostInit();
    }
}

#endif