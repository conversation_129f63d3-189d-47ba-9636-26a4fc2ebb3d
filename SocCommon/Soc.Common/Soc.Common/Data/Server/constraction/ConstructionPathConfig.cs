//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.constraction
{
    public sealed partial class ConstructionPathConfig : Bright.Config.BeanBase 
    {
        public ConstructionPathConfig() { }

        public ConstructionPathConfig(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("PathId")) { var _j = _json["PathId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PathId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("Path")) { var _j = _json["Path"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Path  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static ConstructionPathConfig DeserializeConstructionPathConfig(JSONNode _json)
        {
            return new constraction.ConstructionPathConfig(false, _json);
        }

        /// <summary>
        /// 路径ID
        /// </summary>
        public long PathId { get; private set; }
        /// <summary>
        /// 资源路径
        /// </summary>
        public string Path { get; private set; }

        public const int __ID__ = 874524481;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"PathId\":" + PathId + ","
            + "\"Path\":" + $"\"{Path}\""
            + "}";

        partial void PostInit();
    }
}

#endif