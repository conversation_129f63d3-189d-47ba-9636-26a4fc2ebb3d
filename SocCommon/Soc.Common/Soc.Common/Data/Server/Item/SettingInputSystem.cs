//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Item
{
    public sealed partial class SettingInputSystem : Bright.Config.BeanBase 
    {
        public SettingInputSystem() { }

        public SettingInputSystem(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("actionName")) { var _j = _json["actionName"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ActionName  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("actionNameEnum")) { var _j = _json["actionNameEnum"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ActionNameEnum  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("bindingIndex")) { var _j = _json["bindingIndex"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  BindingIndex  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("bindingName")) { var _j = _json["bindingName"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BindingName  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("title")) { var _j = _json["title"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Title_l10n_index = _j["index"]; } else { throw new SerializationException("Title.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("des")) { var _j = _json["des"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Des_l10n_index = _j["index"]; } else { throw new SerializationException("Des.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("firstTag")) { var _j = _json["firstTag"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  FirstTag  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("secondTag")) { var _j = _json["secondTag"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SecondTag  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("supportComboKey")) { var _j = _json["supportComboKey"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { SupportComboKey = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("supportRebind")) { var _j = _json["supportRebind"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { SupportRebind = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("isImportant")) { var _j = _json["isImportant"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { IsImportant = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("relatedActionNames")) { var _j = _json["relatedActionNames"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { RelatedActionNames = new System.Collections.Generic.List<ActionName>(__json0.Count); foreach(JSONNode __e0 in __json0.Children) { ActionName __v0= default;  if(__e0.IsNumber) { __v0 = (ActionName)__e0.AsInt; } else { throw new SerializationException(); }  RelatedActionNames.Add(__v0); } } else { throw new SerializationException(); } } } } }
            PostInit();
        }

        public static SettingInputSystem DeserializeSettingInputSystem(JSONNode _json)
        {
            return new Item.SettingInputSystem(false, _json);
        }

        /// <summary>
        /// 配置ID
        /// </summary>
        public int Id { get; private set; }
        /// <summary>
        /// 动作名称Key
        /// </summary>
        public string ActionName { get; private set; }
        /// <summary>
        /// 程序动作枚举
        /// </summary>
        public string ActionNameEnum { get; private set; }
        /// <summary>
        /// 绑定序号
        /// </summary>
        public int BindingIndex { get; private set; }
        /// <summary>
        /// 动作绑定name
        /// </summary>
        public string BindingName { get; private set; }
        /// <summary>
        /// 动作标题
        /// </summary>
        public string Title
        {
            get
            {
                MgrTables.Log.Error("Please use Title_l10n_index instead of Title. Table: SettingInputSystem");
                return string.Empty;
            }
        }
        public int Title_l10n_index { get; private set; }
        /// <summary>
        /// 动作描述
        /// </summary>
        public string Des
        {
            get
            {
                MgrTables.Log.Error("Please use Des_l10n_index instead of Des. Table: SettingInputSystem");
                return string.Empty;
            }
        }
        public int Des_l10n_index { get; private set; }
        /// <summary>
        /// 一级页签
        /// </summary>
        public int FirstTag { get; private set; }
        /// <summary>
        /// 二级页签
        /// </summary>
        public int SecondTag { get; private set; }
        /// <summary>
        /// 是否支持组合键
        /// </summary>
        public bool SupportComboKey { get; private set; }
        /// <summary>
        /// 是否支持改键
        /// </summary>
        public bool SupportRebind { get; private set; }
        /// <summary>
        /// 是否是重要按键
        /// </summary>
        public bool IsImportant { get; private set; }
        /// <summary>
        /// 关联的动作绑定
        /// </summary>
        public System.Collections.Generic.List<ActionName> RelatedActionNames { get; private set; }

        public const int __ID__ = -1348243228;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"ActionName\":" + $"\"{ActionName}\"" + ","
            + "\"ActionNameEnum\":" + $"\"{ActionNameEnum}\"" + ","
            + "\"BindingIndex\":" + BindingIndex + ","
            + "\"BindingName\":" + $"\"{BindingName}\"" + ","
            + "\"Title\":" + Title + ","
            + "\"Des\":" + Des + ","
            + "\"FirstTag\":" + FirstTag + ","
            + "\"SecondTag\":" + SecondTag + ","
            + "\"SupportComboKey\":" + SupportComboKey.ToString().ToLower() + ","
            + "\"SupportRebind\":" + SupportRebind.ToString().ToLower() + ","
            + "\"IsImportant\":" + IsImportant.ToString().ToLower() + ","
            + "\"RelatedActionNames\":" + Bright.Common.StringUtil.CollectionToString(RelatedActionNames)
            + "}";

        partial void PostInit();
    }
}

#endif