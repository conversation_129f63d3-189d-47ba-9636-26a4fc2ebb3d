//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.monument
{
    public sealed partial class TbBoyBandConst: BaseTable, IConfigTable
    {
        public static int HashCode = 1177730320;

        private monument.BoyBandConst _data;

        public Bright.Config.BeanBase Data => _data;

        public TbBoyBandConst(JSONNode _json)
        {
            if (_json["data"].Count != 1)
            {
                throw new SerializationException("Deserialize TbBoyBandConst error: TableMode = one, but json size != 1.");
            }

            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbBoyBandConst error: HashCode not equal, Sheet may be changed!");
            }
            try
            {
                _data = monument.BoyBandConst.DeserializeBoyBandConst(_json["data"][0]);
            }
            catch (SerializationException ex)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbBoyBandConst error: " + ex.Message);
            }
            PostInit();
        }

        /// <summary>
        /// 避免刷脸的判定距离（米）
        /// </summary>
        public int OnlinePlayerSpawnRange => _data.OnlinePlayerSpawnRange;

        public override void Resolve(Dictionary<string, BaseTable> _tables)
        {
            _data?.Resolve(_tables);
            PostResolve();
        }

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            _data.Update(_json);
            TriggerDataUpdateEvent();
        }

        partial void PostInit();

    }
}

#endif