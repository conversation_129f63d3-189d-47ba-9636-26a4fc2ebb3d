//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data
{
    public sealed partial class RadiationData : Bright.Config.BeanBase 
    {
        public RadiationData() { }

        public RadiationData(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("posX")) { var _j = _json["posX"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PosX  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("posY")) { var _j = _json["posY"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PosY  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("posZ")) { var _j = _json["posZ"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PosZ  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("scaleX")) { var _j = _json["scaleX"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ScaleX  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("scaleY")) { var _j = _json["scaleY"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ScaleY  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("scaleZ")) { var _j = _json["scaleZ"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ScaleZ  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("name")) { var _j = _json["name"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Name  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("centerX")) { var _j = _json["centerX"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  CenterX  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("centerY")) { var _j = _json["centerY"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  CenterY  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("centerZ")) { var _j = _json["centerZ"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  CenterZ  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("radius")) { var _j = _json["radius"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Radius  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("radiationTier")) { var _j = _json["radiationTier"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RadiationTier  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("radiationAmountOverride")) { var _j = _json["radiationAmountOverride"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RadiationAmountOverride  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("falloff")) { var _j = _json["falloff"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Falloff  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static RadiationData DeserializeRadiationData(JSONNode _json)
        {
            return new RadiationData(false, _json);
        }

        /// <summary>
        /// 相对位置坐标
        /// </summary>
        public float PosX { get; private set; }
        /// <summary>
        /// 相对位置坐标
        /// </summary>
        public float PosY { get; private set; }
        /// <summary>
        /// 相对位置坐标
        /// </summary>
        public float PosZ { get; private set; }
        /// <summary>
        /// 区域缩放
        /// </summary>
        public float ScaleX { get; private set; }
        /// <summary>
        /// 区域缩放
        /// </summary>
        public float ScaleY { get; private set; }
        /// <summary>
        /// 区域缩放
        /// </summary>
        public float ScaleZ { get; private set; }
        /// <summary>
        /// 辐射区域名称
        /// </summary>
        public string Name { get; private set; }
        /// <summary>
        /// 区域中心位置
        /// </summary>
        public float CenterX { get; private set; }
        /// <summary>
        /// 区域中心位置
        /// </summary>
        public float CenterY { get; private set; }
        /// <summary>
        /// 区域中心位置
        /// </summary>
        public float CenterZ { get; private set; }
        /// <summary>
        /// 辐射区域半径
        /// </summary>
        public float Radius { get; private set; }
        /// <summary>
        /// 辐射强度
        /// </summary>
        public int RadiationTier { get; private set; }
        /// <summary>
        /// 辐射重载强度值
        /// </summary>
        public float RadiationAmountOverride { get; private set; }
        /// <summary>
        /// 辐射衰减度
        /// </summary>
        public float Falloff { get; private set; }

        public const int __ID__ = 1639584683;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"PosX\":" + PosX + ","
            + "\"PosY\":" + PosY + ","
            + "\"PosZ\":" + PosZ + ","
            + "\"ScaleX\":" + ScaleX + ","
            + "\"ScaleY\":" + ScaleY + ","
            + "\"ScaleZ\":" + ScaleZ + ","
            + "\"Name\":" + $"\"{Name}\"" + ","
            + "\"CenterX\":" + CenterX + ","
            + "\"CenterY\":" + CenterY + ","
            + "\"CenterZ\":" + CenterZ + ","
            + "\"Radius\":" + Radius + ","
            + "\"RadiationTier\":" + RadiationTier + ","
            + "\"RadiationAmountOverride\":" + RadiationAmountOverride + ","
            + "\"Falloff\":" + Falloff
            + "}";

        partial void PostInit();
    }
}

#endif