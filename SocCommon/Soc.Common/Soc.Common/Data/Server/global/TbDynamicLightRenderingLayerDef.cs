//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.global
{
    public sealed partial class TbDynamicLightRenderingLayerDef: BaseTable
    {
        public static int HashCode = 1312631801;

        public IReadOnlyDictionary<int, global.DynamicLightRenderingLayerDef> DataMap => dataMap;
        private Dictionary<int, global.DynamicLightRenderingLayerDef> dataMap = new();
        public IReadOnlyList<global.DynamicLightRenderingLayerDef> DataList => dataList;
        private List<global.DynamicLightRenderingLayerDef> dataList = new();


        public TbDynamicLightRenderingLayerDef(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbDynamicLightRenderingLayerDef error: HashCode not equal, Sheet may be changed!");
            }
            foreach (JSONNode _row in _json["data"].Children)
            {
                try
                {
                    var _v = global.DynamicLightRenderingLayerDef.DeserializeDynamicLightRenderingLayerDef(_row);
                    dataList.Add(_v);
                    dataMap.Add(_v.RenderingLayerID, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TbDynamicLightRenderingLayerDef error: " + ex.Message);
                }
            }
            enumer = dataMap.Values;
            PostInit();
        }

        public global.DynamicLightRenderingLayerDef GetOrDefault(int key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            int RenderingLayerID = default;
            { if (_json.HasKey("renderingLayerID")) { var _j = _json["renderingLayerID"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RenderingLayerID  =  _j ; } else { throw new SerializationException(); } } } }

            if (dataMap.ContainsKey(RenderingLayerID))
            {
                var _v = dataMap[RenderingLayerID];
                _v.Update(_json);
            }
            else
            {
                var _v = global.DynamicLightRenderingLayerDef.DeserializeDynamicLightRenderingLayerDef(_json);
                dataList.Add(_v);
                dataMap.Add(_v.RenderingLayerID, _v);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();

        public override Bright.Config.BeanBase GetByKey(long key) => dataMap.TryGetValue((int)key, out var ret) ? ret : default;
    }
}

#endif