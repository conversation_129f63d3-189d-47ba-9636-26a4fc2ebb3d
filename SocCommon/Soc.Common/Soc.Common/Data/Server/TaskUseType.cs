//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

namespace WizardGames.Soc.Common.Data
{
    public enum TaskUseType
    {
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        All = 0,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        Make = 1,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        Trade = 2,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        Build = 3,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        TechTree = 4,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        Use = 5,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        Throw = 6,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        Submit = 7,
        /// <summary>
        /// 任务的消耗途径枚举
        /// </summary>
        PlantingBox = 8,
    }
}

#endif