//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.defence
{
    public sealed partial class RelationFixInfo : Bright.Config.BeanBase 
    {
        public RelationFixInfo() { }

        public RelationFixInfo(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("Id")) { var _j = _json["Id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("relation")) { var _j = _json["relation"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Relation  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DefId")) { var _j = _json["DefId"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  DefId  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("EffectSound")) { var _j = _json["EffectSound"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { EffectSound = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("EffectHit")) { var _j = _json["EffectHit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { EffectHit = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("EffectBleed")) { var _j = _json["EffectBleed"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { EffectBleed = _j; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("EffectShake")) { var _j = _json["EffectShake"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsBoolean) { EffectShake = _j; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static RelationFixInfo DeserializeRelationFixInfo(JSONNode _json)
        {
            return new defence.RelationFixInfo(false, _json);
        }

        /// <summary>
        /// id
        /// </summary>
        public int Id { get; private set; }
        /// <summary>
        /// 关系类型
        /// </summary>
        public string Relation { get; private set; }
        /// <summary>
        /// 防御修正id
        /// </summary>
        public int DefId { get; private set; }
        /// <summary>
        /// 本地音效
        /// </summary>
        public bool EffectSound { get; private set; }
        /// <summary>
        /// 准星反馈
        /// </summary>
        public bool EffectHit { get; private set; }
        /// <summary>
        /// 命中流血
        /// </summary>
        public bool EffectBleed { get; private set; }
        /// <summary>
        /// 相机运动(非爆炸)
        /// </summary>
        public bool EffectShake { get; private set; }

        public const int __ID__ = 1781825289;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"Relation\":" + $"\"{Relation}\"" + ","
            + "\"DefId\":" + DefId + ","
            + "\"EffectSound\":" + EffectSound.ToString().ToLower() + ","
            + "\"EffectHit\":" + EffectHit.ToString().ToLower() + ","
            + "\"EffectBleed\":" + EffectBleed.ToString().ToLower() + ","
            + "\"EffectShake\":" + EffectShake.ToString().ToLower()
            + "}";

        partial void PostInit();
    }
}

#endif