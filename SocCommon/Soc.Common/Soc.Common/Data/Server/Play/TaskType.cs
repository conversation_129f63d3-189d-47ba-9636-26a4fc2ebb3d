//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

namespace WizardGames.Soc.Common.Data.Play
{
    public enum TaskType
    {
        /// <summary>
        /// 失去全部领地柜
        /// </summary>
        LoseAllToolCupboards = 1,
        /// <summary>
        /// 双方阵营到达上限
        /// </summary>
        CampLimit = 2,
        /// <summary>
        /// 队长开始
        /// </summary>
        LeaderStart = 3,
        /// <summary>
        /// 队长结束
        /// </summary>
        LeaderStop = 4,
    }
}

#endif