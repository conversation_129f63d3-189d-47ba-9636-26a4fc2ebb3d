//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Play
{
    public sealed partial class StoryStageDescription : Bright.Config.BeanBase 
    {
        public StoryStageDescription() { }

        public StoryStageDescription(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("description")) { var _j = _json["description"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Description_l10n_index = _j["index"]; } else { throw new SerializationException("Description.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("tag")) { var _j = _json["tag"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Tag_l10n_index = _j["index"]; } else { throw new SerializationException("Tag.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("type")) { var _j = _json["type"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Type  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static StoryStageDescription DeserializeStoryStageDescription(JSONNode _json)
        {
            return new Play.StoryStageDescription(false, _json);
        }

        /// <summary>
        /// 描述id（1-N）
        /// </summary>
        public int Id { get; private set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description
        {
            get
            {
                MgrTables.Log.Error("Please use Description_l10n_index instead of Description. Table: StoryStageDescription");
                return string.Empty;
            }
        }
        public int Description_l10n_index { get; private set; }
        /// <summary>
        /// 标签
        /// </summary>
        public string Tag
        {
            get
            {
                MgrTables.Log.Error("Please use Tag_l10n_index instead of Tag. Table: StoryStageDescription");
                return string.Empty;
            }
        }
        public int Tag_l10n_index { get; private set; }
        /// <summary>
        /// 标签类型
        /// </summary>
        public int Type { get; private set; }

        public const int __ID__ = 1586876921;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"Description\":" + Description + ","
            + "\"Tag\":" + Tag + ","
            + "\"Type\":" + Type
            + "}";

        partial void PostInit();
    }
}

#endif