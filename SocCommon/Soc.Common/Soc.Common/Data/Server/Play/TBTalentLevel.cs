//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Play
{
    public sealed partial class TBTalentLevel: BaseTable
    {
        public static int HashCode = 955430292;

        public IReadOnlyDictionary<(int talentID, int level), Play.OBJTalentLevel> DataMap => dataMapUnion;
        private Dictionary<(int, int), Play.OBJTalentLevel> dataMapUnion;
        public IReadOnlyList<Play.OBJTalentLevel> DataList => dataList;
        private List<Play.OBJTalentLevel> dataList;


        public TBTalentLevel(JSONNode _json)
        {
            dataList = new List<Play.OBJTalentLevel>();

            foreach(JSONNode _row in _json["data"].Children)
            {
                var _v = Play.OBJTalentLevel.DeserializeOBJTalentLevel(_row);
                dataList.Add(_v);
            }
            dataMapUnion = new Dictionary<(int, int), Play.OBJTalentLevel>();
            foreach(var _v in dataList)
            {
                dataMapUnion.Add((_v.TalentID, _v.Level), _v);
            }
            enumer = dataList;
            PostInit();
        }

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            var _v = Play.OBJTalentLevel.DeserializeOBJTalentLevel(_json);

            if (dataMapUnion.TryGetValue((_v.TalentID, _v.Level), out var existing))
            {
                existing.Update(_json);
            }
            else
            {
                dataMapUnion.Add((_v.TalentID, _v.Level), _v);
                dataList.Add(_v);
            }
            TriggerDataUpdateEvent();
        }

        public Play.OBJTalentLevel Get(int talentID, int level) => dataMapUnion.TryGetValue((talentID, level), out Play.OBJTalentLevel __v) ? __v : null;


        partial void PostInit();

    }
}

#endif