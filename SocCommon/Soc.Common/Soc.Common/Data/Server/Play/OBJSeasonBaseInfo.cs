//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Play
{
    public sealed partial class OBJSeasonBaseInfo : Bright.Config.BeanBase 
    {
        public OBJSeasonBaseInfo() { }

        public OBJSeasonBaseInfo(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("seasonID")) { var _j = _json["seasonID"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SeasonID  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("name")) { var _j = _json["name"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Name_l10n_index = _j["index"]; } else { throw new SerializationException("Name.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("startTime")) { var _j = _json["startTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  StartTime  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("endPreviewTime")) { var _j = _json["endPreviewTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  EndPreviewTime  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("endTime")) { var _j = _json["endTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  EndTime  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static OBJSeasonBaseInfo DeserializeOBJSeasonBaseInfo(JSONNode _json)
        {
            return new Play.OBJSeasonBaseInfo(false, _json);
        }

        /// <summary>
        /// 赛季ID
        /// </summary>
        public int SeasonID { get; private set; }
        /// <summary>
        /// 赛季名称
        /// </summary>
        public string Name
        {
            get
            {
                MgrTables.Log.Error("Please use Name_l10n_index instead of Name. Table: OBJSeasonBaseInfo");
                return string.Empty;
            }
        }
        public int Name_l10n_index { get; private set; }
        /// <summary>
        /// 赛季开始时间
        /// </summary>
        public string StartTime { get; private set; }
        /// <summary>
        /// 赛季结束预告时间
        /// </summary>
        public string EndPreviewTime { get; private set; }
        /// <summary>
        /// 赛季结束时间
        /// </summary>
        public string EndTime { get; private set; }

        public const int __ID__ = 200636351;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"SeasonID\":" + SeasonID + ","
            + "\"Name\":" + Name + ","
            + "\"StartTime\":" + $"\"{StartTime}\"" + ","
            + "\"EndPreviewTime\":" + $"\"{EndPreviewTime}\"" + ","
            + "\"EndTime\":" + $"\"{EndTime}\""
            + "}";

        partial void PostInit();
    }
}

#endif