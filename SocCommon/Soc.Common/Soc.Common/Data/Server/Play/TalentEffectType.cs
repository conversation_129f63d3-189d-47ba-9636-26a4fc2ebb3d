//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

namespace WizardGames.Soc.Common.Data.Play
{
    public enum TalentEffectType
    {
        /// <summary>
        /// 无
        /// </summary>
        None = 0,
        /// <summary>
        /// 激活buff
        /// </summary>
        ActivateBuff = 1,
        /// <summary>
        /// 情报站中增加可选奖励
        /// </summary>
        AddReputationSelectReward = 2,
        /// <summary>
        /// 首次出生增加属性值
        /// </summary>
        FirstBornAddProperty = 3,
        /// <summary>
        /// 首次出生发道具
        /// </summary>
        FirstBornAddItem = 4,
        /// <summary>
        /// 解锁官方建筑蓝图
        /// </summary>
        UnlockConstructionBlueprint = 5,
    }
}

#endif