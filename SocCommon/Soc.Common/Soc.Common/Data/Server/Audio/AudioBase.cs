//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Audio
{
    public sealed partial class AudioBase : Bright.Config.BeanBase 
    {
        public AudioBase() { }

        public AudioBase(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("HitEnemy")) { var _j = _json["HitEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  HitEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("HitHeadEnemy")) { var _j = _json["HitHeadEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  HitHeadEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("KillEnemy")) { var _j = _json["KillEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  KillEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("KnockDownEnemy")) { var _j = _json["KnockDownEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  KnockDownEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BeHit")) { var _j = _json["BeHit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BeHit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BeHitHead")) { var _j = _json["BeHitHead"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BeHitHead  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BeKnockDown")) { var _j = _json["BeKnockDown"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BeKnockDown  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BeKill")) { var _j = _json["BeKill"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BeKill  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MeleeHitHead")) { var _j = _json["MeleeHitHead"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  MeleeHitHead  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MeleeBeHit")) { var _j = _json["MeleeBeHit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  MeleeBeHit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MeleeBeHitHead")) { var _j = _json["MeleeBeHitHead"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  MeleeBeHitHead  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("P3MeleeKnockDownEnemy")) { var _j = _json["P3MeleeKnockDownEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  P3MeleeKnockDownEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("P3MeleeKillEnemy")) { var _j = _json["P3MeleeKillEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  P3MeleeKillEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("P3HitEnemy")) { var _j = _json["P3HitEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  P3HitEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("P3KnockDownEnemy")) { var _j = _json["P3KnockDownEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  P3KnockDownEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("P3KillEnemy")) { var _j = _json["P3KillEnemy"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  P3KillEnemy  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("HitTexture")) { var _j = _json["HitTexture"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  HitTexture  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("PlayerDied")) { var _j = _json["PlayerDied"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  PlayerDied  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("UIClick")) { var _j = _json["UIClick"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  UIClick  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("UIWheelEnter")) { var _j = _json["UIWheelEnter"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  UIWheelEnter  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("UIWheelSelect")) { var _j = _json["UIWheelSelect"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  UIWheelSelect  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("UIWheelExit")) { var _j = _json["UIWheelExit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  UIWheelExit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("OreCrash")) { var _j = _json["OreCrash"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  OreCrash  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("OreHitCritical")) { var _j = _json["OreHitCritical"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  OreHitCritical  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("TreeFail")) { var _j = _json["TreeFail"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  TreeFail  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("TreeCrackMed")) { var _j = _json["TreeCrackMed"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  TreeCrackMed  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("TreeCrackSmall")) { var _j = _json["TreeCrackSmall"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  TreeCrackSmall  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("TreeHitMarker")) { var _j = _json["TreeHitMarker"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  TreeHitMarker  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildSuccess")) { var _j = _json["BuildSuccess"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildSuccess  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildWoodCrash")) { var _j = _json["BuildWoodCrash"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildWoodCrash  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildWoodStructure")) { var _j = _json["BuildWoodStructure"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildWoodStructure  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildStoneStructure")) { var _j = _json["BuildStoneStructure"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildStoneStructure  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildStoneCrash")) { var _j = _json["BuildStoneCrash"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildStoneCrash  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeployWorkbench")) { var _j = _json["BuildDeployWorkbench"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeployWorkbench  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildMetalCrash")) { var _j = _json["BuildMetalCrash"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildMetalCrash  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeployStorageBox")) { var _j = _json["BuildDeployStorageBox"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeployStorageBox  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeployCabinet")) { var _j = _json["BuildDeployCabinet"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeployCabinet  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeployWoodDoor")) { var _j = _json["BuildDeployWoodDoor"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeployWoodDoor  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeployMetalDoor")) { var _j = _json["BuildDeployMetalDoor"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeployMetalDoor  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeployBonfire")) { var _j = _json["BuildDeployBonfire"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeployBonfire  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildDeploySmelter")) { var _j = _json["BuildDeploySmelter"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildDeploySmelter  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("OpenSafeBox")) { var _j = _json["OpenSafeBox"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  OpenSafeBox  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DropMetal")) { var _j = _json["DropMetal"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  DropMetal  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BoarIdle")) { var _j = _json["BoarIdle"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BoarIdle  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BearIdle")) { var _j = _json["BearIdle"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BearIdle  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ChickenIdle")) { var _j = _json["ChickenIdle"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ChickenIdle  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ScientistIdle")) { var _j = _json["ScientistIdle"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ScientistIdle  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AirDropPlaneFly")) { var _j = _json["AirDropPlaneFly"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AirDropPlaneFly  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("OpenRepairBench")) { var _j = _json["OpenRepairBench"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  OpenRepairBench  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("CloseRepairBench")) { var _j = _json["CloseRepairBench"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  CloseRepairBench  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("RepairBenchRepair")) { var _j = _json["RepairBenchRepair"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  RepairBenchRepair  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("FlySwarm")) { var _j = _json["FlySwarm"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  FlySwarm  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("OpenModerator")) { var _j = _json["OpenModerator"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  OpenModerator  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("CloseModerator")) { var _j = _json["CloseModerator"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  CloseModerator  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("StartModerator")) { var _j = _json["StartModerator"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  StartModerator  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("EndModerator")) { var _j = _json["EndModerator"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  EndModerator  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretEquipWeapon")) { var _j = _json["AutoTurretEquipWeapon"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretEquipWeapon  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretChangeTarget")) { var _j = _json["AutoTurretChangeTarget"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretChangeTarget  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretHasTarget")) { var _j = _json["AutoTurretHasTarget"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretHasTarget  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretTargetLost")) { var _j = _json["AutoTurretTargetLost"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretTargetLost  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretRotate")) { var _j = _json["AutoTurretRotate"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretRotate  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("Switch")) { var _j = _json["Switch"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Switch  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("CeilingLightTurnOn")) { var _j = _json["CeilingLightTurnOn"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  CeilingLightTurnOn  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("CeilingLightTurnOff")) { var _j = _json["CeilingLightTurnOff"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  CeilingLightTurnOff  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WindGenerateLoop")) { var _j = _json["WindGenerateLoop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WindGenerateLoop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretTurnOn")) { var _j = _json["AutoTurretTurnOn"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretTurnOn  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretTurnOff")) { var _j = _json["AutoTurretTurnOff"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretTurnOff  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretMovementLoop")) { var _j = _json["AutoTurretMovementLoop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretMovementLoop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretTargetAcquired")) { var _j = _json["AutoTurretTargetAcquired"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretTargetAcquired  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretReload")) { var _j = _json["AutoTurretReload"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretReload  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoTurretAuthorize")) { var _j = _json["AutoTurretAuthorize"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  AutoTurretAuthorize  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DoorMetalOpen")) { var _j = _json["DoorMetalOpen"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  DoorMetalOpen  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DoorMetalClose")) { var _j = _json["DoorMetalClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  DoorMetalClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DoorWoodOpen")) { var _j = _json["DoorWoodOpen"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  DoorWoodOpen  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DoorWoodClose")) { var _j = _json["DoorWoodClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  DoorWoodClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ToolCupboardOpen")) { var _j = _json["ToolCupboardOpen"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ToolCupboardOpen  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ToolCupboardClose")) { var _j = _json["ToolCupboardClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ToolCupboardClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WoodBoxLargeOpen")) { var _j = _json["WoodBoxLargeOpen"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WoodBoxLargeOpen  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WoodBoxLargeClose")) { var _j = _json["WoodBoxLargeClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WoodBoxLargeClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("Svl_Build_Deploy_WoodLadder")) { var _j = _json["Svl_Build_Deploy_WoodLadder"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SvlBuildDeployWoodLadder  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WaterBottle_Splash")) { var _j = _json["WaterBottle_Splash"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WaterBottleSplash  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WaterBucket_Splash")) { var _j = _json["WaterBucket_Splash"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WaterBucketSplash  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("Ricochet")) { var _j = _json["Ricochet"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Ricochet  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SmallGeneratorStart")) { var _j = _json["SmallGeneratorStart"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SmallGeneratorStart  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SmallGeneratorStop")) { var _j = _json["SmallGeneratorStop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SmallGeneratorStop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DamageOnWoodenFloorSpikes")) { var _j = _json["DamageOnWoodenFloorSpikes"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  DamageOnWoodenFloorSpikes  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("TankEngine")) { var _j = _json["TankEngine"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  TankEngine  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SearchLightOpen")) { var _j = _json["SearchLightOpen"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SearchLightOpen  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SearchLightClose")) { var _j = _json["SearchLightClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SearchLightClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SearchLightTurnOn")) { var _j = _json["SearchLightTurnOn"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SearchLightTurnOn  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SearchLightTurnOff")) { var _j = _json["SearchLightTurnOff"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SearchLightTurnOff  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("HorseDeath")) { var _j = _json["HorseDeath"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  HorseDeath  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WaterPumpStart")) { var _j = _json["WaterPumpStart"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WaterPumpStart  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WaterPumpStop")) { var _j = _json["WaterPumpStop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  WaterPumpStop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("PowerWaterPurifierStart")) { var _j = _json["PowerWaterPurifierStart"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  PowerWaterPurifierStart  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("PowerWaterPurifierStop")) { var _j = _json["PowerWaterPurifierStop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  PowerWaterPurifierStop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("FluifSwitchActiveLoop")) { var _j = _json["FluifSwitchActiveLoop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  FluifSwitchActiveLoop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SprinklerLoop")) { var _j = _json["SprinklerLoop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SprinklerLoop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SearchLightMove")) { var _j = _json["SearchLightMove"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SearchLightMove  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("PressureSensorOpen")) { var _j = _json["PressureSensorOpen"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  PressureSensorOpen  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("PressureSensorClose")) { var _j = _json["PressureSensorClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  PressureSensorClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ElevatorButton")) { var _j = _json["ElevatorButton"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ElevatorButton  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ElevatorMoveStart")) { var _j = _json["ElevatorMoveStart"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ElevatorMoveStart  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ElevatorMoveStop")) { var _j = _json["ElevatorMoveStop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ElevatorMoveStop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("HelicopterExplo")) { var _j = _json["HelicopterExplo"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  HelicopterExplo  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("NPCTurretIdle")) { var _j = _json["NPCTurretIdle"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  NPCTurretIdle  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("NPCTurretHasTarget")) { var _j = _json["NPCTurretHasTarget"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  NPCTurretHasTarget  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("NPCTurretLoseTarget")) { var _j = _json["NPCTurretLoseTarget"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  NPCTurretLoseTarget  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("NPCTurretFire")) { var _j = _json["NPCTurretFire"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  NPCTurretFire  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SamSiteMovement")) { var _j = _json["SamSiteMovement"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SamSiteMovement  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SamSiteTurnMovement")) { var _j = _json["SamSiteTurnMovement"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SamSiteTurnMovement  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("FluidicSwitchOepnClose")) { var _j = _json["FluidicSwitchOepnClose"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  FluidicSwitchOepnClose  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("HelicopterFallDown")) { var _j = _json["HelicopterFallDown"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  HelicopterFallDown  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BradleyExplo")) { var _j = _json["BradleyExplo"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BradleyExplo  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SirenLight")) { var _j = _json["SirenLight"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  SirenLight  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("VehiclePartsDismount")) { var _j = _json["VehiclePartsDismount"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  VehiclePartsDismount  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("NewbieHelicopterSlip")) { var _j = _json["NewbieHelicopterSlip"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  NewbieHelicopterSlip  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("NewbieTankMove")) { var _j = _json["NewbieTankMove"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  NewbieTankMove  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ElectricTimerLoop")) { var _j = _json["ElectricTimerLoop"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ElectricTimerLoop  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BuildWoodBlueprintStructure")) { var _j = _json["BuildWoodBlueprintStructure"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  BuildWoodBlueprintStructure  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static AudioBase DeserializeAudioBase(JSONNode _json)
        {
            return new Audio.AudioBase(false, _json);
        }

        /// <summary>
        /// 命中敌人
        /// </summary>
        public string HitEnemy { get; private set; }
        /// <summary>
        /// 爆头敌人
        /// </summary>
        public string HitHeadEnemy { get; private set; }
        /// <summary>
        /// 击杀敌人
        /// </summary>
        public string KillEnemy { get; private set; }
        /// <summary>
        /// 击倒敌人
        /// </summary>
        public string KnockDownEnemy { get; private set; }
        /// <summary>
        /// 被击中
        /// </summary>
        public string BeHit { get; private set; }
        /// <summary>
        /// 被爆头
        /// </summary>
        public string BeHitHead { get; private set; }
        /// <summary>
        /// 被击倒
        /// </summary>
        public string BeKnockDown { get; private set; }
        /// <summary>
        /// 被击杀
        /// </summary>
        public string BeKill { get; private set; }
        /// <summary>
        /// 近战武器命中头部
        /// </summary>
        public string MeleeHitHead { get; private set; }
        /// <summary>
        /// 近战武器被击中
        /// </summary>
        public string MeleeBeHit { get; private set; }
        /// <summary>
        /// 近战武器被爆头
        /// </summary>
        public string MeleeBeHitHead { get; private set; }
        /// <summary>
        /// P3近战武器击倒
        /// </summary>
        public string P3MeleeKnockDownEnemy { get; private set; }
        /// <summary>
        /// P3近战武器击杀
        /// </summary>
        public string P3MeleeKillEnemy { get; private set; }
        /// <summary>
        /// P3击中敌人
        /// </summary>
        public string P3HitEnemy { get; private set; }
        /// <summary>
        /// P3击倒敌人
        /// </summary>
        public string P3KnockDownEnemy { get; private set; }
        /// <summary>
        /// P3击杀敌人
        /// </summary>
        public string P3KillEnemy { get; private set; }
        /// <summary>
        /// 命中不同物理材质
        /// </summary>
        public string HitTexture { get; private set; }
        /// <summary>
        /// 玩家死亡
        /// </summary>
        public string PlayerDied { get; private set; }
        /// <summary>
        /// UI点击
        /// </summary>
        public string UIClick { get; private set; }
        /// <summary>
        /// 进入转盘
        /// </summary>
        public string UIWheelEnter { get; private set; }
        /// <summary>
        /// 转盘选择
        /// </summary>
        public string UIWheelSelect { get; private set; }
        /// <summary>
        /// 退出转盘
        /// </summary>
        public string UIWheelExit { get; private set; }
        /// <summary>
        /// 矿石碎裂
        /// </summary>
        public string OreCrash { get; private set; }
        /// <summary>
        /// 矿石光点命中
        /// </summary>
        public string OreHitCritical { get; private set; }
        /// <summary>
        /// 树倒下
        /// </summary>
        public string TreeFail { get; private set; }
        /// <summary>
        /// 树中裂缝
        /// </summary>
        public string TreeCrackMed { get; private set; }
        /// <summary>
        /// 树小裂缝
        /// </summary>
        public string TreeCrackSmall { get; private set; }
        /// <summary>
        /// 击中树红叉
        /// </summary>
        public string TreeHitMarker { get; private set; }
        /// <summary>
        /// 建造成功
        /// </summary>
        public string BuildSuccess { get; private set; }
        /// <summary>
        /// 木/草造物破碎
        /// </summary>
        public string BuildWoodCrash { get; private set; }
        /// <summary>
        /// 升级至木质音效
        /// </summary>
        public string BuildWoodStructure { get; private set; }
        /// <summary>
        /// 升级至石质音效
        /// </summary>
        public string BuildStoneStructure { get; private set; }
        /// <summary>
        /// 石质破碎音效
        /// </summary>
        public string BuildStoneCrash { get; private set; }
        /// <summary>
        /// 工作台放置音效
        /// </summary>
        public string BuildDeployWorkbench { get; private set; }
        /// <summary>
        /// 金属摆件破碎音效
        /// </summary>
        public string BuildMetalCrash { get; private set; }
        /// <summary>
        /// 大型储存箱摆放音效
        /// </summary>
        public string BuildDeployStorageBox { get; private set; }
        /// <summary>
        /// 领地柜摆放音效
        /// </summary>
        public string BuildDeployCabinet { get; private set; }
        /// <summary>
        /// 木制门通用放置音效
        /// </summary>
        public string BuildDeployWoodDoor { get; private set; }
        /// <summary>
        /// 铁制门通用放置音效
        /// </summary>
        public string BuildDeployMetalDoor { get; private set; }
        /// <summary>
        /// 火堆放置音效
        /// </summary>
        public string BuildDeployBonfire { get; private set; }
        /// <summary>
        /// 熔炉放置音效
        /// </summary>
        public string BuildDeploySmelter { get; private set; }
        /// <summary>
        /// 保险盒打开音效
        /// </summary>
        public string OpenSafeBox { get; private set; }
        /// <summary>
        /// 金属物品拖出UI物品界面
        /// </summary>
        public string DropMetal { get; private set; }
        /// <summary>
        /// 野猪待机
        /// </summary>
        public string BoarIdle { get; private set; }
        /// <summary>
        /// 熊待机
        /// </summary>
        public string BearIdle { get; private set; }
        /// <summary>
        /// 鸡待机
        /// </summary>
        public string ChickenIdle { get; private set; }
        /// <summary>
        /// 科学家待机
        /// </summary>
        public string ScientistIdle { get; private set; }
        /// <summary>
        /// 空投飞机飞行音效
        /// </summary>
        public string AirDropPlaneFly { get; private set; }
        /// <summary>
        /// 修理台开启
        /// </summary>
        public string OpenRepairBench { get; private set; }
        /// <summary>
        /// 修理台关闭
        /// </summary>
        public string CloseRepairBench { get; private set; }
        /// <summary>
        /// 修理台修理
        /// </summary>
        public string RepairBenchRepair { get; private set; }
        /// <summary>
        /// 苍蝇飞的音效
        /// </summary>
        public string FlySwarm { get; private set; }
        /// <summary>
        /// 调制台开启
        /// </summary>
        public string OpenModerator { get; private set; }
        /// <summary>
        /// 调制台关闭
        /// </summary>
        public string CloseModerator { get; private set; }
        /// <summary>
        /// 调制开启
        /// </summary>
        public string StartModerator { get; private set; }
        /// <summary>
        /// 调制结束
        /// </summary>
        public string EndModerator { get; private set; }
        /// <summary>
        /// 自动炮塔装载枪械
        /// </summary>
        public string AutoTurretEquipWeapon { get; private set; }
        /// <summary>
        /// 自动炮塔切换目标
        /// </summary>
        public string AutoTurretChangeTarget { get; private set; }
        /// <summary>
        /// 自动炮塔获取目标
        /// </summary>
        public string AutoTurretHasTarget { get; private set; }
        /// <summary>
        /// 自动炮塔丢失目标
        /// </summary>
        public string AutoTurretTargetLost { get; private set; }
        /// <summary>
        /// 自动炮塔转头声
        /// </summary>
        public string AutoTurretRotate { get; private set; }
        /// <summary>
        /// 开关拉闸声
        /// </summary>
        public string Switch { get; private set; }
        /// <summary>
        /// 吊顶灯亮起
        /// </summary>
        public string CeilingLightTurnOn { get; private set; }
        /// <summary>
        /// 吊顶灯熄灭
        /// </summary>
        public string CeilingLightTurnOff { get; private set; }
        /// <summary>
        /// 风力发电机运行声
        /// </summary>
        public string WindGenerateLoop { get; private set; }
        /// <summary>
        /// 自动炮塔开机声
        /// </summary>
        public string AutoTurretTurnOn { get; private set; }
        /// <summary>
        /// 自动炮塔关机声
        /// </summary>
        public string AutoTurretTurnOff { get; private set; }
        /// <summary>
        /// 自动炮塔运行声
        /// </summary>
        public string AutoTurretMovementLoop { get; private set; }
        /// <summary>
        /// 自动炮塔模式切换声
        /// </summary>
        public string AutoTurretTargetAcquired { get; private set; }
        /// <summary>
        /// 自动炮塔换弹声
        /// </summary>
        public string AutoTurretReload { get; private set; }
        /// <summary>
        /// 自动炮塔授权变更声
        /// </summary>
        public string AutoTurretAuthorize { get; private set; }
        /// <summary>
        /// 双金属门和金属门的开门
        /// </summary>
        public string DoorMetalOpen { get; private set; }
        /// <summary>
        /// 双金属门和金属门的关门
        /// </summary>
        public string DoorMetalClose { get; private set; }
        /// <summary>
        /// 双木门和木门的开门
        /// </summary>
        public string DoorWoodOpen { get; private set; }
        /// <summary>
        /// 双木门和木门的关门
        /// </summary>
        public string DoorWoodClose { get; private set; }
        /// <summary>
        /// 工具柜的打开
        /// </summary>
        public string ToolCupboardOpen { get; private set; }
        /// <summary>
        /// 工具柜的关闭
        /// </summary>
        public string ToolCupboardClose { get; private set; }
        /// <summary>
        /// 种植箱的打开
        /// </summary>
        public string WoodBoxLargeOpen { get; private set; }
        /// <summary>
        /// 种植箱的关闭
        /// </summary>
        public string WoodBoxLargeClose { get; private set; }
        /// <summary>
        /// 木质梯子
        /// </summary>
        public string SvlBuildDeployWoodLadder { get; private set; }
        /// <summary>
        /// 水瓶水花炸开
        /// </summary>
        public string WaterBottleSplash { get; private set; }
        /// <summary>
        /// 水桶水花炸开
        /// </summary>
        public string WaterBucketSplash { get; private set; }
        /// <summary>
        /// 跳弹
        /// </summary>
        public string Ricochet { get; private set; }
        /// <summary>
        /// 小型燃油发电机启动
        /// </summary>
        public string SmallGeneratorStart { get; private set; }
        /// <summary>
        /// 小型燃油发电机停止
        /// </summary>
        public string SmallGeneratorStop { get; private set; }
        /// <summary>
        /// 路障和地刺伤害音效
        /// </summary>
        public string DamageOnWoodenFloorSpikes { get; private set; }
        /// <summary>
        /// 坦克引擎声
        /// </summary>
        public string TankEngine { get; private set; }
        /// <summary>
        /// 探照灯打开
        /// </summary>
        public string SearchLightOpen { get; private set; }
        /// <summary>
        /// 探照灯关闭
        /// </summary>
        public string SearchLightClose { get; private set; }
        /// <summary>
        /// 探照灯开关打开
        /// </summary>
        public string SearchLightTurnOn { get; private set; }
        /// <summary>
        /// 探照灯开关关闭
        /// </summary>
        public string SearchLightTurnOff { get; private set; }
        /// <summary>
        /// 马匹死亡
        /// </summary>
        public string HorseDeath { get; private set; }
        /// <summary>
        /// 抽水泵开
        /// </summary>
        public string WaterPumpStart { get; private set; }
        /// <summary>
        /// 抽水泵关
        /// </summary>
        public string WaterPumpStop { get; private set; }
        /// <summary>
        /// 电力净水器开
        /// </summary>
        public string PowerWaterPurifierStart { get; private set; }
        /// <summary>
        /// 电力净水器关
        /// </summary>
        public string PowerWaterPurifierStop { get; private set; }
        /// <summary>
        /// 流体开关音效
        /// </summary>
        public string FluifSwitchActiveLoop { get; private set; }
        /// <summary>
        /// 洒水器喷水音效
        /// </summary>
        public string SprinklerLoop { get; private set; }
        /// <summary>
        /// 探照灯移动音效
        /// </summary>
        public string SearchLightMove { get; private set; }
        /// <summary>
        /// 压力传感器开启
        /// </summary>
        public string PressureSensorOpen { get; private set; }
        /// <summary>
        /// 压力传感器关闭
        /// </summary>
        public string PressureSensorClose { get; private set; }
        /// <summary>
        /// 建筑电梯按钮音效
        /// </summary>
        public string ElevatorButton { get; private set; }
        /// <summary>
        /// 建筑电梯移动开始音效
        /// </summary>
        public string ElevatorMoveStart { get; private set; }
        /// <summary>
        /// 建筑电梯移动结束音效
        /// </summary>
        public string ElevatorMoveStop { get; private set; }
        /// <summary>
        /// 运输机武直爆炸音效
        /// </summary>
        public string HelicopterExplo { get; private set; }
        /// <summary>
        /// npc炮塔左右巡逻移动过程
        /// </summary>
        public string NPCTurretIdle { get; private set; }
        /// <summary>
        /// npc炮塔锁定
        /// </summary>
        public string NPCTurretHasTarget { get; private set; }
        /// <summary>
        /// npc炮塔解除锁定
        /// </summary>
        public string NPCTurretLoseTarget { get; private set; }
        /// <summary>
        /// npc炮塔开枪
        /// </summary>
        public string NPCTurretFire { get; private set; }
        /// <summary>
        /// 防空炮 抬头 时的机械声
        /// </summary>
        public string SamSiteMovement { get; private set; }
        /// <summary>
        /// 防空炮 转动 时的机械声
        /// </summary>
        public string SamSiteTurnMovement { get; private set; }
        /// <summary>
        /// 流体开关开关音效
        /// </summary>
        public string FluidicSwitchOepnClose { get; private set; }
        /// <summary>
        /// 武直掉落
        /// </summary>
        public string HelicopterFallDown { get; private set; }
        /// <summary>
        /// 坦克爆炸音效
        /// </summary>
        public string BradleyExplo { get; private set; }
        /// <summary>
        /// 警笛灯
        /// </summary>
        public string SirenLight { get; private set; }
        /// <summary>
        /// 载具配件卸载
        /// </summary>
        public string VehiclePartsDismount { get; private set; }
        /// <summary>
        /// 新手关飞机滑落音效
        /// </summary>
        public string NewbieHelicopterSlip { get; private set; }
        /// <summary>
        /// 新手关坦克行驶音效
        /// </summary>
        public string NewbieTankMove { get; private set; }
        /// <summary>
        /// 计时器转动音效
        /// </summary>
        public string ElectricTimerLoop { get; private set; }
        /// <summary>
        /// 蓝图放置音效
        /// </summary>
        public string BuildWoodBlueprintStructure { get; private set; }

        public const int __ID__ = 1427877743;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"HitEnemy\":" + $"\"{HitEnemy}\"" + ","
            + "\"HitHeadEnemy\":" + $"\"{HitHeadEnemy}\"" + ","
            + "\"KillEnemy\":" + $"\"{KillEnemy}\"" + ","
            + "\"KnockDownEnemy\":" + $"\"{KnockDownEnemy}\"" + ","
            + "\"BeHit\":" + $"\"{BeHit}\"" + ","
            + "\"BeHitHead\":" + $"\"{BeHitHead}\"" + ","
            + "\"BeKnockDown\":" + $"\"{BeKnockDown}\"" + ","
            + "\"BeKill\":" + $"\"{BeKill}\"" + ","
            + "\"MeleeHitHead\":" + $"\"{MeleeHitHead}\"" + ","
            + "\"MeleeBeHit\":" + $"\"{MeleeBeHit}\"" + ","
            + "\"MeleeBeHitHead\":" + $"\"{MeleeBeHitHead}\"" + ","
            + "\"P3MeleeKnockDownEnemy\":" + $"\"{P3MeleeKnockDownEnemy}\"" + ","
            + "\"P3MeleeKillEnemy\":" + $"\"{P3MeleeKillEnemy}\"" + ","
            + "\"P3HitEnemy\":" + $"\"{P3HitEnemy}\"" + ","
            + "\"P3KnockDownEnemy\":" + $"\"{P3KnockDownEnemy}\"" + ","
            + "\"P3KillEnemy\":" + $"\"{P3KillEnemy}\"" + ","
            + "\"HitTexture\":" + $"\"{HitTexture}\"" + ","
            + "\"PlayerDied\":" + $"\"{PlayerDied}\"" + ","
            + "\"UIClick\":" + $"\"{UIClick}\"" + ","
            + "\"UIWheelEnter\":" + $"\"{UIWheelEnter}\"" + ","
            + "\"UIWheelSelect\":" + $"\"{UIWheelSelect}\"" + ","
            + "\"UIWheelExit\":" + $"\"{UIWheelExit}\"" + ","
            + "\"OreCrash\":" + $"\"{OreCrash}\"" + ","
            + "\"OreHitCritical\":" + $"\"{OreHitCritical}\"" + ","
            + "\"TreeFail\":" + $"\"{TreeFail}\"" + ","
            + "\"TreeCrackMed\":" + $"\"{TreeCrackMed}\"" + ","
            + "\"TreeCrackSmall\":" + $"\"{TreeCrackSmall}\"" + ","
            + "\"TreeHitMarker\":" + $"\"{TreeHitMarker}\"" + ","
            + "\"BuildSuccess\":" + $"\"{BuildSuccess}\"" + ","
            + "\"BuildWoodCrash\":" + $"\"{BuildWoodCrash}\"" + ","
            + "\"BuildWoodStructure\":" + $"\"{BuildWoodStructure}\"" + ","
            + "\"BuildStoneStructure\":" + $"\"{BuildStoneStructure}\"" + ","
            + "\"BuildStoneCrash\":" + $"\"{BuildStoneCrash}\"" + ","
            + "\"BuildDeployWorkbench\":" + $"\"{BuildDeployWorkbench}\"" + ","
            + "\"BuildMetalCrash\":" + $"\"{BuildMetalCrash}\"" + ","
            + "\"BuildDeployStorageBox\":" + $"\"{BuildDeployStorageBox}\"" + ","
            + "\"BuildDeployCabinet\":" + $"\"{BuildDeployCabinet}\"" + ","
            + "\"BuildDeployWoodDoor\":" + $"\"{BuildDeployWoodDoor}\"" + ","
            + "\"BuildDeployMetalDoor\":" + $"\"{BuildDeployMetalDoor}\"" + ","
            + "\"BuildDeployBonfire\":" + $"\"{BuildDeployBonfire}\"" + ","
            + "\"BuildDeploySmelter\":" + $"\"{BuildDeploySmelter}\"" + ","
            + "\"OpenSafeBox\":" + $"\"{OpenSafeBox}\"" + ","
            + "\"DropMetal\":" + $"\"{DropMetal}\"" + ","
            + "\"BoarIdle\":" + $"\"{BoarIdle}\"" + ","
            + "\"BearIdle\":" + $"\"{BearIdle}\"" + ","
            + "\"ChickenIdle\":" + $"\"{ChickenIdle}\"" + ","
            + "\"ScientistIdle\":" + $"\"{ScientistIdle}\"" + ","
            + "\"AirDropPlaneFly\":" + $"\"{AirDropPlaneFly}\"" + ","
            + "\"OpenRepairBench\":" + $"\"{OpenRepairBench}\"" + ","
            + "\"CloseRepairBench\":" + $"\"{CloseRepairBench}\"" + ","
            + "\"RepairBenchRepair\":" + $"\"{RepairBenchRepair}\"" + ","
            + "\"FlySwarm\":" + $"\"{FlySwarm}\"" + ","
            + "\"OpenModerator\":" + $"\"{OpenModerator}\"" + ","
            + "\"CloseModerator\":" + $"\"{CloseModerator}\"" + ","
            + "\"StartModerator\":" + $"\"{StartModerator}\"" + ","
            + "\"EndModerator\":" + $"\"{EndModerator}\"" + ","
            + "\"AutoTurretEquipWeapon\":" + $"\"{AutoTurretEquipWeapon}\"" + ","
            + "\"AutoTurretChangeTarget\":" + $"\"{AutoTurretChangeTarget}\"" + ","
            + "\"AutoTurretHasTarget\":" + $"\"{AutoTurretHasTarget}\"" + ","
            + "\"AutoTurretTargetLost\":" + $"\"{AutoTurretTargetLost}\"" + ","
            + "\"AutoTurretRotate\":" + $"\"{AutoTurretRotate}\"" + ","
            + "\"Switch\":" + $"\"{Switch}\"" + ","
            + "\"CeilingLightTurnOn\":" + $"\"{CeilingLightTurnOn}\"" + ","
            + "\"CeilingLightTurnOff\":" + $"\"{CeilingLightTurnOff}\"" + ","
            + "\"WindGenerateLoop\":" + $"\"{WindGenerateLoop}\"" + ","
            + "\"AutoTurretTurnOn\":" + $"\"{AutoTurretTurnOn}\"" + ","
            + "\"AutoTurretTurnOff\":" + $"\"{AutoTurretTurnOff}\"" + ","
            + "\"AutoTurretMovementLoop\":" + $"\"{AutoTurretMovementLoop}\"" + ","
            + "\"AutoTurretTargetAcquired\":" + $"\"{AutoTurretTargetAcquired}\"" + ","
            + "\"AutoTurretReload\":" + $"\"{AutoTurretReload}\"" + ","
            + "\"AutoTurretAuthorize\":" + $"\"{AutoTurretAuthorize}\"" + ","
            + "\"DoorMetalOpen\":" + $"\"{DoorMetalOpen}\"" + ","
            + "\"DoorMetalClose\":" + $"\"{DoorMetalClose}\"" + ","
            + "\"DoorWoodOpen\":" + $"\"{DoorWoodOpen}\"" + ","
            + "\"DoorWoodClose\":" + $"\"{DoorWoodClose}\"" + ","
            + "\"ToolCupboardOpen\":" + $"\"{ToolCupboardOpen}\"" + ","
            + "\"ToolCupboardClose\":" + $"\"{ToolCupboardClose}\"" + ","
            + "\"WoodBoxLargeOpen\":" + $"\"{WoodBoxLargeOpen}\"" + ","
            + "\"WoodBoxLargeClose\":" + $"\"{WoodBoxLargeClose}\"" + ","
            + "\"SvlBuildDeployWoodLadder\":" + $"\"{SvlBuildDeployWoodLadder}\"" + ","
            + "\"WaterBottleSplash\":" + $"\"{WaterBottleSplash}\"" + ","
            + "\"WaterBucketSplash\":" + $"\"{WaterBucketSplash}\"" + ","
            + "\"Ricochet\":" + $"\"{Ricochet}\"" + ","
            + "\"SmallGeneratorStart\":" + $"\"{SmallGeneratorStart}\"" + ","
            + "\"SmallGeneratorStop\":" + $"\"{SmallGeneratorStop}\"" + ","
            + "\"DamageOnWoodenFloorSpikes\":" + $"\"{DamageOnWoodenFloorSpikes}\"" + ","
            + "\"TankEngine\":" + $"\"{TankEngine}\"" + ","
            + "\"SearchLightOpen\":" + $"\"{SearchLightOpen}\"" + ","
            + "\"SearchLightClose\":" + $"\"{SearchLightClose}\"" + ","
            + "\"SearchLightTurnOn\":" + $"\"{SearchLightTurnOn}\"" + ","
            + "\"SearchLightTurnOff\":" + $"\"{SearchLightTurnOff}\"" + ","
            + "\"HorseDeath\":" + $"\"{HorseDeath}\"" + ","
            + "\"WaterPumpStart\":" + $"\"{WaterPumpStart}\"" + ","
            + "\"WaterPumpStop\":" + $"\"{WaterPumpStop}\"" + ","
            + "\"PowerWaterPurifierStart\":" + $"\"{PowerWaterPurifierStart}\"" + ","
            + "\"PowerWaterPurifierStop\":" + $"\"{PowerWaterPurifierStop}\"" + ","
            + "\"FluifSwitchActiveLoop\":" + $"\"{FluifSwitchActiveLoop}\"" + ","
            + "\"SprinklerLoop\":" + $"\"{SprinklerLoop}\"" + ","
            + "\"SearchLightMove\":" + $"\"{SearchLightMove}\"" + ","
            + "\"PressureSensorOpen\":" + $"\"{PressureSensorOpen}\"" + ","
            + "\"PressureSensorClose\":" + $"\"{PressureSensorClose}\"" + ","
            + "\"ElevatorButton\":" + $"\"{ElevatorButton}\"" + ","
            + "\"ElevatorMoveStart\":" + $"\"{ElevatorMoveStart}\"" + ","
            + "\"ElevatorMoveStop\":" + $"\"{ElevatorMoveStop}\"" + ","
            + "\"HelicopterExplo\":" + $"\"{HelicopterExplo}\"" + ","
            + "\"NPCTurretIdle\":" + $"\"{NPCTurretIdle}\"" + ","
            + "\"NPCTurretHasTarget\":" + $"\"{NPCTurretHasTarget}\"" + ","
            + "\"NPCTurretLoseTarget\":" + $"\"{NPCTurretLoseTarget}\"" + ","
            + "\"NPCTurretFire\":" + $"\"{NPCTurretFire}\"" + ","
            + "\"SamSiteMovement\":" + $"\"{SamSiteMovement}\"" + ","
            + "\"SamSiteTurnMovement\":" + $"\"{SamSiteTurnMovement}\"" + ","
            + "\"FluidicSwitchOepnClose\":" + $"\"{FluidicSwitchOepnClose}\"" + ","
            + "\"HelicopterFallDown\":" + $"\"{HelicopterFallDown}\"" + ","
            + "\"BradleyExplo\":" + $"\"{BradleyExplo}\"" + ","
            + "\"SirenLight\":" + $"\"{SirenLight}\"" + ","
            + "\"VehiclePartsDismount\":" + $"\"{VehiclePartsDismount}\"" + ","
            + "\"NewbieHelicopterSlip\":" + $"\"{NewbieHelicopterSlip}\"" + ","
            + "\"NewbieTankMove\":" + $"\"{NewbieTankMove}\"" + ","
            + "\"ElectricTimerLoop\":" + $"\"{ElectricTimerLoop}\"" + ","
            + "\"BuildWoodBlueprintStructure\":" + $"\"{BuildWoodBlueprintStructure}\""
            + "}";

        partial void PostInit();
    }
}

#endif