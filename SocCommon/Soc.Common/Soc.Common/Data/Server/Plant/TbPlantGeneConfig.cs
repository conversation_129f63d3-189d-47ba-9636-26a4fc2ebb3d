//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Plant
{
    public sealed partial class TbPlantGeneConfig: BaseTable
    {
        public static int HashCode = 149665465;

        public IReadOnlyDictionary<int, Plant.PlantGeneConfig> DataMap => dataMap;
        private Dictionary<int, Plant.PlantGeneConfig> dataMap = new();
        public IReadOnlyList<Plant.PlantGeneConfig> DataList => dataList;
        private List<Plant.PlantGeneConfig> dataList = new();


        public TbPlantGeneConfig(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbPlantGeneConfig error: HashCode not equal, Sheet may be changed!");
            }
            foreach (JSONNode _row in _json["data"].Children)
            {
                try
                {
                    var _v = Plant.PlantGeneConfig.DeserializePlantGeneConfig(_row);
                    dataList.Add(_v);
                    dataMap.Add(_v.Id, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TbPlantGeneConfig error: " + ex.Message);
                }
            }
            enumer = dataMap.Values;
            PostInit();
        }

        public Plant.PlantGeneConfig GetOrDefault(int key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            int Id = default;
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }

            if (dataMap.ContainsKey(Id))
            {
                var _v = dataMap[Id];
                _v.Update(_json);
            }
            else
            {
                var _v = Plant.PlantGeneConfig.DeserializePlantGeneConfig(_json);
                dataList.Add(_v);
                dataMap.Add(_v.Id, _v);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();

        public override Bright.Config.BeanBase GetByKey(long key) => dataMap.TryGetValue((int)key, out var ret) ? ret : default;
    }
}

#endif