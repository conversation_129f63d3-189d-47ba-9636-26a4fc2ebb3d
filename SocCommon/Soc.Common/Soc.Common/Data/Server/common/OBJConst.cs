//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.common
{
    public sealed partial class OBJConst : Bright.Config.BeanBase 
    {
        public OBJConst() { }

        public OBJConst(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("PlayerNameLengthMin")) { var _j = _json["PlayerNameLengthMin"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PlayerNameLengthMin  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("PlayerNameLengthMax")) { var _j = _json["PlayerNameLengthMax"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  PlayerNameLengthMax  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ChangeNameCost")) { var _j = _json["ChangeNameCost"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  ChangeNameCost  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ChangeNameCD")) { var _j = _json["ChangeNameCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ChangeNameCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("DeletePlayerServerCD")) { var _j = _json["DeletePlayerServerCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  DeletePlayerServerCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MaxBattleServerCount")) { var _j = _json["MaxBattleServerCount"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MaxBattleServerCount  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SendInvitationCD")) { var _j = _json["SendInvitationCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SendInvitationCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("InvitationExpireTime")) { var _j = _json["InvitationExpireTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  InvitationExpireTime  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SendInvitationLimit")) { var _j = _json["SendInvitationLimit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SendInvitationLimit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ReceiveInvitationLimit")) { var _j = _json["ReceiveInvitationLimit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ReceiveInvitationLimit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("RecruitmenApplyForLimit")) { var _j = _json["RecruitmenApplyForLimit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RecruitmenApplyForLimit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SendRecruitmenApplyForCD")) { var _j = _json["SendRecruitmenApplyForCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SendRecruitmenApplyForCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("RecruitmentRefreshButtonCD")) { var _j = _json["RecruitmentRefreshButtonCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RecruitmentRefreshButtonCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("RecruitmentExpireTime")) { var _j = _json["RecruitmentExpireTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RecruitmentExpireTime  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AheadAppointmentSeconds")) { var _j = _json["AheadAppointmentSeconds"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AheadAppointmentSeconds  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("TeamMatchCDSeconds")) { var _j = _json["TeamMatchCDSeconds"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  TeamMatchCDSeconds  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MaxAppointmentListNum")) { var _j = _json["MaxAppointmentListNum"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MaxAppointmentListNum  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AppointmentOutTimeCD")) { var _j = _json["AppointmentOutTimeCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AppointmentOutTimeCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("CreatRoleEquip")) { var _j = _json["CreatRoleEquip"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; CreatRoleEquip = new int[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { int __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  CreatRoleEquip[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("NameChangeCost")) { var _j = _json["NameChangeCost"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { NameChangeCost = new System.Collections.Generic.List<common.BTuple2>(__json0.Count); foreach(JSONNode __e0 in __json0.Children) { common.BTuple2 __v0= default;  if(__e0.IsObject) { __v0 = common.BTuple2.DeserializeBTuple2(__e0); } else { throw new SerializationException(); }  NameChangeCost.Add(__v0); } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("AppointmentDisbandTime")) { var _j = _json["AppointmentDisbandTime"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AppointmentDisbandTime  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("InitialAvatar")) { var _j = _json["InitialAvatar"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  InitialAvatar  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("InitialAvatarFrame")) { var _j = _json["InitialAvatarFrame"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  InitialAvatarFrame  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("InitialInfoCard")) { var _j = _json["InitialInfoCard"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  InitialInfoCard  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("InitialInfoBubble")) { var _j = _json["InitialInfoBubble"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  InitialInfoBubble  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MaxPersTagNum")) { var _j = _json["MaxPersTagNum"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MaxPersTagNum  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MaxHistoryAppointmentListNum")) { var _j = _json["MaxHistoryAppointmentListNum"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MaxHistoryAppointmentListNum  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("WarReserveBattleAlert")) { var _j = _json["WarReserveBattleAlert"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  WarReserveBattleAlert  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("LobbyReserveBattleAlert")) { var _j = _json["LobbyReserveBattleAlert"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  LobbyReserveBattleAlert  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AppointmentSwitchThresholdHour")) { var _j = _json["AppointmentSwitchThresholdHour"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AppointmentSwitchThresholdHour  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AutoReqGameRecruitmenListCD")) { var _j = _json["AutoReqGameRecruitmenListCD"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AutoReqGameRecruitmenListCD  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AppointmentTeamPushTime10")) { var _j = _json["AppointmentTeamPushTime10"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AppointmentTeamPushTime10  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("AppointmentTeamPushTime20")) { var _j = _json["AppointmentTeamPushTime20"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  AppointmentTeamPushTime20  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("BPExpWeekLimit")) { var _j = _json["BPExpWeekLimit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  BPExpWeekLimit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SettleStyleRankPointsRecordsLimit")) { var _j = _json["SettleStyleRankPointsRecordsLimit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SettleStyleRankPointsRecordsLimit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ProtectStyleRankCheckDays")) { var _j = _json["ProtectStyleRankCheckDays"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ProtectStyleRankCheckDays  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("RecruitmentListNumber")) { var _j = _json["RecruitmentListNumber"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  RecruitmentListNumber  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SkipResGetPopupResID")) { var _j = _json["SkipResGetPopupResID"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; SkipResGetPopupResID = new int[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { int __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  SkipResGetPopupResID[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("BatchUsePackageLimit")) { var _j = _json["BatchUsePackageLimit"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  BatchUsePackageLimit  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("MedalSyncIntervalMinutes")) { var _j = _json["MedalSyncIntervalMinutes"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  MedalSyncIntervalMinutes  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("ChangeRankMatchCd")) { var _j = _json["ChangeRankMatchCd"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  ChangeRankMatchCd  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("SeasonUnlockLevel")) { var _j = _json["SeasonUnlockLevel"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SeasonUnlockLevel  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static OBJConst DeserializeOBJConst(JSONNode _json)
        {
            return new common.OBJConst(false, _json);
        }

        /// <summary>
        /// 玩家名字最小长度
        /// </summary>
        public int PlayerNameLengthMin { get; private set; }
        /// <summary>
        /// 玩家名字最大长度
        /// </summary>
        public int PlayerNameLengthMax { get; private set; }
        /// <summary>
        /// 改名消耗
        /// </summary>
        public string ChangeNameCost { get; private set; }
        /// <summary>
        /// 改名冷却时间 单位：秒
        /// </summary>
        public int ChangeNameCD { get; private set; }
        /// <summary>
        /// 删除服务器冷却时间：秒
        /// </summary>
        public int DeletePlayerServerCD { get; private set; }
        /// <summary>
        /// 允许进入的最大战局数
        /// </summary>
        public int MaxBattleServerCount { get; private set; }
        /// <summary>
        /// 重复发送邀请CD单位：秒
        /// </summary>
        public int SendInvitationCD { get; private set; }
        /// <summary>
        /// 邀请存在时长单位：秒
        /// </summary>
        public int InvitationExpireTime { get; private set; }
        /// <summary>
        /// 发送邀请上限
        /// </summary>
        public int SendInvitationLimit { get; private set; }
        /// <summary>
        /// 收到邀请上限
        /// </summary>
        public int ReceiveInvitationLimit { get; private set; }
        /// <summary>
        /// 接收招募申请上限
        /// </summary>
        public int RecruitmenApplyForLimit { get; private set; }
        /// <summary>
        /// 发送招募申请CD 单位：秒
        /// </summary>
        public int SendRecruitmenApplyForCD { get; private set; }
        /// <summary>
        /// 招募界面刷新按钮点击CD
        /// </summary>
        public int RecruitmentRefreshButtonCD { get; private set; }
        /// <summary>
        /// 招募申请存在时长单位：秒
        /// </summary>
        public int RecruitmentExpireTime { get; private set; }
        /// <summary>
        /// 提前多久可以预约单位：秒
        /// </summary>
        public int AheadAppointmentSeconds { get; private set; }
        /// <summary>
        /// 组队匹配的CD单位：秒
        /// </summary>
        public int TeamMatchCDSeconds { get; private set; }
        /// <summary>
        /// 预约时段的最大显示数量
        /// </summary>
        public int MaxAppointmentListNum { get; private set; }
        /// <summary>
        /// 预约时段过期CD(客户端前置拦截用)：秒
        /// </summary>
        public int AppointmentOutTimeCD { get; private set; }
        /// <summary>
        /// 建角展示装备
        /// </summary>
        public int[] CreatRoleEquip { get; private set; }
        /// <summary>
        /// 改名消耗
        /// </summary>
        public System.Collections.Generic.List<common.BTuple2> NameChangeCost { get; private set; }
        /// <summary>
        /// 预约队伍到期自动解散时间：秒
        /// </summary>
        public int AppointmentDisbandTime { get; private set; }
        /// <summary>
        /// 初始头像
        /// </summary>
        public int InitialAvatar { get; private set; }
        /// <summary>
        /// 初始头像框
        /// </summary>
        public int InitialAvatarFrame { get; private set; }
        /// <summary>
        /// 初始名片
        /// </summary>
        public int InitialInfoCard { get; private set; }
        /// <summary>
        /// 初始气泡
        /// </summary>
        public int InitialInfoBubble { get; private set; }
        /// <summary>
        /// 个性标签数量上限
        /// </summary>
        public int MaxPersTagNum { get; private set; }
        /// <summary>
        /// 历史开服的最大显示数量
        /// </summary>
        public int MaxHistoryAppointmentListNum { get; private set; }
        /// <summary>
        /// 预约开服前提示其他战局玩家
        /// </summary>
        public int WarReserveBattleAlert { get; private set; }
        /// <summary>
        /// 预约开服前提示大厅其他界面玩家
        /// </summary>
        public int LobbyReserveBattleAlert { get; private set; }
        /// <summary>
        /// 能够切换当前第二个预约时间的最小时间
        /// </summary>
        public int AppointmentSwitchThresholdHour { get; private set; }
        /// <summary>
        /// 聊天侧边栏自动请求局内招募的cd：秒<br/>（客户端做了保底5秒，防止配的太小影响服务器性能）
        /// </summary>
        public int AutoReqGameRecruitmenListCD { get; private set; }
        /// <summary>
        /// 预约开服前10分钟推送:秒
        /// </summary>
        public int AppointmentTeamPushTime10 { get; private set; }
        /// <summary>
        /// 预约开服前20分钟推送：秒
        /// </summary>
        public int AppointmentTeamPushTime20 { get; private set; }
        /// <summary>
        /// 每周通行证经验上限
        /// </summary>
        public int BPExpWeekLimit { get; private set; }
        /// <summary>
        /// 结算风格段位积分场次限制
        /// </summary>
        public int SettleStyleRankPointsRecordsLimit { get; private set; }
        /// <summary>
        /// 风格段位保段的检测时间间隔：天
        /// </summary>
        public int ProtectStyleRankCheckDays { get; private set; }
        /// <summary>
        /// 招募列表显示条数
        /// </summary>
        public int RecruitmentListNumber { get; private set; }
        /// <summary>
        /// 跳过资源获得展示弹窗的资源id
        /// </summary>
        public int[] SkipResGetPopupResID { get; private set; }
        /// <summary>
        /// 批量使用礼包数量限制
        /// </summary>
        public int BatchUsePackageLimit { get; private set; }
        /// <summary>
        /// 勋章定时结算时间间隔(分钟)
        /// </summary>
        public int MedalSyncIntervalMinutes { get; private set; }
        /// <summary>
        /// 勋章生效对局切换CD（分钟）
        /// </summary>
        public int ChangeRankMatchCd { get; private set; }
        /// <summary>
        /// 赛季解锁等级
        /// </summary>
        public int SeasonUnlockLevel { get; private set; }

        public const int __ID__ = -495401617;
        public override int GetTypeId() => __ID__;

            

        public override void Resolve(Dictionary<string, BaseTable> _tables)
        {
            foreach(var _e in NameChangeCost) { _e?.Resolve(_tables); }
            PostResolve();
        }

        public override string ToString() => "{ "
            + "\"PlayerNameLengthMin\":" + PlayerNameLengthMin + ","
            + "\"PlayerNameLengthMax\":" + PlayerNameLengthMax + ","
            + "\"ChangeNameCost\":" + $"\"{ChangeNameCost}\"" + ","
            + "\"ChangeNameCD\":" + ChangeNameCD + ","
            + "\"DeletePlayerServerCD\":" + DeletePlayerServerCD + ","
            + "\"MaxBattleServerCount\":" + MaxBattleServerCount + ","
            + "\"SendInvitationCD\":" + SendInvitationCD + ","
            + "\"InvitationExpireTime\":" + InvitationExpireTime + ","
            + "\"SendInvitationLimit\":" + SendInvitationLimit + ","
            + "\"ReceiveInvitationLimit\":" + ReceiveInvitationLimit + ","
            + "\"RecruitmenApplyForLimit\":" + RecruitmenApplyForLimit + ","
            + "\"SendRecruitmenApplyForCD\":" + SendRecruitmenApplyForCD + ","
            + "\"RecruitmentRefreshButtonCD\":" + RecruitmentRefreshButtonCD + ","
            + "\"RecruitmentExpireTime\":" + RecruitmentExpireTime + ","
            + "\"AheadAppointmentSeconds\":" + AheadAppointmentSeconds + ","
            + "\"TeamMatchCDSeconds\":" + TeamMatchCDSeconds + ","
            + "\"MaxAppointmentListNum\":" + MaxAppointmentListNum + ","
            + "\"AppointmentOutTimeCD\":" + AppointmentOutTimeCD + ","
            + "\"CreatRoleEquip\":" + Bright.Common.StringUtil.CollectionToString(CreatRoleEquip) + ","
            + "\"NameChangeCost\":" + Bright.Common.StringUtil.CollectionToString(NameChangeCost) + ","
            + "\"AppointmentDisbandTime\":" + AppointmentDisbandTime + ","
            + "\"InitialAvatar\":" + InitialAvatar + ","
            + "\"InitialAvatarFrame\":" + InitialAvatarFrame + ","
            + "\"InitialInfoCard\":" + InitialInfoCard + ","
            + "\"InitialInfoBubble\":" + InitialInfoBubble + ","
            + "\"MaxPersTagNum\":" + MaxPersTagNum + ","
            + "\"MaxHistoryAppointmentListNum\":" + MaxHistoryAppointmentListNum + ","
            + "\"WarReserveBattleAlert\":" + WarReserveBattleAlert + ","
            + "\"LobbyReserveBattleAlert\":" + LobbyReserveBattleAlert + ","
            + "\"AppointmentSwitchThresholdHour\":" + AppointmentSwitchThresholdHour + ","
            + "\"AutoReqGameRecruitmenListCD\":" + AutoReqGameRecruitmenListCD + ","
            + "\"AppointmentTeamPushTime10\":" + AppointmentTeamPushTime10 + ","
            + "\"AppointmentTeamPushTime20\":" + AppointmentTeamPushTime20 + ","
            + "\"BPExpWeekLimit\":" + BPExpWeekLimit + ","
            + "\"SettleStyleRankPointsRecordsLimit\":" + SettleStyleRankPointsRecordsLimit + ","
            + "\"ProtectStyleRankCheckDays\":" + ProtectStyleRankCheckDays + ","
            + "\"RecruitmentListNumber\":" + RecruitmentListNumber + ","
            + "\"SkipResGetPopupResID\":" + Bright.Common.StringUtil.CollectionToString(SkipResGetPopupResID) + ","
            + "\"BatchUsePackageLimit\":" + BatchUsePackageLimit + ","
            + "\"MedalSyncIntervalMinutes\":" + MedalSyncIntervalMinutes + ","
            + "\"ChangeRankMatchCd\":" + ChangeRankMatchCd + ","
            + "\"SeasonUnlockLevel\":" + SeasonUnlockLevel
            + "}";

        partial void PostInit();
    }
}

#endif