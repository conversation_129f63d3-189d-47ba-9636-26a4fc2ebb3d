//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Reputation
{
    public sealed partial class ReputationSelectConfig : Bright.Config.BeanBase 
    {
        public ReputationSelectConfig() { }

        public ReputationSelectConfig(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("selectList")) { var _j = _json["selectList"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { { var __json0 = _j; if(__json0.IsArray) { int _n0 = __json0.Count; SelectList = new int[_n0]; int __index0=0; foreach(JSONNode __e0 in __json0.Children) { int __v0= default;  if( __e0 .IsNumber) {  __v0  =  __e0 ; } else { throw new SerializationException(); }  SelectList[__index0++] = __v0; } } else { throw new SerializationException(); } } } } }
            { if (_json.HasKey("icon")) { var _j = _json["icon"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Icon  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("name")) { var _j = _json["name"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Name_l10n_index = _j["index"]; } else { throw new SerializationException("Name.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("desc")) { var _j = _json["desc"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Desc_l10n_index = _j["index"]; } else { throw new SerializationException("Desc.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("selectNum")) { var _j = _json["selectNum"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  SelectNum  =  _j ; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static ReputationSelectConfig DeserializeReputationSelectConfig(JSONNode _json)
        {
            return new Reputation.ReputationSelectConfig(false, _json);
        }

        /// <summary>
        /// 可选奖励ID
        /// </summary>
        public int Id { get; private set; }
        /// <summary>
        /// 声望奖励条目id
        /// </summary>
        public int[] SelectList { get; private set; }
        /// <summary>
        /// 图标
        /// </summary>
        public string Icon { get; private set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name
        {
            get
            {
                MgrTables.Log.Error("Please use Name_l10n_index instead of Name. Table: ReputationSelectConfig");
                return string.Empty;
            }
        }
        public int Name_l10n_index { get; private set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Desc
        {
            get
            {
                MgrTables.Log.Error("Please use Desc_l10n_index instead of Desc. Table: ReputationSelectConfig");
                return string.Empty;
            }
        }
        public int Desc_l10n_index { get; private set; }
        /// <summary>
        /// 可选数量
        /// </summary>
        public int SelectNum { get; private set; }

        public const int __ID__ = 191699852;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"SelectList\":" + Bright.Common.StringUtil.CollectionToString(SelectList) + ","
            + "\"Icon\":" + $"\"{Icon}\"" + ","
            + "\"Name\":" + Name + ","
            + "\"Desc\":" + Desc + ","
            + "\"SelectNum\":" + SelectNum
            + "}";

        partial void PostInit();
    }
}

#endif