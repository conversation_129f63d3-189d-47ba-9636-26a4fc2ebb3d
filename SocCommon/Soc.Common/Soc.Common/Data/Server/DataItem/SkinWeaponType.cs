//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.DataItem
{
    public sealed partial class SkinWeaponType : Bright.Config.BeanBase 
    {
        public SkinWeaponType() { }

        public SkinWeaponType(bool useless, JSONNode _json) 
        {
            Update(_json);
        }

        public override void Update(JSONNode _json)
        {
            { if (_json.HasKey("id")) { var _j = _json["id"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Id  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("name")) { var _j = _json["name"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Name_l10n_index = _j["index"]; } else { throw new SerializationException("Name.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("sort")) { var _j = _json["sort"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  Sort  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("icon")) { var _j = _json["icon"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsString) {  Icon  =  _j ; } else { throw new SerializationException(); } } } }
            { if (_json.HasKey("tab")) { var _j = _json["tab"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j["index"].IsNumber) { Tab_l10n_index = _j["index"]; } else { throw new SerializationException("Tab.index is " + _j.Tag + ", not number."); } } } }
            { if (_json.HasKey("SkinType")) { var _j = _json["SkinType"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if(_j.IsNumber) { SkinType = (WeaponSkinType)_j.AsInt; } else { throw new SerializationException(); } } } }
            PostInit();
        }

        public static SkinWeaponType DeserializeSkinWeaponType(JSONNode _json)
        {
            return new DataItem.SkinWeaponType(false, _json);
        }

        /// <summary>
        /// 类型id
        /// </summary>
        public int Id { get; private set; }
        /// <summary>
        /// 类型名称
        /// </summary>
        public string Name
        {
            get
            {
                MgrTables.Log.Error("Please use Name_l10n_index instead of Name. Table: SkinWeaponType");
                return string.Empty;
            }
        }
        public int Name_l10n_index { get; private set; }
        /// <summary>
        /// UI显示排序
        /// </summary>
        public int Sort { get; private set; }
        /// <summary>
        /// 图标
        /// </summary>
        public string Icon { get; private set; }
        /// <summary>
        /// 所属收藏页签
        /// </summary>
        public string Tab
        {
            get
            {
                MgrTables.Log.Error("Please use Tab_l10n_index instead of Tab. Table: SkinWeaponType");
                return string.Empty;
            }
        }
        public int Tab_l10n_index { get; private set; }
        /// <summary>
        /// 皮肤类型
        /// </summary>
        public WeaponSkinType SkinType { get; private set; }

        public const int __ID__ = -1188672252;
        public override int GetTypeId() => __ID__;



        public override string ToString() => "{ "
            + "\"Id\":" + Id + ","
            + "\"Name\":" + Name + ","
            + "\"Sort\":" + Sort + ","
            + "\"Icon\":" + $"\"{Icon}\"" + ","
            + "\"Tab\":" + Tab + ","
            + "\"SkinType\":" + (int)SkinType
            + "}";

        partial void PostInit();
    }
}

#endif