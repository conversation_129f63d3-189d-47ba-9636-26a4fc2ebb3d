//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#if !SOC_CLIENT

using Bright.Serialization;
using System.Collections.Generic;
using SimpleJSON;
using Vector2 = WizardGames.Soc.Common.Algorithm.Vector2;
using Vector3 = WizardGames.Soc.Common.Algorithm.Vector3;
using Vector4 = WizardGames.Soc.Common.Algorithm.Vector4;

namespace WizardGames.Soc.Common.Data.Combat
{
    public sealed partial class TbSkillOverlapGroup: BaseTable
    {
        public static int HashCode = 1385034773;

        public IReadOnlyDictionary<int, Combat.SkillOverlapGroup> DataMap => dataMap;
        private Dictionary<int, Combat.SkillOverlapGroup> dataMap = new();
        public IReadOnlyList<Combat.SkillOverlapGroup> DataList => dataList;
        private List<Combat.SkillOverlapGroup> dataList = new();


        public TbSkillOverlapGroup(JSONNode _json)
        {
            if (_json["hash"].AsInt != HashCode)
            {
                MgrTables.SerializeExceptionList.Add("Deserialize TbSkillOverlapGroup error: HashCode not equal, Sheet may be changed!");
            }
            foreach (JSONNode _row in _json["data"].Children)
            {
                try
                {
                    var _v = Combat.SkillOverlapGroup.DeserializeSkillOverlapGroup(_row);
                    dataList.Add(_v);
                    dataMap.Add(_v.OverlapGroup, _v);
                }
                catch (SerializationException ex)
                {
                    MgrTables.SerializeExceptionList.Add("Deserialize TbSkillOverlapGroup error: " + ex.Message);
                }
            }
            enumer = dataMap.Values;
            PostInit();
        }

        public Combat.SkillOverlapGroup GetOrDefault(int key) => DataMap.TryGetValue(key, out var v) ? v : null;

        public override void Update(JSONNode _json)
        {
            base.Update(_json);

            int OverlapGroup = default;
            { if (_json.HasKey("overlapGroup")) { var _j = _json["overlapGroup"]; if (_j.Tag != JSONNodeType.None && _j.Tag != JSONNodeType.NullValue) { if( _j .IsNumber) {  OverlapGroup  =  _j ; } else { throw new SerializationException(); } } } }

            if (dataMap.ContainsKey(OverlapGroup))
            {
                var _v = dataMap[OverlapGroup];
                _v.Update(_json);
            }
            else
            {
                var _v = Combat.SkillOverlapGroup.DeserializeSkillOverlapGroup(_json);
                dataList.Add(_v);
                dataMap.Add(_v.OverlapGroup, _v);
            }
            TriggerDataUpdateEvent();
        }

        partial void PostInit();

        public override Bright.Config.BeanBase GetByKey(long key) => dataMap.TryGetValue((int)key, out var ret) ? ret : default;
    }
}

#endif