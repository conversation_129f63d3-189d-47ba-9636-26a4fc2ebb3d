using System;
using System.Buffers;
using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using WizardGames.Soc.Common.Framework.Network.Kcp;
using WizardGames.Soc.Common.Utility;

namespace WizardGames.Soc.Common.Framework.Network.Impl
{
    public class UdpNetClient
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UdpNetClient));
        private static SocketAsyncEventArgsPool sendPool = new(64, () => new SocketAsyncEventArgs());

        public Action<ReadOnlySequence<byte>> OnDataReceived;

        private readonly Socket sock;
        private readonly IPEndPoint remote;
        private volatile bool isRunning;

        public int PacketSent = 0;
        public int FragmentExpectedToSend = 0;
        public int FragmentSent = 0;
        public int PacketReceived = 0;
        public int NextExpect = 0;
        public int PacketDropped = 0;
        protected Counter sendCounter;
        private readonly Action<ArraySegment<byte>, UdpBuffer> onRawDataReceived;
        private readonly Action<DisconnectReason> onDisconnect;

        public void SetSendCounter(Counter counter)
        {
            sendCounter = counter;
        }

        public UdpNetClient(IPEndPoint ipEndPoint, Action<ArraySegment<byte>, UdpBuffer> action, Action<DisconnectReason> diconnectAction)
        {
            sock = new Socket(ipEndPoint.AddressFamily, SocketType.Dgram, ProtocolType.Udp);
            sock.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveBuffer, 1024 * 1024);
            if (ipEndPoint.AddressFamily == AddressFamily.InterNetwork)
            {
                sock.Bind(new IPEndPoint(IPAddress.Any, 0));
            }
            else
            {
                sock.Bind(new IPEndPoint(IPAddress.IPv6Any, 0));
            }
            remote = ipEndPoint;
            NetUtils.ConfigureSocketBuffers(sock, KcpConfig.ClientConfig.RecvBufferSize, KcpConfig.ClientConfig.SendBufferSize);
            logger.Info($"[KCP] Client: get socket option  recvBuffer{(int)sock.GetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveBuffer)}, " +
      $"sendBuff{(int)sock.GetSocketOption(SocketOptionLevel.Socket, SocketOptionName.SendBuffer)}");
            onRawDataReceived = action;
            onDisconnect = diconnectAction;
        }

        public void Start()
        {
            isRunning = true;

            var e = new SocketAsyncEventArgs();

            UdpNetCommon.SetBuffer(e);
            e.Completed += new EventHandler<SocketAsyncEventArgs>(ReadCompleted);
            if (remote.AddressFamily == AddressFamily.InterNetwork)
            {
                e.RemoteEndPoint = new IPEndPoint(IPAddress.Any, 0);
            }
            else
            {
                e.RemoteEndPoint = new IPEndPoint(IPAddress.IPv6Any, 0);
            }
            bool willRaiseEvent = sock.ReceiveFromAsync(e);
            logger.Info($"Udp Client started receive");
            if (!willRaiseEvent)
            {
                SocketAsyncEventArgs enext = e;
                do
                {
                    enext = ProcessReceive(enext);
                }
                while (enext != null);
            }
        }

        private SocketAsyncEventArgs ProcessReceive(SocketAsyncEventArgs e)
        {
            if (e.SocketError == SocketError.Success && e.BytesTransferred > 0)
            {
                if (!isRunning) return null;
                try
                {
                    OnDataReceive(e);
                }
                catch (Exception ex)
                {
                    logger.Error($"ReceiveFromAsync OnDataReceive caused exception {ex}");
                }
                if (!isRunning) return null;
            }
            else
            {
                if (!isRunning) return null;
                if (e.SocketError != SocketError.Success)
                {
                    logger.Warn($"Failed to receive data from socket {e.SocketError} {e.BytesTransferred} {e.LastOperation}");
                    onDisconnect(DisconnectReason.SocketError);
                    return null;
                }
            }

            try
            {
                bool willRaiseEvent = sock.ReceiveFromAsync(e);
                if (!willRaiseEvent) return e;
            }
            catch (Exception ex)
            {
                logger.Error($"ReceiveFromAsync caused exception {ex}");
                // 这个分支表示监听socket异常，无法继续监听了
                onDisconnect(DisconnectReason.SocketError);
            }
            return null;
        }

        private void OnDataReceive(SocketAsyncEventArgs e)
        {
            var bytesTransferred = e.BytesTransferred;
            if (bytesTransferred < 1) return;

            var segment = new ArraySegment<byte>(e.Buffer, e.Offset, e.BytesTransferred);
            var info = e.UserToken as UdpBuffer;
            onRawDataReceived(segment, info);
            UdpNetCommon.SetBuffer(e);
        }

        private void ReadCompleted(object sender, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.ReceiveFrom:
                    SocketAsyncEventArgs enext = e;
                    do
                    {
                        enext = ProcessReceive(enext);
                    }
                    while (enext != null);
                    break;
                default:
                    logger.Warn($"The last operation completed on the socket was not a ReceiveFrom {e.LastOperation}");
                    break;
            }
        }

        public void SendRawData(ArraySegment<byte> dataSection1, ArraySegment<byte> dataSection2)
        {
            if (sock == null)
            {
                logger.Error("SendRawData: socket is null");
                return;
            }

            var totalLength = dataSection1.Count + dataSection2.Count;
            if (totalLength > KcpConfig.ServerConfig.Mtu)
            {
                logger.Error($"SendRawData: data length {totalLength} exceed MTU {KcpConfig.ServerConfig.Mtu}");
                return;
            }
            var e = sendPool.Get();
            e.Completed += WriteCompleted;
            var rentMem = ArrayAppender.RentMemory(totalLength);
            if (!MemoryMarshal.TryGetArray(rentMem.Memory, out ArraySegment<byte> segment))
            {
                logger.Error("[SendRawData] MemoryMarshal.TryGetArray failed");
                return;
            }
            dataSection1.CopyTo(segment);
            if (dataSection2.Count > 0)
            {
                dataSection2.CopyTo(segment.Slice(dataSection1.Count));
            }
            e.SetBuffer(segment.Array, segment.Offset, totalLength);
            e.UserToken = rentMem;
            e.RemoteEndPoint = remote;
            bool willRaiseEvent = sock.SendToAsync(e);
            if (!willRaiseEvent)
            {
                OnDataSentCallback(e);
            }
            NetFlowCounter.Instance.SendRawData(totalLength);
        }

        public void SendRawData(ArraySegment<byte> data) => SendRawData(data, ArraySegment<byte>.Empty);

        protected void WriteCompleted(object _, SocketAsyncEventArgs e)
        {
            switch (e.LastOperation)
            {
                case SocketAsyncOperation.SendTo:
                    OnDataSentCallback(e);
                    break;
                default:
                    logger.Warn($"The last operation completed on the socket was not a SendTo {e.LastOperation}");
                    break;
            }
        }

        protected void OnDataSentCallback(SocketAsyncEventArgs e)
        {
            if (e.SocketError != SocketError.Success)
            {
                if (isRunning)
                {
                    logger.Warn($"SocketError while sending reason = {e.SocketError} {e.BytesTransferred} {e.LastOperation}");
                    onDisconnect(DisconnectReason.SocketError);
                }
            }

            var userToken = e.UserToken as IMemoryOwner<byte>;
            userToken?.Dispose();
            e.Completed -= WriteCompleted;
            e.UserToken = null;
            e.RemoteEndPoint = null;
            sendPool.Return(e);
        }

        public void Disconnect()
        {
            isRunning = false;
            sock?.Dispose();
        }
    }
}
