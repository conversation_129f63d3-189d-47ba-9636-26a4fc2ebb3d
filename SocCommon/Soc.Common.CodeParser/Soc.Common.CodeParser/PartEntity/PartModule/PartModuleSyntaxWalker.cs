using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace Soc.Common.CodeParser.PartModule
{
    public class PartModuleSyntaxWalker : CSharpSyntaxWalker
    {
        public string CurrentNamespace { get; set; }

        public Dictionary<string, List<PartModuleData>> PartModuleDataDic = new();

        public PartModuleData CurPartModuleData;

        public List<PartModuleData> PartModuleDatas = new List<PartModuleData>();

        public void StartVisit()
        {
            PartModuleDatas.Clear();
            CurrentNamespace = null;
        }

        public override void VisitNamespaceDeclaration(NamespaceDeclarationSyntax node)
        {
            CurrentNamespace = node.Name.ToString();
            base.VisitNamespaceDeclaration(node);

        }

        public override void VisitClassDeclaration(ClassDeclarationSyntax node)
        {
            //Console.WriteLine("Found class: " + node.Identifier.Text);
            CurPartModuleData = new();
            CurPartModuleData.ClassName = node.Identifier.Text;
            base.VisitClassDeclaration(node);
        }

        public override void VisitAttribute(AttributeSyntax node)
        {
            var attributeIdentifier = node.Name.ToString();
            //Console.WriteLine("Found Attribute: " + attributeIdentifier);
            // Check if the attribute is a custom attribute that you are interested in
            if (attributeIdentifier == "PartModule")
            {
                PartModuleDatas.Add(CurPartModuleData);
                if (!string.IsNullOrEmpty(CurrentNamespace))
                {
                    CurPartModuleData.Namespace = CurrentNamespace;
                }
            }

            base.VisitAttribute(node);
        }

        public override void VisitAttributeArgument(AttributeArgumentSyntax node)
        {
            // Access the data within the attribute argument
            var dataValue = node.Expression.ToString();

            //Console.WriteLine("Attribute argument value: " + dataValue);

            if (dataValue.Contains("PartModuleType"))
            {
                CurPartModuleData.PartType = dataValue;
            }

            if (dataValue.Contains("PlatformType"))
            {
                CurPartModuleData.Platform = dataValue;
            }

            base.VisitAttributeArgument(node);
        }

        public override void Visit(SyntaxNode node)
        {
            base.Visit(node);
        }

        public void EndVisit()
        {
            foreach (var partModuleData in PartModuleDatas)
            {
                if (!PartModuleDataDic.TryGetValue(partModuleData.Platform, out var datas))
                {
                    datas = new List<PartModuleData>();
                    PartModuleDataDic.Add(partModuleData.Platform, datas);
                }

                var data = datas.FirstOrDefault((data) => data.PartType == partModuleData.PartType);
                if (data == null)
                {
                    datas.Add(partModuleData);
                }
            }

        }

        public void Log()
        {
            foreach (var keyValuePair in PartModuleDataDic)
            {
                Console.WriteLine($"{keyValuePair.Key}");
                foreach (var partModuleData in keyValuePair.Value)
                {
                    Console.WriteLine($"{partModuleData.PartType} || {partModuleData.ClassName}");
                }
            }
        }

        private StringBuilder content = new();
        private List<string> nameSpaces = new();
        public string GetUsingContent(string platform)
        {
            content.Clear();
            nameSpaces.Clear();
            if (PartModuleDataDic.TryGetValue(platform, out var datas))
            {
                foreach (var partModuleData in datas)
                {
                    if (!string.IsNullOrEmpty(partModuleData.Namespace) && !nameSpaces.Contains(partModuleData.Namespace))
                    {
                        nameSpaces.Add(partModuleData.Namespace);
                    }
                }
            }
            if (PartModuleDataDic.TryGetValue("PlatformType.All", out var otherDatas))
            {
                foreach (var partModuleData in otherDatas)
                {
                    if (!string.IsNullOrEmpty(partModuleData.Namespace) && !nameSpaces.Contains(partModuleData.Namespace))
                    {
                        nameSpaces.Add(partModuleData.Namespace);
                    }
                }
            }

            foreach (var nameSpace in nameSpaces)
            {
                content.Append(
                     $"using {nameSpace};" + "\n");
            }
            return content.ToString();
        }

        public string GetContent(string platform)
        {
            content.Clear();
            if (PartModuleDataDic.TryGetValue(platform, out var datas))
            {
                foreach (var partModuleData in datas)
                {
                    content.Append(
                        $"                case {partModuleData.PartType} :" + "\n" +
                        $"                    moduleCommon = new {partModuleData.ClassName}();" + "\n" +
                        "                    break;" + "\n");
                }
            }

            if (PartModuleDataDic.TryGetValue("PlatformType.All", out var otherDatas))
            {
                foreach (var partModuleData in otherDatas)
                {

                    content.Append(
                        $"                case {partModuleData.PartType} :" + "\n" +
                        $"                    moduleCommon = new {partModuleData.ClassName}();" + "\n" +
                        "                     break; //PlatformType.All" + "\n");
                }
            }

            return content.ToString();
        }
    }
}