namespace Soc.Common.CodeParser.RuleGraph
{
    public class CustomTypeListTemplate
    {
        public const string VALUE = @"
using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.SocWorld.RuleGraph.Function
{
    [GraphFunctionLibrary(""{$$CamelTypeName}List"", Category = ""List"")]
    public static class X{$$CamelTypeName}ListFunctionLibrary
    {
        [GraphValue]
        public static CustomTypeList<{$$ItemTypeName}> CreateList([ConstDataIn] CustomTypeList<{$$ItemTypeName}> list)
        {
            return CustomTypeListUtils.SpecialCreateList(list);
        }

        /// <summary>
        /// 创建动态列表{$$ItemTypeName}
        /// </summary>
        /// <param name=""list""></param>
        /// <returns></returns>
        [GraphValue]
        public static CustomTypeList<{$$ItemTypeName}> CreateListDynamic([ConstDataIn][DynamicContainer] CustomTypeList<{$$ItemTypeName}> list)
        {
            return CustomTypeListUtils.SpecialCreateList(list);
        }

        [GraphValue]
        public static {$$ItemTypeName} Get(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name,
            long index
        )
        {
            return CustomTypeListUtils.Get<{$$ItemTypeName}>(context, name, index);
        }

        [GraphNode]
        public static void Set(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name,
            long index,
            {$$ItemTypeName} item)
        {
            CustomTypeListUtils.Set(context, name, index, item);
        }

        [GraphValue]
        // [GraphExpression(Template = ""len({{Name}})"")]
        public static long Count(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name)
        {
            return CustomTypeListUtils.Count<{$$ItemTypeName}>(context, name);
        }

        /// <summary>
        /// 添加元素到{$$CamelTypeName}列表(最多100个元素)
        /// 1. 拷贝值
        /// </summary>
        /// <param name=""context""></param>
        /// <param name=""name""></param>
        /// <param name=""item""></param>
        [GraphNode]
        public static void Add(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name,
            {$$ItemTypeName} item)
        {
            CustomTypeListUtils.Add(context, name, item);
        }

        // CustomType 没有重载 Equals 实现值比较
        /*
        [GraphNode]
        public static void Remove(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name,
            {$$ItemTypeName} item)
        {
            CustomTypeListUtils.Remove(context, name, item);
        }
        */

        [GraphNode]
        public static void RemoveAt(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name,
            long index)
        {
            CustomTypeListUtils.RemoveAt<{$$ItemTypeName}>(context, name, index);
        }

        [GraphNode]
        public static void Clear(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof({$$EditorTypeName}))]
            long name)
        {
            CustomTypeListUtils.Clear<{$$ItemTypeName}>(context, name);
        }
    }
}
";
    }
}