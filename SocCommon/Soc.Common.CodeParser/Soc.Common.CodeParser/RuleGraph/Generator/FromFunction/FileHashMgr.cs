using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;

namespace Soc.Common.CodeParser.RuleGraph.FromFunction
{
    /// <summary>
    /// 管理文件哈希值用于变更检测
    /// </summary>
    public class FileHashMgr
    {
        private Dictionary<string, string> fileHashes = new Dictionary<string, string>();
        private readonly string hashFilePath;

        public FileHashMgr(string hashFilePath)
        {
            this.hashFilePath = hashFilePath ?? throw new ArgumentNullException(nameof(hashFilePath));
            LoadHashes();
        }

        /// <summary>
        /// 检查文件自上次哈希存储后是否已更改
        /// </summary>
        public bool HasFileChanged(string filePath)
        {
            if (!File.Exists(filePath))
                return true;

            var currentHash = CalculateFileHash(filePath);
            return !fileHashes.TryGetValue(filePath, out var lastHash) || lastHash != currentHash;
        }

        /// <summary>
        /// 更新指定文件的哈希值
        /// </summary>
        public void UpdateFileHash(string filePath)
        {
            fileHashes[filePath] = CalculateFileHash(filePath);
        }

        /// <summary>
        /// 保存所有哈希值到文件
        /// </summary>
        public void SaveHashes()
        {
            File.WriteAllLines(hashFilePath, fileHashes.Select(kv => $"{kv.Key}|{kv.Value}"));
        }

        /// <summary>
        /// 清理不存在的文件的哈希记录
        /// </summary>
        public void CleanupOrphanedHashes(IEnumerable<string> validFiles)
        {
            var orphanedKeys = fileHashes.Keys.Except(validFiles).ToList();
            foreach (var key in orphanedKeys)
                fileHashes.Remove(key);
        }

        private void LoadHashes()
        {
            if (!File.Exists(hashFilePath))
                return;

            foreach (var line in File.ReadLines(hashFilePath))
            {
                var parts = line.Split('|');
                if (parts.Length == 2)
                    fileHashes[parts[0]] = parts[1];
            }
        }

        private static string CalculateFileHash(string filePath)
        {
            using var md5 = MD5.Create();
            using var stream = File.OpenRead(filePath);
            return BitConverter.ToString(md5.ComputeHash(stream)).Replace("-", "").ToLowerInvariant();
        }
        
        public bool HasFile(string filePath)
        {
            return fileHashes.ContainsKey(filePath);
        }
        
        /// <summary>
        /// 获取字符串的稳定哈希值（跨平台兼容）
        /// </summary>
        public static int GetStableHashCode(string str)
        {
            unchecked
            {
                int hash1 = 5381;
                int hash2 = hash1;

                for (int i = 0; i < str.Length && str[i] != '\0'; i += 2)
                {
                    hash1 = ((hash1 << 5) + hash1) ^ str[i];
                    if (i == str.Length - 1 || str[i + 1] == '\0')
                        break;
                    hash2 = ((hash2 << 5) + hash2) ^ str[i + 1];
                }

                return hash1 + (hash2 * 1566083941);
            }
        }
    }
}