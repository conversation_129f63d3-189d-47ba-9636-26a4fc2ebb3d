using System.Collections.Generic;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.Manager;

namespace WizardGames.Soc.Common.Component
{
    public static partial class TerritoryPermissionComponentHotfix
    {
        public static bool IsSuperAdmin(this TerritoryPermissionComponent self, ulong roleId)
        {
            var selfBase = self.TerrEnt?.BaseComp;
            if (selfBase != null && selfBase.CreatorRoleId == roleId)
                return true;
            if (self.IsAdmin(roleId))
                return true;
            return false;
        }
        public static bool IsAdmin(this TerritoryPermissionComponent self, ulong roleId)
        {
            if (self.Admins.ContainsKey(roleId))
                return true;
            return false;
        }
        public static bool IsMember(this TerritoryPermissionComponent self, ulong roleId)
        {
            foreach (var (_, group) in self.Groups)
            {
                if (group.IsMember(roleId))
                    return true;
            }
            return false;
        }
        public static bool IsMember(this TerritoryPermissionComponent self, ulong roleId, int groupId)
        {
            if (self.Groups.TryGetValue(groupId, out var group))
            {
                return group.IsMember(roleId);
            }
            return false;
        }

        public static bool HasAnyPermission(this TerritoryPermissionComponent self, ulong roleId)
        {
            return self.IsMember(roleId) || self.IsSuperAdmin(roleId);
        }
        public static bool HasSpecialPermission(this TerritoryPermissionComponent self, ulong roleId, int permissionGroupId)
        {
            var selfBase = self.TerrEnt.BaseComp;
            if (roleId == selfBase.CreatorRoleId)
                return true;
            if (self.Groups.TryGetValue(permissionGroupId, out var group))
            {
                return group.IsMember(roleId);
            }
            return false;
        }
#if !SOC_SIMULATOR

        public static void GetPermissionGroupIds(this TerritoryPermissionComponent self, ulong roleId, ref List<int> groupIds)
        {
            foreach (var (groupId, group) in self.Groups)
            {
                if (group.IsMember(roleId))
                    groupIds.Add(groupId);
            }
        }
        public static bool HasSinglePermission(this TerritoryPermissionComponent self, ulong roleId, int permUnitId)
        {
            var selfBase = self.TerrEnt?.BaseComp;
            if (selfBase != null && roleId == selfBase.CreatorRoleId)
                return true;
            foreach (var (_, group) in self.Groups)
            {
                if (group.IsMember(roleId))
                {
                    var mask = BuildingPrivilegeHelper.CalcMask(permUnitId);
                    PermissionConst.PermissionGroupMask.TryGetValue(group.Id, out var groupMask);
                    if ((mask & groupMask) == mask)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

#endif
#if SOC_WORLD

#endif

        public static HashSet<ulong> GetAllMemebers(this TerritoryPermissionComponent self)
        {
            HashSet<ulong> result = new HashSet<ulong>();
            foreach (var (_, group) in self.Groups)
            {
                result.UnionWith(group.Teammates.Keys);
                result.UnionWith(group.OtherMembers.Keys);
            }
            result.UnionWith(self.Admins.Keys);
            return result;
        }

#if !SOC_SIMULATOR
        public static bool HasShowBriefInfoPrivilege(this TerritoryPermissionComponent self, ulong roleId)
        {
            if (roleId == self.TerrEnt.BaseComp.CreatorRoleId) return true;
            var groupsHavePrivilege = McCommon.Tables.TbPermissionConstant.ShowLabelPermGrpId;
            if (groupsHavePrivilege.Count <= 0) return false;
            if (groupsHavePrivilege.Count == 1) return self.Groups.TryGetValue(groupsHavePrivilege[0], out var group) && group.IsMember(roleId);
            foreach (var groupId in groupsHavePrivilege)
            {
                if (!self.Groups.TryGetValue(groupId, out var group)) return false;
                if (!group.IsMember(roleId)) return false;
            }
            return true;
        }
#endif
    }
}
