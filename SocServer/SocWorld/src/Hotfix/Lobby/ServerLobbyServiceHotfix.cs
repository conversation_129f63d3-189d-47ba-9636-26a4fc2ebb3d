using Newtonsoft.Json;
using SimpleJSON;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.Util;

namespace WizardGames.Soc.SocWorld
{
    [HotfixClass]
    public static partial class ServerLobbyServiceHotfix
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(ServerLobbyService));

        public static void SendGetRoleData(this ServerLobbyService self, long entityId, string roleId, Action<JSONNode, HttpStatusCode, int> onCallback = null)
        {
            BattelGetRoleData message = new BattelGetRoleData
            {
                roleId = roleId,
            };

            var notifyContent = JsonConvert.SerializeObject(message);
            HttpContent content = new StringContent(notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            self.serviceManager.PostToLobby("/inner/warzoneservice/role", content, ETargetThreadType.Logic, onCallback);
            logger.Info("send get role data to lobby");
        }

        /// <summary>
        /// sceneId:
        /// 1001：聊天
        /// 101： 名称更改
        /// 4001：玩家搜索
        /// </summary>
        [Hotfix(NeedWrapper = true)]
        public static void MinganciTest(this ServerLobbyService self, string text, int sceneId, ulong roleId, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            JSONObject obj = new();
            obj.Add("roleID", roleId);
            obj.Add("sceneID", sceneId);
            obj.Add("text", text);
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/diplomatist/judgetext", content, ETargetThreadType.Logic, onCallback);
        }

        private static List<PlayScoreRankItemData> rankItemDatas = new();
        private static PlayScoreRankData rankData = new();
        [Hotfix(NeedWrapper = true)]
        public static void SendRankItemData(this ServerLobbyService self, long id, long rankScore, string rankContent)
        {
            rankItemDatas.Clear();
            rankItemDatas.Add(new PlayScoreRankItemData()
            {
                uniqueID = id.ToString(),
                score = rankScore,
                data = rankContent,
            });

            rankData.boardJsonData = JsonConvert.SerializeObject(rankItemDatas);

            var notifyContent = JsonConvert.SerializeObject(rankData);
            HttpContent content = new StringContent(notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            self.serviceManager.PostToLobby("/inner/rank/setscoreanddata", content, ETargetThreadType.Main, null);
        }

        public static void SendLoadReputaionReplacements(this ServerLobbyService self, long entityId, ulong roleId, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            Dictionary<string, string> content = new() { { "roleID", roleId.ToString() } };
            LobbyServiceManager.Instance.GetFromLobby("/inner/playergrowth/reputationreward/myreward", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendSyncTaskFromLobby(this ServerLobbyService self, ulong roleId,
            Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            Dictionary<string, string> content = new() { { "roleID", roleId.ToString() } };
            LobbyServiceManager.Instance.GetFromLobby("/inner/task/pull/taskgroup", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendFinishTaskToLobby(this ServerLobbyService self, ulong roleId, int groupId, long taskVersion, long taskId,
            Action<JSONNode, HttpStatusCode, int, long> onCallback)
        {
            var data = new TaskServiceInnerTasksFinishedReq()
            {
                RoleId = (long)roleId,
                DsFinishedTaskGroups =
                    [new TaskServiceGroupInfo { GroupId = groupId, Version = taskVersion, TaskIds = [(int)taskId] }],
            };
            var notifyContent = JsonConvert.SerializeObject(data);
            logger.InfoFormat("[SendFinishTaskToLobby] server to lobby, info:{0}", notifyContent);
            HttpContent content = new StringContent(notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/task/update/finished", content, ETargetThreadType.Logic,
                (jsonNode, statusCode, errorCode) =>
                {
                    onCallback?.Invoke(jsonNode, statusCode, errorCode, taskId);
                });
        }

        public static void SendSyncTaskToLobby(this ServerLobbyService self, TaskServiceInnerTaskGroup data,
            Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            var notifyContent = JsonConvert.SerializeObject(data);
            HttpContent content = new StringContent(notifyContent);
            logger.InfoFormat("[SendSyncTaskToLobby] server to lobby, info:{0}", notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/task/push/taskgroup", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendUnlockReputaionReward(this ServerLobbyService self, long entityId, ulong roleId, List<int> rewardIds, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            var data = new UnlockReputationRewardData()
            {
                roleID = roleId.ToString(),
                rewardIDs = rewardIds,
            };
            var notifyContent = JsonConvert.SerializeObject(data);
            HttpContent content = new StringContent(notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/reputationreward/unlock", content, ETargetThreadType.Logic, onCallback);
        }

        [Hotfix(NeedWrapper = true)]
        public static void SendPlayerMailToLobby(this ServerLobbyService self, List<ulong> playerRoleIds, int templateId, string mailType)
        {
            var data = new SendMailData()
            {
                roleId = new List<string>(),
                request = new NewMailData { TemplateID = templateId, mailType = mailType }
            };

            foreach (var playerRoleId in playerRoleIds)
            {
                data.roleId.Add(playerRoleId.ToString());
            }

            var notifyContent = JsonConvert.SerializeObject(data);
            HttpContent content = new StringContent(notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/mail/send", content, ETargetThreadType.Logic, null);
        }

        public static void SendTeamInviteToLobby(this ServerLobbyService self, long entityId, InviteFromBattleReq message, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            var notifyContent = JsonConvert.SerializeObject(message);
            HttpContent content = new StringContent(notifyContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            self.serviceManager.PostToLobby("/inner/teamservice/invitefrombattle", content, ETargetThreadType.Logic, onCallback);
            logger.Info($"send team invite to lobby {message.senderID} {message.receiverID}");
        }

        public static void SendPersonalMessageToLobby(this ServerLobbyService self, ulong sender, ulong receiver, string msg)
        {
            var pm = new PersonalMessage() { sender = sender.ToString(), receiver = receiver.ToString(), msg = msg };
            var msgContent = JsonConvert.SerializeObject(pm);
            HttpContent content = new StringContent(msgContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/chatservice/personalmessage", content, ETargetThreadType.None, null);
        }

        public static void SendLobbyReward(this ServerLobbyService self, long entityId, ulong sender, Dictionary<long, int> rewards, string reason, Action<JSONNode, HttpStatusCode, int, long> onCallback)
        {
            var srrd = new SendLobbyRewardData();
            List<Resources> resources = new();
            srrd.roleId = sender.ToString();
            srrd.reason = reason;
            foreach (var (rid, amount) in rewards)
            {
                var rs = new Resources() { amount = amount, rid = rid };
                resources.Add(rs);
            }
            srrd.resources = resources;
            var msgContent = JsonConvert.SerializeObject(srrd);
            HttpContent content = new StringContent(msgContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/resource/add", content, ETargetThreadType.Logic, (jsonNode, statusCode, errorCode) =>
            {
                onCallback?.Invoke(jsonNode, statusCode, errorCode, entityId);
            });
        }

        public static void SendLobbyRewardFatigue(this ServerLobbyService self, long entityId, ulong sender, string reason, int fatigueId, int rewardFatigue, Action<JSONNode, HttpStatusCode, int, long> onCallback)
        {
            var srrd = new DelLobbyResource();
            List<Resources> resources = new();
            srrd.roleId = sender.ToString();
            srrd.reason = reason;
            var rs = new Resources() { amount = rewardFatigue, rid = fatigueId };
            resources.Add(rs);
            srrd.resources = resources;
            var msgContent = JsonConvert.SerializeObject(srrd);
            HttpContent content = new StringContent(msgContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/resource/del", content, ETargetThreadType.Logic, (jsonNode, statusCode, errorCode) =>
            {
                onCallback?.Invoke(jsonNode, statusCode, errorCode, entityId);
            });
        }

        public static void SendRefreshLobbyLevel(this ServerLobbyService self, long entityId, ulong roleId, Action<JSONNode, HttpStatusCode, int, long> onCallback)
        {
            Dictionary<string, string> message = new() { { "roleID", roleId.ToString() } };
            var msgContent = JsonConvert.SerializeObject(message);
            HttpContent content = new StringContent(msgContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playerservice/pull/level", content, ETargetThreadType.Logic, (jsonNode, statusCode, errorCode) =>
            {
                onCallback?.Invoke(jsonNode, statusCode, errorCode, entityId);
            });
        }

        public static void SendLoadFriendData(this ServerLobbyService self, long entityId, ulong roleId, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            Dictionary<string, string> content = new()
            {
                { "roleID", roleId.ToString() }
            };
            LobbyServiceManager.Instance.GetFromLobby("/inner/social/pull/friend", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendSaveRecentFriendList(this ServerLobbyService self, ulong roleId, CustomValueDictionary<ulong, RecentFriend> friends, HashSet<ulong> removeList)
        {
            SendSaveRecentFriendsData recentFriendsData = new()
            {
                roleID = roleId.ToString(),
                removeList = new(),
                players = new(),
            };

            foreach (var (friendId, friendData) in friends)
            {
                var recent = new RecentFriendData()
                {
                    roleID = friendId.ToString(),
                    lastModifyTime = friendData.LastTs.ToString(),
                    reason = new(),
                };
                foreach (var reason in friendData.Reason)
                {
                    recent.reason.Add(reason);
                }
                recentFriendsData.players.Add(recent);
            }

            foreach (var removeId in removeList)
            {
                recentFriendsData.removeList.Add(removeId.ToString());
            }

            var dataContent = JsonConvert.SerializeObject(recentFriendsData);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            logger.InfoFormat("[SendSaveRecentFriendList] server to lobby, info:{0}", dataContent);
            self.serviceManager.PostToLobby("/inner/social/push/recentlist", content, ETargetThreadType.Logic, (jsonNode, statusCode, errorCode) =>
            {
                if (statusCode != HttpStatusCode.OK || errorCode != 0)
                    logger.ErrorFormat("[SendSaveRecentFriendList] callback statusCode:{0}, code:{1}", statusCode, errorCode);
                else
                    logger.InfoFormat("[SendSaveRecentFriendList] callback content:{0}", jsonNode);

            });
        }

        public static void SendSaveIntimacyList(this ServerLobbyService self, ulong roleId, BasicValueDictionary<ulong, float> intimacys)
        {
            SendSaveIntimacysData intimacyData = new()
            {
                roleID = roleId.ToString(),
                intimacyList = new(),
                IsBothInc = false,
            };

            foreach (var (friendId, value) in intimacys)
            {
                var friendIntimacy = new FriendIntimacy()
                {
                    roleID = friendId.ToString(),
                    intimacy = value
                };
                intimacyData.intimacyList.Add(friendIntimacy);
            }

            var dataContent = JsonConvert.SerializeObject(intimacyData);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            logger.InfoFormat("[SendSaveIntimacyList] server to lobby, info:{0}", dataContent);
            self.serviceManager.PostToLobby("/inner/social/push/intimacy", content, ETargetThreadType.Logic, (jsonNode, statusCode, errorCode) =>
            {
                if (statusCode != HttpStatusCode.OK || errorCode != 0)
                    logger.ErrorFormat("[SendSaveIntimacyList] callback statusCode:{0}, code:{1}", statusCode, errorCode);
                else
                    logger.InfoFormat("[SendSaveIntimacyList] callback content:{0}", jsonNode);
            });
        }

        public static void StarSkin(this ServerLobbyService self, ulong roleId, long skinId, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            JSONObject obj = new();
            obj.Add("roleID", roleId.ToString());
            obj.Add("skinID", skinId);
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/equipment/starskin", content, ETargetThreadType.Logic, onCallback);
            logger.Info($"star skin roleId {roleId} skinId {skinId}");
        }

        public static void UnStarSkin(this ServerLobbyService self, ulong roleId, long skinId, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            JSONObject obj = new();
            obj.Add("roleID", roleId.ToString());
            obj.Add("skinID", skinId);
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/equipment/unstarskin", content, ETargetThreadType.Logic, onCallback);
            logger.Info($"unstar skin roleId {roleId} skinId {skinId}");
        }

        /// <summary>
        /// APP推送消息
        /// </summary>
        public static void PushAppMessage(this ServerLobbyService self, ulong roleId, int tipId, string[] args = null, Action<JSONNode, HttpStatusCode, int> onCallback = null, string customData = null)
        {
            var config = McCommon.Tables.TBPushMessageContent.GetOrDefault(tipId);
            if (config == null)
            {
                logger.Warn($"TbAppPushTip not found {tipId} please check config");
                return;
            }

            var languageId = TipUtil.ELanguageId.Chs;
            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (player != null)
            {
                languageId = (TipUtil.ELanguageId)player.LanguageId;
            }

            string title, context;
            switch (languageId)
            {
                case TipUtil.ELanguageId.Chs:
                    title = config.CNTitle;
                    context = config.CNText;
                    break;
                default:
                    title = config.ENTitle;
                    context = config.ENText;
                    break;
            }
            if (null != args && args.Length > 0)
            {
                context = string.Format(context, args);
            }
            string customJson = null;
            // 支持自定义数据 覆盖表里数据
            if (customData != null)
            {
                customJson = customData;
            }
            AppPushMessageData message = new()
            {
                roleIDs = new() { roleId.ToString() },
                message = new()
                {
                    title = title,
                    content = context,
                    customContent = customJson,
                }
            };
            var dataContent = JsonConvert.SerializeObject(message);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            if (onCallback == null)
            {
                onCallback = (jsonNode, statusCode, errorCode) =>
                {
                    if (statusCode != HttpStatusCode.OK || errorCode != 0)
                    {
                        logger.ErrorFormat("[PushAppMessage] callback statusCode:{0}, code:{1}, roleId:{2}, tipId:{3}", statusCode, errorCode, roleId, tipId);
                    }
                    else
                    {
                        logger.InfoFormat("[PushAppMessage] callback roleId:{0}, tipId:{1}, content:{2}", roleId, tipId, jsonNode);
                    }
                };
            }
            self.serviceManager.PostToLobby("/inner/diplomatist/pushmessage", content, ETargetThreadType.Logic, onCallback);
            logger.InfoFormat("[PushAppMessage] server to lobby, info:{0}", dataContent);
        }

        private static string GenerateMD5Hash(string input, bool toUpper = false)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2")); // 将字节转换为两位16进制形式
                }

                return toUpper ? sb.ToString().ToUpper() : sb.ToString();
            }
        }

        /// <summary>
        /// 批量同步收藏和取消收藏操作给大厅
        /// {
        ///   "roleID": "string",
        ///   "starredSkinIDs": [
        ///     0
        ///   ],
        ///   "unStarredSkinIDs": [
        ///     0
        ///   ]
        /// }
        /// </summary>
        /// <param name="self"></param>
        /// <param name="roleId"></param>
        /// <param name="onCallback"></param>
        public static void SyncStarInfoToLobby(this ServerLobbyService self, ulong roleId, BasicTypeList<long> starredSkinIDs, BasicTypeList<long> unStarredSkinIDs, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            JSONObject obj = new();
            obj.Add("roleID", roleId.ToString());
            var starredSkinIDsArray = new JSONArray();
            foreach (var skinID in starredSkinIDs)
            {
                starredSkinIDsArray.Add(skinID);
            }
            obj.Add("starredSkinIDs", starredSkinIDsArray);
            var unStarredSkinIDsArray = new JSONArray();
            foreach (var skinID in unStarredSkinIDs)
            {
                unStarredSkinIDsArray.Add(skinID);
            }
            obj.Add("unStarredSkinIDs", unStarredSkinIDsArray);
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/equipment/batchsyncstar", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendRecruitmentToLobby(this ServerLobbyService self, TeamRecruitInfo info, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            var dataContent = JsonConvert.SerializeObject(info);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/teamservice/battlerecruitment/publishtolobby", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendRecruitmentApplicationResponseToLobby(this ServerLobbyService self, TeamRecruitResponse info, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            var dataContent = JsonConvert.SerializeObject(info);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/teamservice/battlerecruitment/handleapplication", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendGetLobbyMainTaskRewardReq(this ServerLobbyService self, ulong roleId, int groupId, long taskId, long taskVersion, Action<JSONNode, HttpStatusCode, int, long> onCallback)
        {
            JSONObject obj = new();
            obj.Add("roleID", roleId.ToString());
            obj.Add("groupID", groupId);
            obj.Add("taskID", taskId);
            obj.Add("version", taskVersion.ToString());
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/task/reward", content, ETargetThreadType.Logic, (jsonNode, statusCode, errorCode) =>
            {
                onCallback?.Invoke(jsonNode, statusCode, errorCode, taskId);
            });
        }

        public static void RegisterPhotoTaskToLobby(this ServerLobbyService self, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            JSONObject obj = new();
            obj.Add("serverID", ProcessEntity.Instance.ServerId);
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/phototaskservice/register", content, ETargetThreadType.Logic, onCallback);
        }

        public static void UnregisterPhotoTaskToLobby(this ServerLobbyService self, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            JSONObject obj = new();
            obj.Add("serverID", ProcessEntity.Instance.ServerId);
            HttpContent content = new StringContent(obj.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/phototaskservice/unregister", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendGameEndResultToLobby(this ServerLobbyService self, WarzoneServiceReportBattleEndSummaryReq req, Action<JSONNode, HttpStatusCode, int> onCallback)
        {
            var dataContent = JsonConvert.SerializeObject(req);
            logger.InfoFormat("[SendGameEndResultToLobby] server to lobby, info:{0}", dataContent);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/warzoneservice/battlesummary", content, ETargetThreadType.Logic, onCallback);
        }

        public static void SendSettleStyleRankPointsToLobby(this ServerLobbyService self, List<PlayerGrowthServiceSettleStyleRankPointsInfo> playerInfoList, Action<JSONNode, HttpStatusCode, int> onCallback = null)
        {
            var gameMode = ServerInstanceEntity.Instance?.GameModeId ?? 0;

            var request = new PlayerGrowthServiceInnerSettleStyleRankPointsReq
            {
                GameMode = gameMode,
                IsLastSettle = false,
                FullSettleStyleRankPointsInfoList = null,
                SettleStyleRankPointsInfoList = playerInfoList,
                BattleServerId = ProcessEntity.Instance.ServerId
            };

            var dataContent = JsonConvert.SerializeObject(request);
            logger.InfoFormat("[SendSettleStyleRankPointsToLobby] server to lobby, GameMode: {0}, IsLastSettle: false, PlayerCount: {1}, info: {2}", gameMode, playerInfoList?.Count ?? 0, dataContent);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/style/settle", content, ETargetThreadType.Logic, onCallback);
        }

        /// <summary>
        /// 发送全量勋章风格积分数据到大厅
        /// </summary>
        /// <param name="self">服务实例</param>
        /// <param name="playerInfoList">玩家信息列表</param>
        /// <param name="onCallback">回调函数</param>
        [Hotfix]
        public static void SendFullStyleRankPointsToLobby(this ServerLobbyService self, List<PlayerGrowthServiceSettleStyleRankPointsInfo> playerInfoList, Action<JSONNode, HttpStatusCode, int> onCallback = null)
        {
            var gameMode = ServerInstanceEntity.Instance?.GameModeId ?? 0;

            // 转换为全量数据格式
            var fullInfoList = new List<PlayerGrowthServiceFullSettleStyleRankPointsInfo>();
            foreach (var playerInfo in playerInfoList)
            {
                var fullInfo = new PlayerGrowthServiceFullSettleStyleRankPointsInfo
                {
                    RoleID = playerInfo.RoleID,
                    StyleRankPoints = new Dictionary<int, int>(playerInfo.AddStyleRankPointsMap)
                };
                fullInfoList.Add(fullInfo);
            }

            var request = new PlayerGrowthServiceInnerSettleStyleRankPointsReq
            {
                GameMode = gameMode,
                IsLastSettle = true, // 全量同步标志
                FullSettleStyleRankPointsInfoList = fullInfoList,
                SettleStyleRankPointsInfoList = playerInfoList,
                BattleServerId = ProcessEntity.Instance.ServerId
            };

            var dataContent = JsonConvert.SerializeObject(request);
            logger.InfoFormat("[SendFullStyleRankPointsToLobby] server to lobby, GameMode: {0}, IsLastSettle: true, PlayerCount: {1}, info: {2}", gameMode, playerInfoList?.Count ?? 0, dataContent);
            HttpContent content = new StringContent(dataContent);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/style/settle", content, ETargetThreadType.Logic, onCallback);
        }
        
        /// <summary>
        /// 跳过新手关并完成指定的任务
        /// </summary>
        /// <param name="self"></param>
        /// <param name="roleId"></param>
        /// <param name="onCallback"></param>
        public static void SendFinishNewbieTaskToLobby(this ServerLobbyService self, ulong roleId,
            Action<JSONNode, HttpStatusCode, int> onCallback = null)
        {
            JSONObject req = new();
            req.Add("roleID", roleId.ToString());

            HttpContent content = new StringContent(req.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/finishfreshmantaskbatch", content, ETargetThreadType.Logic,
                onCallback);
        }

        /// <summary>
        /// 获取勋章结算开关
        /// </summary>
        /// <param name="self"></param>
        /// <param name="roleId"></param>
        /// <param name="onCallback"></param>
        public static void GetMedalSettleSwitchFromLobby(this ServerLobbyService self, ulong roleId,
            Action<JSONNode, HttpStatusCode, int> onCallback = null)
        {
            JSONObject req = new();
            req.Add("roleID", roleId.ToString());

            HttpContent content = new StringContent(req.ToString());
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            self.serviceManager.PostToLobby("/inner/playergrowth/style/settleswitch", content, ETargetThreadType.Logic,
                onCallback);
        }
    }
}
