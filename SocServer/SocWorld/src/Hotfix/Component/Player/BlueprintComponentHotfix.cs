using MessagePack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.CustomType.RootNode;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.NodeSystem.VirtualNode;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;


namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    public static partial class BlueprintComponentHotfix
    {
        static BlueprintComponentHotfix()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<OnSoftDisconnectEndEvent>((pe, _) =>
            {
                BluePrintManagerEntity.Instance.PlayerOffline(pe);
                pe.ComponentBlueprint.StopEditElectricBlueprintInner();
            });
        }

        [Hotfix]
        public static void Init(this BlueprintComponent self)
        {
            self.UnlockBluePrints = new();
            self.UnlockTechIds = new();
            self.SystemRoot = new BlueprintRootNode();
            self.TechSystemRoot = new TechnologyTreeRootNode();
            self.Root.RegisterSystemRootNode(self.SystemRoot);
            self.Root.RegisterSystemRootNode(self.TechSystemRoot);

            self.DelayExecute(() =>
            {
                foreach (var item in BluePrintConst.DefaultBluePrintInfo)
                {
                    var bpId = item.Key;
                    var bpCnt = item.Value;

                    if (self.BluePrintIsUnlock(bpId))
                    {
                        self.Logger.Warn($"[AddDefaultBlueprints] bpId:{bpId} already unlock");
                        continue;
                    }
                    var code = self.Root.MergeNode(bpId, bpCnt);
                    if(code != EOpCode.Success)
                    {
                        self.Logger.Warn($"[AddDefaultBlueprints] bpId:{bpId} MergeNode fail {code}");
                    }
                }
            });
        }

        [Hotfix]
        private static void InitFromDb(this BlueprintComponent self)
        {
            self.Root.RegisterSystemRootNode(self.SystemRoot);
            self.Root.RegisterSystemRootNode(self.TechSystemRoot);
        }


        public static void GetElectricBlueprintCallback(this BlueprintComponent self, long itemId, ISerializeType blueprint)
        {
            var blueprintData = blueprint as Alpha3ElectricIntegratedCircuit;
            if (blueprintData == null)
            {
                self.Logger.Error($"GetElectricBlueprintCallback blueprintData is null {itemId}");
                return;
            }

            using (var writer = CustomTypeHelper.GetDisposableArrayWriter())
            {
                var msgWriter = new MessagePackWriter(writer);
                blueprintData.SerializeCore(ref msgWriter, ESerializeMode.All, blueprintData.GetClassHash());
                msgWriter.Flush();
                var bytes = writer.GetSequence();
                self.RemoteCallGetElectricBlueprintAck(ERpcTarget.OwnClient, itemId, bytes);
            }
        }

        #region rpc
        /// <summary>
        /// todo@bxl 校验itemId
        /// </summary>
        /// <param name="itemId"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void GetElectricBlueprint(this BlueprintComponent self, long itemId)
        {
            BluePrintManagerEntity.Instance.GetElectricIntegratedCircuitByItemId(itemId, self.GetElectricBlueprintCallback);
        }

        //public override void ElectricCmd(int seq, ElectricCmdBase cmd)
        //{
        //    if (cmd == null)
        //    {
        //        self.Logger.Error($"ElectricCmd cmd is null {seq} {Id}");
        //        RCallElectricCmdAck(seq, (int)OpCode.ArgsError);
        //        return;
        //    }

        //    if (OperatingBlueprintId == 0)
        //    {
        //        self.Logger.Error($"ElectricCmd OperatingBlueprintId is 0 {seq} {Id} {cmd.GetType().Name}");
        //        RCallElectricCmdAck(seq, (int)OpCode.NotOperateBlueprint);
        //        return;
        //    }

        //    if (!cmd.OfflineVerify())
        //    {
        //        self.Logger.Warn($"ElectricCmd OfflineVerify failed {seq} {Id} {cmd.GetType().Name}");
        //        RCallElectricCmdAck(seq, (int)OpCode.OfflineVerifyFailed);
        //        return;
        //    }
        //    electricCmdQueue.Enqueue((seq, cmd));
        //}
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void StorageElectricBlueprint(this BlueprintComponent self, List<long> storageList, string name)
        {
            var eic = ConstructionManagerEntity.Instance.CreateElectricIntegratedCircuit(storageList, name);
            eic.Name = name;
            using var _ = NodeOpContext.GetNew("StorageElectricBlueprint").SetOpRoleId(self.RoleId);
            self.AddBlueprintItem(eic);
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void StartEditElectricBlueprint(this BlueprintComponent self, long itemId)
        {
            if (self.OperatingBlueprintId != 0)
            {
                self.Logger.Error($"StartEditElectricBlueprint OperatingBlueprintId != 0 {self.OperatingBlueprintId} {itemId}");
                return;
            }

            var itemNode = self.Root.GetNodeById(itemId);
            if (itemNode == null)
            {
                self.Logger.Error($"StartEditElectricBlueprint itemNode is null {itemId}");
                return;
            }

            BluePrintManagerEntity.Instance.GetElectricIntegratedCircuitByItemId(itemId, (id, data) =>
            {
                var blueprintData = data as Alpha3ElectricIntegratedCircuit;
                self.OperatingBlueprintId = blueprintData.Id;
            });
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void StopEditElectricBlueprint(this BlueprintComponent self, long blueprintId)
        {
            if (self.OperatingBlueprintId != blueprintId)
            {
                self.Logger.Warn($"StopEditElectricBlueprint OperatingBlueprintId != blueprintId {self.OperatingBlueprintId} {blueprintId}");
                return;
            }

            self.StopEditElectricBlueprintInner();
        }
        #endregion

        #region memFunc
        public static void AddBlueprintItem(this BlueprintComponent self, Alpha3ElectricIntegratedCircuit eic)
        {
            var code = self.Root.MergeNode(new NodeOpByBizId(ItemConst.ElectricBlueprintItemId, 1));
            // todo@bxl 使用事务
            if (code != EOpCode.Success)
            {
                self.Logger.Error($"AddBlueprintItem MergeNode failed {code} {self.ParentId} {eic.Id}");
                return;
            }
            var node = NodeOpContext.GetCurrent().AcquireList.Last();
            BluePrintManagerEntity.Instance.AddBlueprintData(node.Id, eic);
        }
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void StopEditElectricBlueprintInner(this BlueprintComponent self)
        {
            if (self.OperatingBlueprintId == 0) return;
            //BluePrintManagerEntity.Instance.GetElectricIntegratedCircuit(OperatingBlueprintId, (id, data) =>
            //{
            //    var blueprintData = data as ElectricIntegratedCircuit;
            //    while (electricCmdQueue.Count > 0)
            //    {
            //        var (seq, cmd) = electricCmdQueue.Dequeue();
            //        if (!cmd.OnlineVerify_IC(blueprintData))
            //        {
            //            self.Logger.Error($"StopEditElectricBlueprintInner OnlineVerify_IC failed {seq} {cmd.GetType().Name}");
            //            break;
            //        }
            //        cmd.ApplyCmd_IC(blueprintData);
            //    }
            //    BluePrintManagerEntity.Instance.UpdateBlueprint(id, blueprintData);
            //});

            self.OperatingBlueprintId = 0;
        }
        #endregion
        public static void UseBlueprintItem(this BlueprintComponent self, ItemConfig config, BaseItemNode item)
        {
            if (!config.BlueprintIds.Any())
            {
                self.Logger.Error($"item:{config.Id}, no have blueprint ids");
                return;
            }

            var allUnlock = true;
            foreach (var bpId in config.BlueprintIds)
            {
                if(!self.BluePrintIsUnlock(bpId))
                {
                    allUnlock = false;
                    break;
                }
            }

            if (allUnlock)
            {
                self.Logger.Info($"id:{config.Id}, all blue print already unlock");
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.CannotUnlockAgain);
                return;
            }

            using var transaction = EntityTransaction.Start($"UseItem.UnlockBlueprint-{self.RoleId}-{item}");
            {
                transaction.IsLogInfoWhenRollback = true;
                var ctx = NodeOpContext.GetCurrent().SetTriggerBeltAutoSupply(true);
                var ret = item.RequireSelf();
                if (ret != EOpCode.Success)
                {
                    transaction.Rollback(OpCodeReason.Reason[ret]);
                    self.Player.RemoteCallPopMessageWithSingleParam(ERpcTarget.OwnClient, 0, 9000, new Alpha3PopMsgParam(config.Name_l10n_index));
                    return;
                }
                foreach (var bpId in config.BlueprintIds)
                {
                    ret = self.Root.MergeNode(bpId, 1);
                    if (ret != EOpCode.Success)
                    {
                        transaction.Rollback(OpCodeReason.Reason[ret]);
                        self.Player.RemoteCallPopMessageWithSingleParam(ERpcTarget.OwnClient, 0, 9000, new Alpha3PopMsgParam(config.Name_l10n_index));
                        return;
                    }
                }
                transaction.Commit();
            }
            self.RemoteCallOnUseBlueprintItem(ERpcTarget.OwnClient, item.BizId, 1);
        }

        /// <summary>
        /// 蓝图操作
        /// </summary>
        /// <param name="bpId">道具ID, 接口内部自动转换为蓝图ID</param>
        /// <param name="opType">1:解锁;2:收藏;3:取消收藏</param>
        [RpcHandler(ExposeToClient = true, CallInterval = .2f)]
        public static void BluePrintOp(this BlueprintComponent self, long bpId, int opType)
        {
            EBlueprintOpType enumOpType = (EBlueprintOpType)opType;
            if (!Enum.IsDefined(enumOpType))
            {
                self.Logger.InfoFormat("player:{0}, opType:{1}, not define op type", self.RoleId, enumOpType);
                return;
            }

            if(!self.UnlockBluePrints.TryGetValue(bpId,out var bpNode))
            {
                self.Logger.InfoFormat("player:{0}, id:{1}, not unlock blueprint", self.RoleId, bpId);
                return;
            }

            if (!bpNode.CanOp(enumOpType))
            {
                self.Logger.InfoFormat("player:{0}, id:{1}, opType:{2} fail to CanOp", self.RoleId, bpId, enumOpType);
                return;
            }
            if (enumOpType == EBlueprintOpType.Collect)
            {
                if (self.BluePrintCollectCount < McCommon.Tables.TbGlobalConfig.CraftCommonBlueprintCount)
                {
                    self.BluePrintCollectCount += 1;
                }
                else
                {
                    self.Logger.InfoFormat("player:{0}, id:{1}, BluePrintCollectCount:{2} fail to collect", self.RoleId, bpId, self.BluePrintCollectCount);
                    return;
                }
            }
            if (enumOpType == EBlueprintOpType.UnCollect)
            {
                self.BluePrintCollectCount -= 1;
            }

            bpNode.ExcuteOp(enumOpType);
            self.RemoteCallBlueprintOpResponse(ERpcTarget.OwnClient);
        }

        private static Dictionary<long, int> unlockScrapCost = new();
        private static Dictionary<long, int> costItemInfo = new();
        /// <summary>
        /// 解锁科技
        /// </summary>
        /// <param name="bizIds">科技ID</param>
        /// <param name="workBenchId">工作台Id</param>
        [RpcHandler(ExposeToClient = true, CallInterval = .2f)]
        public static void TechnologyUnlock(this BlueprintComponent self, HashSet<long> bizIds, long workBenchId)
        {
            if (bizIds.Count == 0 || bizIds.Count > 20) return;
            PartEntity workBench = null;
            int workBenchLevel = 0;
            // 如果发0 必须是基础科技
            if (workBenchId == 0)
            {
                foreach (var bizId in bizIds)
                {
                    var techConfig = McCommon.Tables.TbTechnology.GetOrDefault(bizId);
                    if (techConfig.GroupId != 0)
                    {
                        self.Logger.Info($"TechnologyUnlock workBenchId:{workBenchId}, groupId:{techConfig.GroupId}, bizId:{bizId}, groupId is not 0");
                        return;
                    }
                }
            }
            else
            {
                workBench = EntityManager.Instance.GetEntity<PartEntity>(workBenchId);
                if (workBench == null)
                {
                    self.Logger.Info($"TechnologyUnlock workBenchId:{workBenchId} not found");
                    return;
                }
                workBenchLevel = ConstructionConsumeUtils.GetWorkBenchLevel(workBench.TemplateId);
                if (workBenchLevel == -1)
                {
                    self.Logger.Info($"TechnologyUnlock workBenchId:{workBenchId}, TemplateId:{workBench.TemplateId}, workBenchLevel:{workBenchLevel} < 0");
                    return;
                }

                //是否在交互范围
                if (!InteractionUtil.IsInInteractionRange(new Vector3(workBench.PosX, 0, workBench.PosZ),
                    new Vector3(self.Player.PosX, 0, self.Player.PosZ), McCommon.Tables.TbGlobalConfig.ItemInterRange))
                {
                    self.Logger.Info($"TechnologyUnlock workBench:[x:{workBench.PosX},z:{workBench.PosZ}], player:[x:{self.Player.PosX},z:{self.Player.PosZ}]. not in range");
                    return;
                }
            }


            // 由于科技合批操作了，所以第一层判断只能判断是否已经解锁和道具是否足够
            // 比如科技链为 1->2->3->4 消耗分别为100， 如果3 已经解锁那么就只会判断道具是否满足300
            // 如上链，bizIds = 1，2，3，4，7 7的前置是6. 那么科技解锁会失败，因为链式结构没法先尝试改状态，所以事务会回滚

            unlockScrapCost.Clear();
            costItemInfo.Clear();
            var canUnlockBizIds = new List<long>();
            var canUnlockbpIds = new List<long>();
            foreach (var techId in bizIds)
            {
                var techConfig = McCommon.Tables.TbTechnology.GetOrDefault(techId);
                if (techConfig == null)
                {
                    self.Logger.Info($"tech:{techId}, fail to get tech config");
                    return;
                }
                if (!techConfig.Isuse)
                {
                    self.Logger.Info($"tech:{techId},tech config is not use");
                    return;
                }

                //情报模式需要判断等级
                if (PlayHelper.IsReputationPlay && techConfig.ReputationLevel > self.Player.ReputationLevel)
                {
                    self.Logger.Info($"tech:{techId},tech need reputation level:{techConfig.ReputationLevel} > player {self.Player.ReputationLevel}");
                    return;
                }

                if (techConfig.GroupId > workBenchLevel)
                {
                    self.Logger.Info($"tech:{techId},tech config group:{techConfig.GroupId} > workBenchLevel:{workBenchLevel}");
                    return;
                }
                
                if(self.TechnologyIsUnlock(techId)) continue;

                //如果前置科技没有解锁，则不能解锁
                foreach (var parentTechId in techConfig.ParentId)
                {
                    if (!bizIds.Contains(parentTechId) && !self.TechnologyIsUnlock(parentTechId))
                    {
                        self.Logger.Info($"tech:{techId},parent tech:{parentTechId} is unlock");
                        return;
                    }
                }

                canUnlockBizIds.Add(techId);

                for (int i = 0; i < techConfig.BlueprintIds.Length; i++)
                {
                    var bpId = techConfig.BlueprintIds[i];
                    if (!self.BluePrintIsUnlock(bpId))
                    {
                        canUnlockbpIds.Add(bpId);

                        var costItemId = techConfig.Ingredientsids[i];
                        var costNum = techConfig.IngredientsNum[i];
                        // 消耗会配置为0
                        if (costNum == 0) continue;

                        if (costItemInfo.ContainsKey(costItemId))
                            costItemInfo[costItemId] += costNum;
                        else
                            costItemInfo.Add(costItemId, costNum);

                        if (costItemId == ItemConst.ScrapItemId)
                        {
                            if (unlockScrapCost.ContainsKey(techId))
                                unlockScrapCost[techId] += costNum;
                            else
                                unlockScrapCost.Add(techId, costNum);
                        }
                    }
                }
            }

            if (canUnlockBizIds.Count == 0)
            {
                self.Logger.Info($"bizIds:{string.Join(",", bizIds)}, not need unlock tech");
                return;
            }

            // 可能会没有消耗，因为策划配置的科技解锁不需要消耗废料
            foreach (var (itemId, costCount) in costItemInfo)
            {
                var hasCount = self.Player.Root.GetNodeValByPath(NodePathConst.VPlayerInventory, itemId);
                if (hasCount < costCount)
                {
                    self.Logger.Info($"itemId:{itemId}, count:{hasCount}, limit:{costCount}, not enough");
                    return;
                }
            }
            using var _ = NodeOpContext.GetNew(OpContextConst.TECHNOLOGY_UNLOCK).SetOpRoleId(self.RoleId);
            using var transaction = EntityTransaction.Start($"TechnologyUnlock-{self.RoleId}-{workBenchId}-{string.Join('/', bizIds)}");
            foreach (var (itemId, costCount) in costItemInfo)
            {
                var opCode = self.Root.RequireNode(new NodeOpByBizId(itemId, costCount));
                if (opCode != EOpCode.Success)
                {
                    self.Logger.Info($"itemId:{itemId}, count:{costCount}, fail to require");
                    transaction.Rollback(OpCodeReason.Reason[opCode]);
                    return;
                }
            }

            foreach (var techId in canUnlockBizIds)
            {
                var opCode = self.Root.MergeNode(techId, 1);
                if (opCode != EOpCode.Success)
                {
                    self.Logger.Info($"techId:{techId}, fail to merge tech");
                    transaction.Rollback(OpCodeReason.Reason[opCode]);
                    return;
                }
                else
                {
                    var techConfig = McCommon.Tables.TbTechnology.GetOrDefault(techId);
                    unlockScrapCost.TryGetValue(techId, out int scrapCount);
                    TLogUtil.LogUnlockEvent(self.Player, workBench?.TemplateId.ToString() ?? "0", techConfig.ItemId, scrapCount, techConfig.System);
                }
            }

            foreach (var bpId in canUnlockbpIds)
            {
                var opCode = self.Root.MergeNode(bpId, 1);
                if (opCode != EOpCode.Success)
                {
                    self.Logger.Info($"techId:{bpId}, fail to merge blueprint");
                    transaction.Rollback(OpCodeReason.Reason[opCode]);
                    return;
                }
            }
            transaction.Commit();
        }

        public static void OnBlueprintUnlock(this BlueprintComponent self, long bpId)
        {
            if (BluePrintConst.BluePrintId2TechId.TryGetValue(bpId, out var techIds))
            {
                foreach (var techId in techIds)
                {
                    var techConfig = McCommon.Tables.TbTechnology.GetOrDefault(techId);
                    if (techConfig == null || !techConfig.Isuse) continue;

                    if (self.TechnologyIsUnlock(techId)) continue;

                    bool allUnlock = true;
                    foreach (var tmpBpId in techConfig.BlueprintIds)
                    {
                        if (!self.BluePrintIsUnlock(tmpBpId))
                        {
                            allUnlock = false;
                            break;
                        }
                    }

                    if (allUnlock)
                    {
                        var opCode = self.Root.MergeNode(techId, 1);
                        if (opCode != EOpCode.Success)
                        {
                            self.Logger.Info($"OnBlueprintUnlock techId:{techId}, fail to merge tech");
                            continue;
                        }
                        TLogUtil.LogUnlockEvent(self.Player, "blueprint", techConfig.ItemId, 0, -1);
                    }
                }
            }
        }

        public static IEnumerable<long> GetTechnologyIds(this BlueprintComponent self)
        {
            foreach (var techId in self.UnlockTechIds)
            {
                yield return techId;
            }
        }

        public static IEnumerable<long> GetBlueprintIds(this BlueprintComponent self)
        {
            foreach (var bpId in self.UnlockBluePrints.Keys)
            {
                yield return bpId;
            }
        }

        /// <summary>
        /// 玩家自己蓝图解锁
        /// </summary>
        /// <param name="bpId">结果bool</param>
        public static bool BluePrintIsUnlock(this BlueprintComponent self, long bpId)
        {
            return self.UnlockBluePrints.ContainsKey(bpId);
        }

        /// <summary>
        /// 玩家自己科技解锁
        /// </summary>
        /// <param name="techId">结果bool</param>
        public static bool TechnologyIsUnlock(this BlueprintComponent self, long techId)
        {
            return self.UnlockTechIds.Contains(techId);
        }

        /// <summary>
        /// 队友共享蓝图是否解锁
        /// </summary>
        /// <param name="bpId">结果bool</param>
        public static bool IsShareBluePrintUnlock(this BlueprintComponent self, long bpId)
        {
            if (FunctionSwitchComponent.Instance.IsEnable(FunctionConst.TeamTechUnlockShare) && self.Player.MyTeam != null)
            {
                return TeamTechnologyUtil.IsUnlockBlueprint(self.Player.MyTeam, bpId);
            }
            else
            {
                return self.BluePrintIsUnlock(bpId);
            }
        }

        /// <summary>
        /// 队友共享科技是否解锁
        /// </summary>
        /// <param name="techId">结果bool</param>
        public static bool IsShareTechnologyUnlock(this BlueprintComponent self, long techId)
        {
            if (FunctionSwitchComponent.Instance.IsEnable(FunctionConst.TeamTechUnlockShare) && self.Player.MyTeam != null)
            {
                return TeamTechnologyUtil.IsUnlockTechnology(self.Player.MyTeam, techId);
            }
            else
            {
                return self.TechnologyIsUnlock(techId);
            }
        }
    }
}
