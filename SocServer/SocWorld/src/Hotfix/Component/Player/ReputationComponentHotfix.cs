using Newtonsoft.Json;
using SimpleJSON;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Extension;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.CustomType.RootNode;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    static public partial class ReputationComponentHotfix
    {
        private const int REPUTATION_FATIGUEE_ID = 9001; // 大厅情报疲劳Id

        static ReputationComponentHotfix()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerEntityLoginEvent>((pe, _) =>
            {
                if (PlayHelper.IsReputationPlay)
                {
                    pe.ReputationComp.UpdateUnlockData();
                }
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<BeforeJoinTeam>((player, eve) =>
            {
                // 玩家已经进过进服务器
                if (PlayHelper.IsReputationPlay)
                {
                    var reputationComp = player.ReputationComp;
                    if (reputationComp.ReputationCabinetId != 0)
                    {
                        var entity = EntityManager.Instance.GetEntity(player.ReputationComp.ReputationCabinetId);
                        if (entity is not PartEntity partEntity)
                            return;

                        var team = EntityManager.Instance.GetEntity(eve.TeamId) as TeamEntity;
                        if (team == null)
                            return;


                        if (team.MemberDic.Count == 0)
                        {
                            team.TeamReputationInfo.ReputationCabinetId = player.ReputationComp.ReputationCabinetId;
                            team.TeamReputationInfo.PosX = partEntity.PosX;
                            team.TeamReputationInfo.PosY = partEntity.PosY;
                            team.TeamReputationInfo.PosZ = partEntity.PosZ;
                            player.ReputationComp.ReputationCabinetId = 0;
                            team.Logger.Info($"[OnBeforePlayerEnterTeam], role:{player.RoleId}, team reputation cabinet:{team.TeamReputationInfo.ReputationCabinetId}");
                        }
                        else
                        {
                            // 当前队伍没有声望柜，队员有声望柜，设置队伍声望柜
                            if (team.TeamReputationInfo.ReputationCabinetId == 0)
                            {
                                team.TeamReputationInfo.ReputationCabinetId = player.ReputationComp.ReputationCabinetId;
                            }
                            else
                            {
                                var territoryCabinetComp = partEntity.GetComponent<TerritoryCabinetComponent>(EComponentIdEnum.TerritoryCabinet);
                                if (territoryCabinetComp != null)
                                {
                                    territoryCabinetComp.OnTeamMemberEnter(player);
                                    team.Logger.Info($"[OnBeforePlayerEnterTeam], role:{player.RoleId}, reputationCabinet:{partEntity.EntityId}, to normal");
                                }
                            }
                        }
                    }
                }
            });

            EntityStaticCallback<TeamEntity>.AddStaticCallback<LeaveTeam>((team, eve) =>
            {
                var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(eve.RoleId);
                if (playerEntity == null) return;
                if (PlayHelper.IsReputationPlay)
                {
                    var partEntity = EntityManager.Instance.GetEntity(team.TeamReputationInfo.ReputationCabinetId) as PartEntity;
                    if (partEntity != null)
                    {
                        var reputationCabinet = partEntity.GetComponent<TerritoryCabinetComponent>(EComponentIdEnum.TerritoryCabinet);
                        if (reputationCabinet == null)
                        {
                            team.TeamReputationInfo.ReputationCabinetId = 0;
                        }
                        else if (reputationCabinet.CreatePlayerRoleId == eve.RoleId)
                        {
                            // 当声望柜子的创建者离队时，清空队伍声望柜子，玩家设置声望柜子
                            team.TeamReputationInfo.ReputationCabinetId = 0;
                            playerEntity.ReputationComp.ReputationCabinetId = partEntity.EntityId;
                        }
                        else
                        {
                            playerEntity.ReputationComp.OnLeaveTeam();
                        }

                        team.TeamReputationInfo.CalConversionEfficiency(team);
                    }
                }
            });

            EntityStaticCallback<TeamEntity>.AddStaticCallback<LeaveTeam>((team, eve) =>
            {
                var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(eve.RoleId);
                if (playerEntity == null) return;
                if (PlayHelper.IsReputationPlay)
                {
                    if (team.TeamReputationInfo.ReputationCabinetId != 0)
                    {
                        playerEntity.ReputationComp.ActivateReputationCabinetTimeStamp = ProcessEntity.Instance.TimeSinceStartup + 60 * 1000 * McCommon.Tables.TbGlobalConfig.ActivateReputationCabinetCD;
                    }
                }
            });
        }

        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(ReputationComponentHotfix));

        [Hotfix]
        public static void Init(this ReputationComponent self)
        {
            self.SystemRoot = new ItemSystemRootNode(NodeSystemType.ReputationSystem);
            var containerReputationNode = new ItemContainerNode(ContainerConst.ReputationContainerId, PlayerInventoryNodeIndex.Reputation, NodeConst.ReputationContainerNodeId);
            self.SystemRoot.AddChildNode(containerReputationNode);

            self.BadgeSystemRoot = new ReputationBadgeRootNode();
            self.BadgeSystemRoot.AddChildNode(new DirectoryNode(ReputationConst.BadgeDirectoryIndex, ReputationConst.BadgeDirectoryIndex));
            self.BadgeSystemRoot.AddChildNode(new DirectoryNode(ReputationConst.BadgeSlotDirectoryIndex, ReputationConst.BadgeSlotDirectoryIndex));

            self.LobbyAwardLevels = new();
            self.BattleRewardDict = new();
            self.ReputationUnlockDropIds = new();
            self.ReputationRecords = new();
            self.UnlockedReward = new();

            self.Root.RegisterSystemRootNode(self.SystemRoot);
            self.Root.RegisterSystemRootNode(self.BadgeSystemRoot);
        }

        [Hotfix]
        public static void InitFromDb(this ReputationComponent self)
        {
            self.Root.RegisterSystemRootNode(self.SystemRoot);
            self.Root.RegisterSystemRootNode(self.BadgeSystemRoot);
        }

        [Hotfix]
        private static void PostInit(this ReputationComponent self, bool isLoadFromDb)
        {
            if (!isLoadFromDb)
            {
                self.UpdateUnlockData();
            }
        }

        public static void UpdateUnlockData(this ReputationComponent self)
        {
#if !SOC_WORLD_TEST
            ServerLobbyServiceHotfix.SendLoadReputaionReplacements(LobbyServiceManager.Instance.lobbyService, self.ParentEntity.EntityId, self.RoleId, self.OnLoadReputaionUnlockRewardCallback);
#endif
        }

        static public void OnLoadReputaionUnlockRewardCallback(this ReputationComponent self, JSONNode content, HttpStatusCode statusCode, int errorCode)
        {
            if (statusCode != HttpStatusCode.OK || errorCode != 0)
            {
                self.Logger.Error($"Load Reputaion Replacements fail! recv code : {errorCode} {statusCode}");
                return;
            }
            self.Logger.Info($"Load Reputaion Replacements! recv {content}");
            var unlockedRewardIDList = content["unlockedRewardIDList"].AsArray;
            foreach (var (_, node) in unlockedRewardIDList)
            {
                if (!int.TryParse(node, out var index))
                {
                    self.Logger.Error($"int Try Parse  fail : {node.Value}");
                    continue;
                }
                if (!McCommon.Tables.TbReputRwd.DataMap.TryGetValue(index, out var config))
                {
                    self.Logger.Error($"[OnLoadReputaionReplacementsCallback] index: {config.InGameReward} is not exist in TbReputRwd!");
                    continue;
                }

                if (self.ReputationUnlockDropIds.Contains(config.Id))
                {
                    continue;
                }
                self.ReputationUnlockDropIds.Add(config.Id);
            }
        }

        /// <summary>
        /// 添加一条声望提交记录
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="count"></param>
        /// <param name="total"></param>
        /// <param name="level"></param>
        static public void AddTeamReputationRecord(this ReputationComponent self, ulong roleId, int count, long total, long level)
        {
            self.ReputationRecords.Add(new PlayerReputationRecord()
            {
                Timestamp = ProcessEntity.Instance.NowTs,
                RoleId = roleId,
                Count = count,
                Total = (int)total,
                Level = (int)level,
            });
        }

        /// <summary>
        /// 增加个人声望数值
        /// </summary>
        /// <param name="commiterRoleId"></param>
        /// <param name="level"></param>
        /// <param name="count"></param>
        static public void AddPlayerReputation(this ReputationComponent self, ulong roleId, int addExp)
        {
            int maxLevel = McCommon.Tables.TbReputationLevelConfig.MaxReputationLevel;
            var curReputationExp = self.ReputationExp;
            var curReputationLevel = self.ReputationLevel;
            if (addExp <= 0 || curReputationLevel >= maxLevel) return;

            //加分
            curReputationExp += addExp;
            TLogUtil.LogBattleResourceEvent(self.Player, MoneyConst.PlayerReputation, "add", "ReputationConverter", addExp, curReputationExp - addExp, curReputationExp);
            var levelMap = McCommon.Tables.TbReputationLevelConfig.SumScoreByLevel;

            // 升级
            if (curReputationLevel < maxLevel)
            {
                for (int level = (int)curReputationLevel + 1; level <= maxLevel; level++)
                {
                    if (curReputationExp >= (long)levelMap[level])
                    {
                        curReputationLevel += 1;
                    }
                }
            }

            self.ReputationExp = curReputationExp;
            self.Player.ReputationExp = curReputationExp;
            if (self.ReputationLevel != curReputationLevel)
            {
                self.ReputationLevelTimeSec = ProcessEntity.Instance.SecSinceLogicStart;
            }
            self.ReputationLevel = curReputationLevel;
            self.Player.ReputationLevel = curReputationLevel;
            self.AddTeamReputationRecord(roleId, addExp, curReputationExp, curReputationLevel);
            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(self.Player, new ItemAdd(MoneyConst.PlayerReputation, addExp, OpContextConst.REPUTATION));
            self.Logger.Info($"[AddPlayerReputation] roleId:{roleId} addExp:{addExp} curLevel:{self.ReputationLevel} curExp:{self.ReputationExp}");
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void ReputationBadgeUnlock(this ReputationComponent self, long badgeId)
        {
            var code = self.ReputationBadgeUnlockServer(badgeId);
            self.RemoteCallReputationBadgeUnlockAck(ERpcTarget.OwnClient, badgeId, (int)code);
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void ReputationBadgeEquip(this ReputationComponent self, long badgeId, int slot)
        {
            var code = self.ReputationBadgeEquipServer(badgeId, slot);
            self.RemoteCallReputationBadgeEquipAck(ERpcTarget.OwnClient, badgeId, slot, (int)code);
        }

        static public EOpCode WearReputationBadgeIdInner(this ReputationComponent self, long badgeId)
        {
            if (badgeId != 0)
            {
                var badgeConfig = McCommon.Tables.TbReputationBadgeConfig.GetOrDefault(badgeId);
                if (null == badgeConfig)
                {
                    return EOpCode.ReputationBadgeIdError;
                }
                var badgeNode = self.Root.GetNodeByPath(NodeSystemType.ReputationBadge,
                    ReputationConst.BadgeDirectoryIndex, badgeId) as ReputationBadgeNode;
                if (null == badgeNode)
                {
                    return EOpCode.NodeNotExist;
                }
            }

            self.CurrentBadgeId = badgeId;
            return EOpCode.Success;
        }

        /// <summary>
        /// 设置是否显示声望等级
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void SetHideReputationLevel(this ReputationComponent self, bool hide)
        {
            self.HideReputationLevel = hide;
            self.RemoteCallOnSetHideReputationLevelAck(ERpcTarget.OwnClient, (int)EOpCode.Success);
        }

        /// <summary>
        /// 佩戴徽章Id
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void WearReputationBadgeId(this ReputationComponent self, long badgeId)
        {
            self.RemoteCallOnWearReputationBadgeIdAck(ERpcTarget.OwnClient, (int)self.WearReputationBadgeIdInner(badgeId), badgeId);
        }

        /// <summary>
        /// 查询玩家声望等级和佩戴徽章简略信息,聊天中显示
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void QueryPlayerBriefInfo(this ReputationComponent self, ulong roleId)
        {
            var playerEntity = EntityManager.Instance.GetPlayerEntityByRoleId(roleId);
            if (playerEntity == null)
            {
                self.RemoteCallQueryPlayerBriefInfoAck(ERpcTarget.OwnClient, (int)EOpCode.ArgsError, null);
                self.Logger.Info($"{roleId} roleId not found");
                return;
            }
            EntityBriefInfo info = new()
            {
                RoleId = roleId,
                ReputationLevel = playerEntity.GetHideReputionLevel(),
                CurrentBadgeId = playerEntity.ReputationComp.CurrentBadgeId,

            };
            self.RemoteCallQueryPlayerBriefInfoAck(ERpcTarget.OwnClient, (int)EOpCode.Success, info);
        }

        /// <summary>
        /// 领取局内声望奖励
        /// </summary>
        /// <param name="level">领取等级 </param>
        /// <param name="selectIds">selectIds</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void AwardBattleReward(this ReputationComponent self, int level, Dictionary<int, IntHashSetData> selectIds)
        {
            if (level < 0) return;
            if (level > self.ReputationLevel) return;
            if (self.BattleRewardDict.TryGetValue(level, out var selectInfo)) return;

            if (!McCommon.Tables.TbReputationLevelConfig.DataMap.TryGetValue(level, out var config))
            {
                self.Logger.Warn($"[AwardBattleReward] dropId: {level} is not exist!");
                return;
            }

            if (config.InGameRewardSelect.Length != selectIds.Count)
            {
                self.Logger.Warn($"[AwardBattleReward] selectIds count: {selectIds.Count} is not match config:{config.InGameRewardSelect.Length}!");
                return;
            }

            var info = new ReputationUtil.AwardBattleRewardInfo()
            {
                ToBeUnlocked = [],
                ReputationLevel = level,
                DropIds = [],
                Rewards = [.. DropUtil.GetRewardByDropId(config.InGameReward)],
            };
            selectInfo ??= new();

            foreach (var (id, val) in selectIds)
            {
                if (!config.InGameRewardSelect.Contains(id))
                {
                    self.Logger.Info($"[AwardBattleReward] id: {id} is not in InGameRewardSelect{string.Join(",", config.InGameRewardSelect)}!");
                    return;
                }

                if (!McCommon.Tables.TbReputationSelectConfig.DataMap.TryGetValue(id, out var selectConfig))
                {
                    self.Logger.Warn($"[AwardBattleReward] id: {id}: is not in TbReputationSelectConfig!");
                    return;
                }
                var selectList = val.Inner;
                if (selectList == null || selectList.Count != selectConfig.SelectNum)
                {
                    self.Logger.Info($"[AwardBattleReward] id: {id} is not in InGameRewardSelect{string.Join(",", config.InGameRewardSelect)}!");
                    return;
                }

                foreach (var selectId in selectList)
                {
                    if (!selectConfig.SelectList.Contains(selectId))
                    {
                        self.Logger.Info($"[AwardBattleReward] selectId: {selectId} is not in SelectList{string.Join(",", selectConfig.SelectList)}!");
                        return;
                    }

                    if (!McCommon.Tables.TbReputRwd.DataMap.TryGetValue(selectId, out var rewardConfig))
                    {
                        self.Logger.Warn($"[AwardBattleReward] selectId: {selectId} is not in TbReputRwd!");
                        return;
                    }

                    if (selectInfo.SelectedDropIds.Contains(rewardConfig.InGameReward))
                    {
                        self.Logger.Warn($"[AwardBattleReward] InGameReward: {rewardConfig.InGameReward} have already in SelectedDropIds:{selectInfo.SelectedDropIds.ToDetailedString()}!");
                        return;
                    }

                    if (rewardConfig.IsTalentLocked && !self.UnlockedReward.Contains(selectId))
                    {
                        self.Logger.Warn($"[AwardBattleReward] selectId: {selectId} is locked by talent, please unlock it first!");
                        return;
                    }

                    // 加入待解锁列表
                    if (rewardConfig.IsLocked && !self.ReputationUnlockDropIds.Contains(rewardConfig.Id))
                    {
                        info.ToBeUnlocked.Add(rewardConfig.Id);
                    }

                    info.DropIds.Add(rewardConfig.InGameReward);
                    info.Rewards.AddRange(DropUtil.GetRewardByDropId(rewardConfig.InGameReward));
                }
            }

            if (info.ToBeUnlocked.Count <= 0)
            {
                self.OnAwardBattleRewardCallback(ref info);
                return;
            }

            ServerLobbyServiceHotfix.SendUnlockReputaionReward(LobbyServiceManager.Instance.lobbyService, self.Player.EntityId, self.RoleId, info.ToBeUnlocked,
                (content, statusCode, code) => self.AwardBattleRewardCallback(content, statusCode, code, ref info));
        }
        private static void AwardBattleRewardCallback(this ReputationComponent self, JSONNode content, HttpStatusCode statusCode, int errorCode, ref ReputationUtil.AwardBattleRewardInfo info)
        {
            if (statusCode != HttpStatusCode.OK || errorCode != 0)
            {
                self.Logger.Error($"[AwardBattleReward] SendUnlockReputaionReward fail! recv code : {errorCode} {statusCode}");
            }
            else
            {
                self.Logger.Info($"[AwardBattleReward] SendUnlockReputaionReward! recv {content}");

                foreach (var node in info.ToBeUnlocked)
                {
                    self.ReputationUnlockDropIds.Add(node);
                }

                self.OnAwardBattleRewardCallback(ref info);
            }
        }

        private static void OnAwardBattleRewardCallback(this ReputationComponent self, ref ReputationUtil.AwardBattleRewardInfo info)
        {
            if (self.BattleRewardDict.TryGetValue(info.ReputationLevel, out var selectInfo)) return;

            selectInfo ??= new();
            using var ctx = NodeOpContext.GetNew(OpContextConst.REPUTATION);
            ctx.SetServerOperation(true);
            using var transaction = EntityTransaction.Start($"OnAwardBattleRewardCallback-{self.Player.RoleId}-{info.ReputationLevel}");
            foreach (var item in info.Rewards)
            {
                var code = self.Root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventory).WithDetail(new BizIdWithCount(item.ItemId, item.Amount, item.ItemParam)));
                if (code != EOpCode.Success)
                {
                    if (code != EOpCode.ItemSystemFull)
                    {
                        self.Logger.Error($"[OnAwardBattleRewardCallback] MergeNode to PlayerInventory failed. RoleId {self.RoleId},id:{item.ItemId}, count:{item.Amount}, code:{code}");
                    }
                    transaction.Rollback(OpCodeReason.Reason[code], false);
                    self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, 3007);
                    return;
                }

                code = self.Root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInDataStatisticReputation).WithDetail(new BizIdWithCount(item.ItemId, item.Amount, item.ItemParam)));
                if (code != EOpCode.Success)
                {
                    self.Logger.Error($"[OnAwardBattleRewardCallback] MergeNode to DataStatisticReputation failed. RoleId {self.RoleId},id:{item.ItemId}, count:{item.Amount}");
                    transaction.Rollback(OpCodeReason.Reason[code]);
                    self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, 3007);
                    return;
                }
            }

            transaction.Commit();

            foreach (var dropId in info.DropIds)
            {
                selectInfo.SelectedDropIds.Add(dropId);
            }

            self.BattleRewardDict[info.ReputationLevel] = selectInfo;
            self.Logger.Info($"OnAwardBattleRewardCallback sucess reputation dropIds: {info.DropIds.Print()},rewards {info.Rewards.Print()}");
        }

        /// <summary>
        /// 领取局外奖励
        /// </summary>
        /// <param name="level"> 领取等级</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void AwardLobbyReward(this ReputationComponent self, int level)
        {
            if (level < 0) return;
            if (level > self.ReputationLevel) return;
            if (self.LobbyAwardLevels.Contains(level)) return;

            if (!McCommon.Tables.TbReputationLevelConfig.DataMap.TryGetValue(level, out var config))
            {
                self.Logger.Warn($"[AwardLobbyReward] dropId: {level} is not exist!");
                return;
            }

            self.Logger.InfoFormat($"[AwardLobbyReward], delLobbyRewardFatigue level:{level} RewardFatigue:{config.RewardFatigue}");
            // 通知大厅处理领奖疲劳
            ServerLobbyServiceHotfix.SendLobbyRewardFatigue(LobbyServiceManager.Instance.lobbyService, self.Player.EntityId, self.RoleId, "Reputation Reward Fatigue", REPUTATION_FATIGUEE_ID, config.RewardFatigue, (content, statusCode, code, playerEntityId) => OnSendLobbyRewardFatigueCallback(self, content, statusCode, code, level));
        }

        static public void OnSendLobbyRewardFatigueCallback(ReputationComponent self, JSONNode content, HttpStatusCode statusCode, int errorCode, int level)
        {
            if (errorCode == 21004)
            {
                logger.Info($"[AwardLobbyReward] FatigueValue limit");
                return;
            }
            else if (statusCode != HttpStatusCode.OK || errorCode != 0)
            {
                logger.Error($"OnsendLobbyRewardCallback code : {errorCode} {statusCode}");
                return;
            }

            self.LobbyAwardLevels.Add(level);
            var dropList = new List<(long, int)>();

            var config = McCommon.Tables.TbReputationLevelConfig.DataMap[level];
            var groupCfg = McCommon.Tables.TBGroup.GetOrDefault(config.OutGameReward);
            if (groupCfg != null)
            {
                var itemList = groupCfg.Items;
                for (int i = 0; i < itemList.Count; i++)
                {
                    dropList.Add((itemList[i].ResourceID, itemList[i].Amount));
                }
            }

            Dictionary<long, int> lobbyItems = new();
            foreach (var (itemId, count) in dropList)
            {
                if (!lobbyItems.ContainsKey(itemId))
                    lobbyItems[itemId] = count;
                else
                    lobbyItems[itemId] += count;
            }
            self.Logger.InfoFormat($"[AwardLobbyReward],level:{level} lobbyItems:{DebugDetailHelper.ToDetailedString(lobbyItems)}");
            //发送局外奖励
            if (lobbyItems.Count > 0)
                ServerLobbyServiceHotfix.SendLobbyReward(LobbyServiceManager.Instance.lobbyService, self.Player.EntityId, self.RoleId, lobbyItems, "lobby Reputation Reward", (content, statusCode, code, playerEntityId) => OnSendLobbyRewardCallback(self, content, statusCode, code, level));
        }

        static public void OnSendLobbyRewardCallback(ReputationComponent self, JSONNode content, HttpStatusCode statusCode, int errorCode, int level)
        {
            if (statusCode != HttpStatusCode.OK || errorCode != 0)
            {
                logger.Error($"OnsendLobbyRewardCallback code : {errorCode} {statusCode}");
                OnSendLobbyRewardFailed(self, level);
                return;
            }

            var addResourceRsp = JsonConvert.DeserializeObject<AddResourceRsp>(content.ToString());
            if (addResourceRsp == null)
            {
                logger.Error($"OnsendLobbyRewardCallback AddResourceRsp is null");
                OnSendLobbyRewardFailed(self, level);
                return;
            }

            var playerEntity = self.Player;
            using var ctx = NodeOpContext.GetNew("OnSendLobbyRewardCallback").SetOpRoleId(playerEntity.RoleId);
            foreach (var resource in addResourceRsp.AcquiredResources)
            {
                var resourceConfig = McCommon.Tables.TBGeneral.GetOrDefault(resource.ResourceId);
                if (resourceConfig != null && resourceConfig.Type == Data.resource.ENUMResourceType.SKIN)
                {
                    var skinNode = new SkinNode(resource.ResourceId, 0, ProcessEntity.Instance.NowTs, 0, resource.ExpireAt);
                    var code = playerEntity.Root.MergeNode(new NodeOpByExisting(skinNode).WithOverrideSystem(NodeSystemType.Skin));
                    if (code != EOpCode.Success)
                    {
                        logger.Warn(
                            $"[OnSendLobbyRewardCallback] MergeNode to PlayerInventory failed code:{code}. RoleId {playerEntity.RoleId},id:{resource.ResourceId}, count:{resource.AcquiredAmount}");
                        continue;
                    }
                }
            }
        }

        static public void OnSendLobbyRewardFailed(ReputationComponent self, int level)
        {
            var config = McCommon.Tables.TbReputationLevelConfig.DataMap[level];

            self.Logger.InfoFormat($"[RollbackReputationFatigue],level:{level} RewardFatigue:{config.RewardFatigue}");
            //回滚已扣除的声望疲劳
            Dictionary<long, int> reward = new()
            {
                { REPUTATION_FATIGUEE_ID, config.RewardFatigue },
            };
            ServerLobbyServiceHotfix.SendLobbyReward(LobbyServiceManager.Instance.lobbyService, self.Player.EntityId, self.RoleId, reward, "rollback Reputation Fatigue", (content, statusCode, errorCode, playerEntityId) => 
            { 
                if (statusCode != HttpStatusCode.OK || errorCode != 0)
                {
                    self.Logger.Error($"[RollbackReputationFatigue] failed! level:{level} RewardFatigue:{config.RewardFatigue}");
                }
            });
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void GetClientLastCheckReputationLevel(this ReputationComponent self)
        {
            self.RemoteCallGetClientLastCheckReputationLevelAck(ERpcTarget.OwnClient, self.ClientLastCheckReputationLevel);
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void SetClientLastCheckReputationLevel(this ReputationComponent self, int level)
        {
            if (level < 0 || level > McCommon.Tables.TbReputationLevelConfig.MaxReputationLevel)
            {
                self.Logger.Warn($"[SetClientLastCheckReputationLevel] Set ClientLastCheckReputationLevel fail level:{level}");
                self.RemoteCallSetClientLastCheckReputationLevelAck(ERpcTarget.OwnClient, self.ReputationLevel);
                return;
            }
            self.ClientLastCheckReputationLevel = level;
            self.RemoteCallSetClientLastCheckReputationLevelAck(ERpcTarget.OwnClient, self.ClientLastCheckReputationLevel);
        }

        /// <summary>
        /// 声望徽章解锁
        /// </summary>
        /// <param name="badgeId">徽章ID</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public EOpCode ReputationBadgeUnlockServer(this ReputationComponent self, long badgeId)
        {
            var badgeConfig = McCommon.Tables.TbReputationBadgeConfig.GetOrDefault(badgeId);
            if (null == badgeConfig) return EOpCode.ReputationBadgeIdError;

            int preBadgeId = badgeConfig.BadgeUnlockCondition;
            if (preBadgeId > 0)
            {
                var preBadgeConfig = McCommon.Tables.TbReputationBadgeConfig.GetOrDefault(preBadgeId);
                if (null == preBadgeConfig) return EOpCode.ReputationBadgeIdError;

                var preBadgeNode = self.Root.GetNodeByPath(NodeSystemType.ReputationBadge, ReputationConst.BadgeDirectoryIndex, preBadgeId);
                if (null == preBadgeNode) return EOpCode.ReputationBadgePreNodeUnlock;
            }

            var badgeNode = self.Root.GetNodeByPath(NodeSystemType.ReputationBadge, ReputationConst.BadgeDirectoryIndex, badgeId);
            if (null != badgeNode) return EOpCode.NodeExists;

            var currLevel = self.ReputationLevel;
            if (currLevel < badgeConfig.BadgeUnlockLevel)
            {
                self.Logger.Info($"curLevel:{currLevel}, is less than limit:{badgeConfig.BadgeUnlockLevel}");
                return EOpCode.ReputationBadgeLevelError;
            }

            var taskId = badgeConfig.TaskId;
            var parentNode = self.Player.Root.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Bage, TaskNodeIndex.CompletedAndNotGetReward) as DirectoryNode;
            var notRewardNode = parentNode.GetChildNode(taskId) as TaskNode;
            if (notRewardNode == null)
            {
                self.Logger.Error($"[ReputationBadgeUnlockServer] notRewardNode null badge id:{badgeId}, task id: {taskId}");
                return EOpCode.ReputationBadgeItemNotEnough;
            }

            using var ctx = NodeOpContext.GetNew("ReputationBadgeUnlockServer").SetOpRoleId(self.RoleId);
            using var transaction = EntityTransaction.Start($"UnlockBadge-{self.RoleId}-{badgeId}");

            var taskContainer = self.Player.Root.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Bage) as TaskContainer;
            parentNode.RemoveChildNode(notRewardNode);
            taskContainer.AddCompletedTask(notRewardNode);

            var opCode = self.Player.Root.MergeNode(badgeId, 1);
            if (opCode != EOpCode.Success)
            {
                self.Logger.Error($"[ReputationBadgeUnlockServer] MergeNode failed. RoleId {self.Player.RoleId}, badge id {badgeId}");
                transaction.Rollback($"ReputationBadgeUnlockServer MergeNode fail {opCode}");
                return opCode;
            }

            transaction.Commit();

            return EOpCode.Success;
        }

        /// <summary>
        /// 声望徽章装备
        /// </summary>
        /// <param name="badgeId">徽章ID</param>
        /// <param name="slot">槽位</param>
        static public EOpCode ReputationBadgeEquipServer(this ReputationComponent self, long badgeId, int slot)
        {
            var badgeConfig = McCommon.Tables.TbReputationBadgeConfig.GetOrDefault(badgeId);
            if (null == badgeConfig) return EOpCode.ReputationBadgeIdError;

            var badgeNode = self.BadgeSystemRoot.GetNodeByRelativePath(ReputationConst.BadgeDirectoryIndex, badgeId) as ReputationBadgeNode;
            if (null == badgeNode) return EOpCode.NodeNotExist;

            var currLevel = self.ReputationLevel;
            var canUseSlotNum = self.BadgeSystemRoot.GetCanEquitSlotNum(currLevel);
            if (canUseSlotNum == -1) return EOpCode.ReputationBadgeSlotNotEnough;

            if (slot < 0 || slot >= canUseSlotNum) return EOpCode.ArgsError;

            var ret = self.BadgeSystemRoot.EquipBadge(badgeNode, slot);
            if (ret != EOpCode.Success) return ret;

            return EOpCode.Success;
        }

        public static long GetReputationCabinetIdByRoleId(this ReputationComponent self)
        {
            var player = self.Player;
            if (player == null) return -1;
            if (player.MyTeam != null) return player.MyTeam.TeamReputationInfo.ReputationCabinetId;
            return self.ReputationCabinetId;
        }

        public static void DebugResetReputation(this ReputationComponent self)
        {
            self.ReputationLevel = 0;
            self.ReputationExp = 0;
            self.BattleRewardDict.Clear();
        }

        public static void OnLeaveTeam(this ReputationComponent self)
        {
            self.Efficiency = 0;
            self.ConvertedTimeStamp = 0;
            self.StorageCount = 0;
        }
    }
}
