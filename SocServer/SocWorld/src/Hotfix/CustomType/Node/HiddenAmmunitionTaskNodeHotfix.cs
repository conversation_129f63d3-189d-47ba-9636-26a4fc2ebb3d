using System.Collections.Generic;
using System.Linq;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Entity;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Spawn;

namespace WizardGames.Soc.Common.CustomType
{
    [HotfixClass]
    public static partial class HiddenAmmunitionTaskNodeHotfix
    {
        private static List<GroupPoint> spawnPosList = new();

        [Hotfix]
        public static void StartVirtual(this HiddenAmmunitionTaskNode self)
        {
            self.CreatePoiTaskItemHiddenAmmunition(self.GetPoiTaskTemplated());
        }

        [Hotfix]
        public static void StopVirtual(this HiddenAmmunitionTaskNode self, TaskContainer container)
        {
            BaseStop.Invoke(self, container);
        }

        public static void CreatePoiTaskItemHiddenAmmunition(this HiddenAmmunitionTaskNode self, int templateId)
        {
            // 选择遗迹
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(self.BizId);
            var createItemNum = taskConfig.EndConditionParameter[0];
            if (!self.GetSpwanPosAtMonument(McCommon.Tables.TbQuestConst.SneakyFireMonumentList, templateId, createItemNum, spawnPosList))
            {
                self.Logger.Warn($"CreatePoiTaskItemHiddenAmmunition failed. spawnPosList is empty. player:{self.Player.EntityId} templateId:{templateId}");
                return;
            }

            var randIndex = RandomUtil.Instance.Next(0, spawnPosList.Count);
            for (int i = 0; i < createItemNum; i++)
            {
                randIndex = (randIndex + 1) % spawnPosList.Count;
                var pos = spawnPosList[randIndex].Position;
                var rot = spawnPosList[randIndex].EulerAngles;
                var interactionEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
                {
                    EntityType = EntityTypeId.InteractionEntity,
                    TemplateId = templateId,
                    Pos = pos,
                    Rot = rot,
                    PoiLinkedPlayerId = self.Player.EntityId,
                    SpawnType = SpawnTypeDefine.SpawnPoiTask,
                });

                self.GuideData.Add(new TaskGuideData(interactionEntityId, new CustomVector3(pos.x, pos.y, pos.z)));
                self.Logger.Info($"CreatePoiTaskItemHiddenAmmunition. player:{self.Player.EntityId} task:{self.BizId} entityId:{interactionEntityId}");
            }
            
            spawnPosList.Clear();
        }

        // 藏道具
        public static EOpCode HidePoiTaskItem(this HiddenAmmunitionTaskNode self, long itemId, long interactionEntityId, Vector3 interactionPos)
        {
            if (self.TaskComp.Root.GetNodeById(itemId) is not WeaponItemNode srcItemNode)
            {
                self.Logger.Info($"HidePoiTaskItem failed. invaild path. player:{self.Player.EntityId} itemId:{itemId}");
                return EOpCode.ArgsError;
            }
            
            if (!McCommon.Tables.TbQuestConst.SneakyFireWeapons.Contains(srcItemNode.BizId))
            {
                self.Logger.Info($"HidePoiTaskItem failed. invaild weapon. player:{self.Player.EntityId} itemId:{{itemId}}\" bizId:{srcItemNode.BizId}");
                return EOpCode.ArgsError;
            }

            var opCode = srcItemNode.RequireSelf();
            if (opCode != EOpCode.Success)
            {
                self.Logger.Warn($"HidePoiTaskItem failed. weapon is not found. player:{self.Player.EntityId} itemId:{itemId} bizId:{srcItemNode.BizId}");
                return EOpCode.ServerInternalError;
            }
            
            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(self.Player, new FinishActionEvent(GetPoiTaskItemTemplated(self.BizId)));
            self.Logger.Info($"CompletePoiTaskHiddenAmmunition completed. player:{self.Player.EntityId} itemId:{itemId} bizId:{srcItemNode.BizId}");

            for (int i = 0; i < self.GuideData.Count; i++)
            {
                if (self.GuideData[i].EntityId == interactionEntityId)
                {
                    self.GuideData.RemoveAt(i);
                    break;
                }
            }
            
            return EOpCode.Success;
        }

        public static long GetPoiTaskItemTemplated(long poiTaskId)
        {
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(poiTaskId);
            if (taskConfig != null && taskConfig.EndConditionParameter.Length >= 2)
            {
                return taskConfig.EndConditionParameter[1];
            }

            return 0;
        }
    }
}
