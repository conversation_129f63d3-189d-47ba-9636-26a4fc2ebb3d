using SimpleJSON;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Data.medal;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.SocWorld.Component;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.CustomType
{
    [HotfixClass]
    public static partial class MedalTaskContainerHotfix
    {
        static MedalTaskContainerHotfix()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<OnGetClientEvent>((player, _) =>
            {
                LobbyServiceManager.Instance.lobbyService.GetMedalSettleSwitchFromLobby(player.RoleId);
            });
        }

        [Hotfix]
        public static void Init(this MedalTaskContainer self)
        {
            BaseInit.Invoke(self);
            self.StyleToRank = new();
            self.StyleToScore = new();
            self.MedalSyncRecord = new ();
        }

        [Hotfix]
        public static void PostInit(this MedalTaskContainer self, bool isLoadFromDb)
        {
            BasePostInit.Invoke(self, isLoadFromDb);
            if (!isLoadFromDb)
            {
                self.AcceptAcquireTask();
                self.PullStyleRankFromLobby();
            }
        }

        /// <summary>
        /// 接取所有一级勋章的任务和需要提前计数的勋章的所有等级任务
        /// </summary>
        /// <param name="self"></param>
        private static void AcceptAcquireTask(this MedalTaskContainer self)
        {
            foreach (var (taskId, medalInfo) in GlobalInfoSyncEntity.Instance.MedalReconstructData)
            {
                if (self.CompletedTaskIds.Contains(taskId))continue;
                if (medalInfo.Level == (int)ENUMType.Normal ||
                    (McCommon.Tables.TBMedal.Get(medalInfo.MedalId, medalInfo.Level)?.ShouldPreCount ?? false))
                {
                    self.AcceptTask(taskId, false);
                }
            }
        }

        [Hotfix]
        public static TaskNode CreateTaskNode(this MedalTaskContainer self, QuestPhase taskConfig)
        {
            return new MedalTaskNode(taskConfig.Id);
        }

        [Hotfix]
        public static EOpCode CompleteTask(this MedalTaskContainer self, TaskNode task)
        {
            var medalInfo = (task as MedalTaskNode).GetMedalInfo();
            var ret = self.CompleteTaskVirtual(task);
            if (ret == EOpCode.Success)
            {
                self.StyleToScore.TryGetValue(medalInfo.StyleId, out var currentScore);
                self.StyleToScore[medalInfo.StyleId] = currentScore + medalInfo.Point;
                // 没获取到就说明没有下一等级了
                if (McCommon.Tables.TBMedal.Get(medalInfo.MedalId, medalInfo.Level + 1) is OBJMedal nextMedal)
                {
                    if (nextMedal.Task.TryGetValue(GlobalInfoSyncEntity.Instance.TeamMemberLimit, out var nextTaskId) && !self.HasTask(nextTaskId))
                    {
                        self.AcceptTask(nextTaskId, false);
                    }
                }
            }

            return ret;
        }

        /// <summary>
        /// 获取全量勋章数据
        /// </summary>
        /// <param name="self">勋章组件实例</param>
        /// <returns>玩家勋章风格分数信息，如果无数据返回null</returns>
        public static PlayerGrowthServiceSettleStyleRankPointsInfo GetFullMedalData(this MedalTaskContainer self)
        {
            var playerInfo = new PlayerGrowthServiceSettleStyleRankPointsInfo
            {
                RoleID = self.Player.RoleId,
                AddStyleRankPointsMap = new Dictionary<int, int>(self.StyleToScore),
                MedalLevelInfoList = new List<PlayerGrowthServiceMedalLevelInfo>()
            };

            foreach (var (_, medalTaskNode) in self.completedNotRewardTask)
            {
                var medalInfo = (medalTaskNode as MedalTaskNode).GetMedalInfo();
                playerInfo.MedalLevelInfoList.Add(new PlayerGrowthServiceMedalLevelInfo
                {
                    MedalID = medalInfo.MedalId, Level = medalInfo.Level
                });
            }
            
            foreach (var taskId in self.CompletedTaskIds)
            {
                if (GlobalInfoSyncEntity.Instance.MedalReconstructData.TryGetValue(taskId, out MedalInfo value))
                {
                    playerInfo.MedalLevelInfoList.Add(new PlayerGrowthServiceMedalLevelInfo
                    {
                        MedalID = value.MedalId, Level = value.Level
                    });
                }
            }

            return playerInfo;
        }


        /// <summary>
        /// 获取增量勋章数据（仅包含待同步的勋章）
        /// </summary>
        /// <param name="self">勋章组件实例</param>
        /// <returns>玩家勋章风格分数信息，如果无新数据返回null</returns>
        public static PlayerGrowthServiceSettleStyleRankPointsInfo GetIncrementalMedalData(this MedalTaskContainer self)
        {
            var deltaScores = new Dictionary<int, int>();
            var pendingMedalLevels = new List<PlayerGrowthServiceMedalLevelInfo>();

            // 获取待同步的勋章列表
            foreach (var (_, medalTaskNode) in self.completedNotRewardTask)
            {
                var medalInfo = (medalTaskNode as MedalTaskNode).GetMedalInfo();
                deltaScores[medalInfo.StyleId] = self.StyleToScore[medalInfo.StyleId];

                pendingMedalLevels.Add(
                    new PlayerGrowthServiceMedalLevelInfo { MedalID = medalInfo.MedalId, Level = medalInfo.Level });
            }

            // 如果没有分数变化且没有待同步勋章，返回null
            if (deltaScores.Count == 0 && pendingMedalLevels.Count == 0)
            {
                return null;
            }

            var playerInfo = new PlayerGrowthServiceSettleStyleRankPointsInfo
            {
                RoleID = self.Player.RoleId, AddStyleRankPointsMap = deltaScores, MedalLevelInfoList = pendingMedalLevels
            };

            return playerInfo;
        }


        /// <summary>
        /// 从大厅拉取玩家风格段位数据
        /// </summary>
        /// <param name="self">勋章组件实例</param>
        private static void PullStyleRankFromLobby(this MedalTaskContainer self)
        {
#if TEST
            // 测试环境下直接返回，避免调用LobbyServiceManager
            return;
#else
            var parameters = new Dictionary<string, string> { ["roleID"] = self.Player.RoleId.ToString() };

            LobbyServiceManager.Instance.GetFromLobby(LobbyPath.PLAYER_PULL_STYLE_RANK, parameters, ETargetThreadType.Logic,
                self.OnPullStyleRankResponse);
#endif
        }

        /// <summary>
        /// 处理拉取风格段位的响应
        /// </summary>
        /// <param name="self">勋章组件实例</param>
        /// <param name="content">响应内容</param>
        /// <param name="statusCode">HTTP状态码</param>
        /// <param name="errorCode">错误码</param>
        private static void OnPullStyleRankResponse(this MedalTaskContainer self, JSONNode content,
            System.Net.HttpStatusCode statusCode, int errorCode)
        {
            if (errorCode != 0 || statusCode != System.Net.HttpStatusCode.OK || content == null)
            {
                self.Logger.Error(
                    $"pullStyleRankFromLobby failed for roleId:{self.Player.RoleId}, errorCode:{errorCode}, statusCode:{statusCode}");
                return;
            }

            self.DealStyleRankResponse(content.ToString());
        }

        // 处理风格段位响应数据
        private static void DealStyleRankResponse(this MedalTaskContainer self, string jsonString)
        {
            if (string.IsNullOrEmpty(jsonString))
            {
                self.Logger.Error($"pullStyleRankFromLobby: Empty response for roleId:{self.Player.RoleId}");
                return;
            }

            var response = Newtonsoft.Json.JsonConvert.DeserializeObject<InnerPullStyleRankRsp>(jsonString);
            if (response?.StyleRank == null)
            {
                self.Logger.Error($"pullStyleRankFromLobby: Invalid response format for roleId:{self.Player.RoleId}");
                return;
            }

            if (response.StyleRank.Count == 0)
            {
                self.Logger.Info($"pullStyleRankFromLobby: No style rank data found for roleId:{self.Player.RoleId}");
                return;
            }

            foreach (var (styleId, styleRank) in response.StyleRank)
            {
                self.StyleToRank[(int)styleId] = styleRank;
            }

            self.Logger.Info(
                $"pullStyleRankFromLobby success for roleId:{self.Player.RoleId}, styleRank count:{response.StyleRank.Count}");
        }

        /// <summary>
        /// 为本次差量同步生成勋章同步记录
        /// </summary>
        /// <param name="self">勋章任务容器实例</param>
        /// <param name="syncedMedals">本次同步的勋章信息列表(styleId, medalId, level, points, rank)</param>
        public static void GenerateSyncRecord(this MedalTaskContainer self,
            List<(int styleId, int medalId, int level, int points)> syncedMedals)
        {
            if (syncedMedals == null || syncedMedals.Count == 0)
                return;

            var currentTimestamp = TimeStampUtil.GetNowSec();

            // 按风格ID分组生成同步记录
            var styleGroups = new Dictionary<int, (int totalPoints, BasicValueDictionary<int, int> medals)>();

            foreach (var (styleId, medalId, level, points) in syncedMedals)
            {
                var (currentPoints, medals) = styleGroups.GetValueOrDefault(styleId);
                medals ??= new BasicValueDictionary<int, int>();

                styleGroups[styleId] = (currentPoints + points, medals);

                if (medals.TryGetValue(medalId, out var currentLevel))
                {
                    medals[medalId] = Math.Max(currentLevel, level);
                }
                else
                {
                    medals[medalId] = level;
                }
            }

            // 为每个风格生成一条同步记录
            foreach (var (styleId, (totalPoints, medals)) in styleGroups)
            {
                self.StyleToRank.TryGetValue(styleId, out var rank);
                var syncRecord = new MedalRecord(currentTimestamp, styleId, totalPoints, rank, medals);
                self.MedalSyncRecord.Add(syncRecord);
            }
        }

        public static void OnGetMedalSettleSwitch(this MedalTaskContainer self, JSONNode content, System.Net.HttpStatusCode statusCode, int errorCode)
        {
            if (errorCode != 0 || statusCode != System.Net.HttpStatusCode.OK || content == null)
            {
                self.Logger.Error(
                    $"[OnGetMedalSettleSwitch] failed for roleId:{self.Player.RoleId}, errorCode:{errorCode}, statusCode:{statusCode}");
                return;
            }

            var response = Newtonsoft.Json.JsonConvert.DeserializeObject<GetSettleStyleRankPointsSwitchResp>(content.ToString());
            if (response == null)
            {
                self.Logger.Error($"[OnGetMedalSettleSwitch] Invalid response format for roleId:{self.Player.RoleId}");
                return;
            }

            var oldSwitch = FunctionSwitchComponent.Instance.IsEnable(FunctionConst.MedalTask);
            var newSwitch = response.BattleServerId != ProcessEntity.Instance.ServerId;
            // 本服未开启结算则清理掉未结算的勋章, 即使已完成, 保留结算记录
            if (oldSwitch && !newSwitch)
            {
                self.Logger.Info($"[OnGetMedalSettleSwitch] Medal settle switch off for roleId:{self.Player.RoleId}");
                FunctionSwitchComponent.Instance.SetEnable(FunctionConst.MedalTask, false);
                self.RemoveContainerTask(TaskNodeIndex.InProgress);
                self.RemoveContainerTask(TaskNodeIndex.CompletedAndNotGetReward);
            }
            else if (!oldSwitch && newSwitch)
            {
                self.Logger.Info($"[OnGetMedalSettleSwitch] Medal settle switch on for roleId:{self.Player.RoleId}");
                FunctionSwitchComponent.Instance.SetEnable(FunctionConst.MedalTask, true);
            }
        }
    }
}