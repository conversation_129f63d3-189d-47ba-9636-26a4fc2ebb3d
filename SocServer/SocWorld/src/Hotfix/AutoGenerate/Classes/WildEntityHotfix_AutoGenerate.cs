using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using MessagePack;
using System.Buffers;
using System.Linq;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Entity
{
    public partial class WildEntityHotfix
    {


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(1994425385, new Action<WildEntity>(Init));
            WildEntityHotfixWrapper.InitImpl = Init;
            HotfixDelegateHelper.Register(14115640, new Action<WildEntity, bool>(PostInit));
            WildEntityHotfixWrapper.PostInitImpl = PostInit;
            HotfixDelegateHelper.Register(1871697386, new Action<WildEntity>(Cleanup));
            WildEntityHotfixWrapper.CleanupImpl = Cleanup;
            HotfixDelegateHelper.Register(1576263126, new Func<WildEntity, FixSizeArrayWriter, ReadOnlySequence<byte>>(SerializeForPhoto));
            WildEntityHotfixWrapper.SerializeForPhotoImpl = SerializeForPhoto;



        }
    }
}
