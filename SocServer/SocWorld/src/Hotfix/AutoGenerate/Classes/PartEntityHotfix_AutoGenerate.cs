using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using SimpleJSON;
using System.Net;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Ability;
using WizardGames.Soc.SocWorld.Construction;
using WizardGames.Soc.SocWorld.Entity;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.Spawn;

namespace WizardGames.Soc.Common.Entity
{
    public partial class PartEntityHotfix
    {


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(2097128473, new Action<PartEntity>(Init));
            PartEntityHotfixWrapper.InitImpl = Init;
            HotfixDelegateHelper.Register(1609618138, new Action<PartEntity>(InitFromDb));
            PartEntityHotfixWrapper.InitFromDbImpl = InitFromDb;
            HotfixDelegateHelper.Register(451302725, new Action<PartEntity>(BeforeRemove));
            PartEntityHotfixWrapper.BeforeRemoveImpl = BeforeRemove;



        }
    }
}
