using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.NodeSystem;

namespace WizardGames.Soc.SocWorld.CustomType.RootNode
{
    public partial class BlueprintHotfix
    {


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(207038071, new Func<BlueprintRootNode, INodeOpData, NodeOpContext, EOpCode>(InputVirtual));
            BlueprintHotfixWrapper.InputImpl = InputVirtual;
            HotfixDelegateHelper.Register(86483161, new Func<BlueprintRootNode, long, NodeBase>(GetChildNodeVirtual));
            BlueprintHotfixWrapper.GetChildNodeImpl = GetChildNodeVirtual;



        }
    }
}
