using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld.Spawn;
using WizardGames.Soc.SocWorld.WorldCommon;

namespace WizardGames.Soc.SocWorld
{
    public partial class BotEventsHotfix
    {


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(1724489068, new Func<BotEvents, string>(ToLogString));



        }
    }
}
