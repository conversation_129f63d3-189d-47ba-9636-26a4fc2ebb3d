using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld.Entity;
using WizardGames.Soc.SocWorld.RuleGraph.Function;

namespace WizardGames.Soc.SocWorld.RuleGraph
{
    public partial class SpawnHorseNodeHotfix
    {
        private static readonly Action<BaseNode, FlowContext> BaseOnStart = BaseNodeHotfix.OnStart;


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(2128241483, new Action<SpawnHorseNode, FlowContext, object>(OnFinish));
            SpawnHorseNodeHotfixWrapper.OnFinishImpl = OnFinish;
            HotfixDelegateHelper.Register(1422780923, new Action<SpawnHorseNode, FlowContext>(OnStart));
            SpawnHorseNodeHotfixWrapper.OnStartImpl = OnStart;



        }
    }
}
