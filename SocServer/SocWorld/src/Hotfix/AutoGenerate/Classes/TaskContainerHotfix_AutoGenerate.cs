using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using System.Collections.Generic;
using System.Linq;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.ClassImpl.Task;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.NodeSystem.Table;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.CustomType
{
    public partial class TaskContainerHotfix
    {
        public static readonly ArchiveTimerCallback<PlayerTaskComponent, TimerLongParam> TaskTimerCallbackHotfix = new(TaskTimerCallback, 1160886856);
        public static readonly ArchiveTimerCallback<PlayerTaskComponent, TimerLongAndLongParam> DelayAcceptTaskHotfix = new(DelayAcceptTask, 1833644357);


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(2056118711, new Action<TaskContainer>(Init));
            TaskContainerHotfixWrapper.InitImpl = Init;
            HotfixDelegateHelper.Register(1415165407, new Action<TaskContainer, bool>(PostInit));
            TaskContainerHotfixWrapper.PostInitImpl = PostInit;
            HotfixDelegateHelper.Register(1766059357, new Func<TaskContainer, QuestPhase, TaskNode>(CreateTaskNodeVirtual));
            TaskContainerHotfixWrapper.CreateTaskNodeImpl = CreateTaskNodeVirtual;
            HotfixDelegateHelper.Register(1724979915, new Func<TaskContainer, long, bool, bool>(AcceptTaskVirtual));
            TaskContainerHotfixWrapper.AcceptTaskImpl = AcceptTaskVirtual;
            HotfixDelegateHelper.Register(1446427646, new Func<TaskContainer, TaskNode, EOpCode>(CompleteTaskVirtual));
            TaskContainerHotfixWrapper.CompleteTaskImpl = CompleteTaskVirtual;
            HotfixDelegateHelper.Register(1160886856, new Action<PlayerTaskComponent, long, TimerLongParam>(TaskTimerCallback));
            HotfixDelegateHelper.Register(1833644357, new Action<PlayerTaskComponent, long, TimerLongAndLongParam>(DelayAcceptTask));

            HotfixRegister.HotfixableTimerDict.Add(1160886856, TaskTimerCallbackHotfix);
            HotfixRegister.HotfixableTimerDict.Add(1833644357, DelayAcceptTaskHotfix);


        }
    }
}
