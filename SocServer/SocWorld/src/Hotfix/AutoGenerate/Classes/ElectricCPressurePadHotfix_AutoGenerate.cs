using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.Electric;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.CustomType
{
    public partial class ElectricCPressurePadHotfix
    {
        private static readonly Action<ElectricCBase> BaseCleanup = ElectricCBaseHotfix.Cleanup;
        private static readonly Func<ElectricCBase, int, int> BaseGetPassthroughAmount = ElectricCBaseHotfix.GetPassthroughAmount;
        private static readonly Func<ElectricCBase, int, bool> BaseAllowDrainFrom = ElectricCBaseHotfix.AllowDrainFrom;
        public static readonly ArchiveTimerCallback<PartEntity, TimerLongParam> CPressurePadOnEmitPowerTimerHotfix = new(CPressurePadOnEmitPowerTimer, 2008695505);


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(466280964, new Action<ElectricCPressurePad>(Cleanup));
            ElectricCPressurePadHotfixWrapper.CleanupImpl = Cleanup;
            HotfixDelegateHelper.Register(1468049948, new Func<ElectricCPressurePad, int, int>(GetPassthroughAmount));
            ElectricCPressurePadHotfixWrapper.GetPassthroughAmountImpl = GetPassthroughAmount;
            HotfixDelegateHelper.Register(580995411, new Func<ElectricCPressurePad, int, bool>(AllowDrainFrom));
            ElectricCPressurePadHotfixWrapper.AllowDrainFromImpl = AllowDrainFrom;
            HotfixDelegateHelper.Register(2008695505, new Action<PartEntity, long, TimerLongParam>(CPressurePadOnEmitPowerTimer));

            HotfixRegister.HotfixableTimerDict.Add(2008695505, CPressurePadOnEmitPowerTimerHotfix);


        }
    }
}
