
// Auto-generated file, do not edit manually.

using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.SocWorld.RuleGraph.Function
{
    public static class VariableFunctionLibrary
    {

        /// <summary>
        /// 设置布尔变量
        /// </summary>
        /// <param name="name">布尔变量</param>
        [GraphNode]
        public static void SetVariableBool(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(bool))] long name, bool value)
        {
            context.SetBool(name, value);
        }

        /// <summary>
        /// 获取布尔变量
        /// </summary>
        /// <param name="name">布尔变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static bool GetVariableBool(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(bool))] long name)
        {
            return context.GetBool(name);
        }

        /// <summary>
        /// 设置Float变量
        /// </summary>
        /// <param name="name">Float变量</param>
        [GraphNode]
        public static void SetVariableFloat(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(float))] long name, float value)
        {
            context.SetFloat(name, value);
        }

        /// <summary>
        /// 获取Float变量
        /// </summary>
        /// <param name="name">Float变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static float GetVariableFloat(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(float))] long name)
        {
            return context.GetFloat(name);
        }

        /// <summary>
        /// 设置Long变量
        /// </summary>
        /// <param name="name">Long变量</param>
        [GraphNode]
        public static void SetVariableLong(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(long))] long name, long value)
        {
            context.SetLong(name, value);
        }

        /// <summary>
        /// 获取Long变量
        /// </summary>
        /// <param name="name">Long变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static long GetVariableLong(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(long))] long name)
        {
            return context.GetLong(name);
        }

        /// <summary>
        /// 设置String变量
        /// </summary>
        /// <param name="name">String变量</param>
        [GraphNode]
        public static void SetVariableString(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(string))] long name, string value)
        {
            context.SetString(name, value ?? string.Empty);
        }

        /// <summary>
        /// 获取String变量
        /// </summary>
        /// <param name="name">String变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static string GetVariableString(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(string))] long name)
        {
            return context.GetString(name);
        }

        /// <summary>
        /// 设置Vector3变量
        /// </summary>
        /// <param name="name">Vector3变量</param>
        [GraphNode]
        public static void SetVariableVector3(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(SimpleVector3))] long name, CustomVector3 value)
        {
            context.SetObject(name, value ?? new CustomVector3());
        }

        /// <summary>
        /// 获取Vector3变量
        /// </summary>
        /// <param name="name">Vector3变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomVector3 GetVariableVector3(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(SimpleVector3))] long name)
        {
            return (CustomVector3)context.GetObject(name);
        }

        /// <summary>
        /// 设置布尔列表变量
        /// </summary>
        /// <param name="name">布尔列表变量</param>
        [GraphNode]
        public static void SetVariableListBool(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<bool>))] long name, BasicTypeList<bool> value)
        {
            context.SetObject(name, value ?? new BasicTypeList<bool>());
        }

        /// <summary>
        /// 获取布尔列表变量
        /// </summary>
        /// <param name="name">布尔列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicTypeList<bool> GetVariableListBool(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<bool>))] long name)
        {
            return (BasicTypeList<bool>)context.GetObject(name);
        }

        /// <summary>
        /// 设置Float列表变量
        /// </summary>
        /// <param name="name">Float列表变量</param>
        [GraphNode]
        public static void SetVariableListFloat(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<float>))] long name, BasicTypeList<float> value)
        {
            context.SetObject(name, value ?? new BasicTypeList<float>());
        }

        /// <summary>
        /// 获取Float列表变量
        /// </summary>
        /// <param name="name">Float列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicTypeList<float> GetVariableListFloat(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<float>))] long name)
        {
            return (BasicTypeList<float>)context.GetObject(name);
        }

        /// <summary>
        /// 设置Long列表变量
        /// </summary>
        /// <param name="name">Long列表变量</param>
        [GraphNode]
        public static void SetVariableListLong(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<long>))] long name, BasicTypeList<long> value)
        {
            context.SetObject(name, value ?? new BasicTypeList<long>());
        }

        /// <summary>
        /// 获取Long列表变量
        /// </summary>
        /// <param name="name">Long列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicTypeList<long> GetVariableListLong(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<long>))] long name)
        {
            return (BasicTypeList<long>)context.GetObject(name);
        }

        /// <summary>
        /// 设置String列表变量
        /// </summary>
        /// <param name="name">String列表变量</param>
        [GraphNode]
        public static void SetVariableListString(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<string>))] long name, BasicTypeList<string> value)
        {
            context.SetObject(name, value ?? new BasicTypeList<string>());
        }

        /// <summary>
        /// 获取String列表变量
        /// </summary>
        /// <param name="name">String列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicTypeList<string> GetVariableListString(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<string>))] long name)
        {
            return (BasicTypeList<string>)context.GetObject(name);
        }

        /// <summary>
        /// 设置Vector3列表变量
        /// </summary>
        /// <param name="name">Vector3列表变量</param>
        [GraphNode]
        public static void SetVariableListVector3(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<SimpleVector3>))] long name, CustomTypeList<CustomVector3> value)
        {
            context.SetObject(name, value ?? new CustomTypeList<CustomVector3>());
        }

        /// <summary>
        /// 获取Vector3列表变量
        /// </summary>
        /// <param name="name">Vector3列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomTypeList<CustomVector3> GetVariableListVector3(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<SimpleVector3>))] long name)
        {
            return (CustomTypeList<CustomVector3>)context.GetObject(name);
        }

        /// <summary>
        /// 设置布尔字典变量
        /// </summary>
        /// <param name="name">布尔字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongBool(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,bool>))] long name, BasicValueDictionary<long, bool> value)
        {
            context.SetObject(name, value ?? new BasicValueDictionary<long, bool>());
        }

        /// <summary>
        /// 获取布尔字典变量
        /// </summary>
        /// <param name="name">布尔字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicValueDictionary<long, bool> GetVariableDictionaryLongBool(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,bool>))] long name)
        {
            return (BasicValueDictionary<long, bool>)context.GetObject(name);
        }

        /// <summary>
        /// 设置Float字典变量
        /// </summary>
        /// <param name="name">Float字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongFloat(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,float>))] long name, BasicValueDictionary<long, float> value)
        {
            context.SetObject(name, value ?? new BasicValueDictionary<long, float>());
        }

        /// <summary>
        /// 获取Float字典变量
        /// </summary>
        /// <param name="name">Float字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicValueDictionary<long, float> GetVariableDictionaryLongFloat(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,float>))] long name)
        {
            return (BasicValueDictionary<long, float>)context.GetObject(name);
        }

        /// <summary>
        /// 设置Long字典变量
        /// </summary>
        /// <param name="name">Long字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongLong(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,long>))] long name, BasicValueDictionary<long, long> value)
        {
            context.SetObject(name, value ?? new BasicValueDictionary<long, long>());
        }

        /// <summary>
        /// 获取Long字典变量
        /// </summary>
        /// <param name="name">Long字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicValueDictionary<long, long> GetVariableDictionaryLongLong(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,long>))] long name)
        {
            return (BasicValueDictionary<long, long>)context.GetObject(name);
        }

        /// <summary>
        /// 设置String字典变量
        /// </summary>
        /// <param name="name">String字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongString(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,string>))] long name, BasicValueDictionary<long, string> value)
        {
            context.SetObject(name, value ?? new BasicValueDictionary<long, string>());
        }

        /// <summary>
        /// 获取String字典变量
        /// </summary>
        /// <param name="name">String字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static BasicValueDictionary<long, string> GetVariableDictionaryLongString(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,string>))] long name)
        {
            return (BasicValueDictionary<long, string>)context.GetObject(name);
        }

        /// <summary>
        /// 设置Vector3字典变量
        /// </summary>
        /// <param name="name">Vector3字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongVector3(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))] long name, CustomValueDictionary<long,CustomVector3> value)
        {
            context.SetObject(name, value ?? new CustomValueDictionary<long,CustomVector3>());
        }

        /// <summary>
        /// 获取Vector3字典变量
        /// </summary>
        /// <param name="name">Vector3字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomValueDictionary<long,CustomVector3> GetVariableDictionaryLongVector3(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))] long name)
        {
            return (CustomValueDictionary<long,CustomVector3>)context.GetObject(name);
        }

        /// <summary>
        /// 设置示例类型变量
        /// </summary>
        /// <param name="name">示例类型变量</param>
        [GraphNode]
        public static void SetVariableCustomTypeExample(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(CustomTypeExampleCfg))] long name, CustomTypeExample value)
        {
            context.SetObject(name, value ?? new CustomTypeExample());
        }

        /// <summary>
        /// 获取示例类型变量
        /// </summary>
        /// <param name="name">示例类型变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomTypeExample GetVariableCustomTypeExample(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(CustomTypeExampleCfg))] long name)
        {
            return (CustomTypeExample)context.GetObject(name);
        }

        /// <summary>
        /// 设置示例类型列表变量
        /// </summary>
        /// <param name="name">示例类型列表变量</param>
        [GraphNode]
        public static void SetVariableListCustomTypeExample(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<CustomTypeExampleCfg>))] long name, CustomTypeList<CustomTypeExample> value)
        {
            context.SetObject(name, value ?? new CustomTypeList<CustomTypeExample>());
        }

        /// <summary>
        /// 获取示例类型列表变量
        /// </summary>
        /// <param name="name">示例类型列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomTypeList<CustomTypeExample> GetVariableListCustomTypeExample(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<CustomTypeExampleCfg>))] long name)
        {
            return (CustomTypeList<CustomTypeExample>)context.GetObject(name);
        }

        /// <summary>
        /// 设置示例类型字典变量
        /// </summary>
        /// <param name="name">示例类型字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongCustomTypeExample(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long, CustomTypeExampleCfg>))] long name, CustomValueDictionary<long, CustomTypeExample> value)
        {
            context.SetObject(name, value ?? new CustomValueDictionary<long, CustomTypeExample>());
        }

        /// <summary>
        /// 获取示例类型字典变量
        /// </summary>
        /// <param name="name">示例类型字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomValueDictionary<long, CustomTypeExample> GetVariableDictionaryLongCustomTypeExample(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long, CustomTypeExampleCfg>))] long name)
        {
            return (CustomValueDictionary<long, CustomTypeExample>)context.GetObject(name);
        }

        /// <summary>
        /// 设置ItemCount变量
        /// </summary>
        /// <param name="name">ItemCount变量</param>
        [GraphNode]
        public static void SetVariableItemCount(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(ItemCountCfg))] long name, ItemCount value)
        {
            context.SetObject(name, value ?? new ItemCount());
        }

        /// <summary>
        /// 获取ItemCount变量
        /// </summary>
        /// <param name="name">ItemCount变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static ItemCount GetVariableItemCount(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(ItemCountCfg))] long name)
        {
            return (ItemCount)context.GetObject(name);
        }

        /// <summary>
        /// 设置ItemCount列表变量
        /// </summary>
        /// <param name="name">ItemCount列表变量</param>
        [GraphNode]
        public static void SetVariableListItemCount(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<ItemCountCfg>))] long name, CustomTypeList<ItemCount> value)
        {
            context.SetObject(name, value ?? new CustomTypeList<ItemCount>());
        }

        /// <summary>
        /// 获取ItemCount列表变量
        /// </summary>
        /// <param name="name">ItemCount列表变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomTypeList<ItemCount> GetVariableListItemCount(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(List<ItemCountCfg>))] long name)
        {
            return (CustomTypeList<ItemCount>)context.GetObject(name);
        }

        /// <summary>
        /// 设置ItemCount字典变量
        /// </summary>
        /// <param name="name">ItemCount字典变量</param>
        [GraphNode]
        public static void SetVariableDictionaryLongItemCount(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long, ItemCountCfg>))] long name, CustomValueDictionary<long, ItemCount> value)
        {
            context.SetObject(name, value ?? new CustomValueDictionary<long, ItemCount>());
        }

        /// <summary>
        /// 获取ItemCount字典变量
        /// </summary>
        /// <param name="name">ItemCount字典变量</param>
        [GraphValue]
        [GraphVariableGetter]
        public static CustomValueDictionary<long, ItemCount> GetVariableDictionaryLongItemCount(FlowContext context, [ConstDataIn] [SocVarGuid(typeof(Dictionary<long, ItemCountCfg>))] long name)
        {
            return (CustomValueDictionary<long, ItemCount>)context.GetObject(name);
        }

    }
}

