
using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;

namespace WizardGames.Soc.SocWorld.RuleGraph
{
    [GraphFunctionLibrary("CustomVector3Dict", Category = "Dict")]
    public static class XCustomVector3DictFunctionLibrary
    {
        [GraphValue]
        public static bool ContainsKey(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))]
            long name,
            long key)
        {
            return CustomValueDictUtils.ContainsKey<long, CustomVector3>(context, name, key);
        }

        [GraphValue]
        public static long Count(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))]
            long name)
        {
            return CustomValueDictUtils.Count<long, CustomVector3>(context, name);
        }

        [GraphValue]
        public static CustomVector3 Get(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))]
            long name,
            long key,
            [ConstDataIn] bool createNew
        )
        {
            return CustomValueDictUtils.Get<long, CustomVector3>(context, name, key, createNew);
        }

        /// <summary>
        /// 添加元素到CustomVector3字典(最多100个元素)
        /// 1. 拷贝值
        /// </summary>
        /// <param name="context"></param>
        /// <param name="name"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        [GraphNode]
        public static void Set(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))]
            long name,
            long key,
            CustomVector3 value
        )
        {
            CustomValueDictUtils.Set(context, name, key, value);
        }

        [GraphNode]
        public static void Remove(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))]
            long name,
            long key
        )
        {
            CustomValueDictUtils.Remove<long, CustomVector3>(context, name, key);
        }

        [GraphNode]
        public static void Clear(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(Dictionary<long,SimpleVector3>))]
            long name
        )
        {
            CustomValueDictUtils.Clear<long, CustomVector3>(context, name);
        }
    }
}
