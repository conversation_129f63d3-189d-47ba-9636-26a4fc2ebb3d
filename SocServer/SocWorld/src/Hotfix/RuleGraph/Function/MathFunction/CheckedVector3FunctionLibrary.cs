using Soc.Common.CodeParser.RuleGraph;
using System;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;

namespace WizardGames.Soc.SocWorld.RuleGraph.Function
{
    [GraphFunctionLibrary("Vector3", Category = "Math")]
    public static class CheckedVector3FunctionLibrary
    {
        [GraphValue]
        public static CustomVector3 Create(float x, float y, float z)
        {
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     更新向量 X, Y, Z
        /// </summary>
        [GraphNode]
        public static void Set(
            FlowContext context,
            [ConstDataIn] [SocVarGuid(typeof(SimpleVector3))]
            long name,
            float x, float y, float z)
        {
            CustomVector3 v = (CustomVector3)context.GetObject(name);
            v.X = x;
            v.Y = y;
            v.Z = z;
        }

        /// <summary>
        ///     获取向量 X
        /// </summary>
        /// <param name="a"></param>
        /// <returns></returns>
        [GraphValue]
        public static float GetX(CustomVector3 a)
        {
            FloatUtil.CheckFloat(a.X);
            return a.X;
        }

        /// <summary>
        ///     获取向量 Y
        /// </summary>
        /// <param name="a"></param>
        /// <returns></returns>
        [GraphValue]
        public static float GetY(CustomVector3 a)
        {
            FloatUtil.CheckFloat(a.Y);
            return a.Y;
        }

        /// <summary>
        ///     获取向量 Z
        /// </summary>
        /// <param name="a"></param>
        /// <returns></returns>
        [GraphValue]
        public static float GetZ(CustomVector3 a)
        {
            FloatUtil.CheckFloat(a.Z);
            return a.Z;
        }

        /// <summary>
        ///     加
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        [GraphBinaryOperator(OpType = EBinaryOpType.Add)]
        public static CustomVector3 Add(CustomVector3 a, CustomVector3 b)
        {
            float x = a.X + b.X;
            float y = a.Y + b.Y;
            float z = a.Z + b.Z;
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     减
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        [GraphBinaryOperator(OpType = EBinaryOpType.Sub)]
        public static CustomVector3 Sub(CustomVector3 a, CustomVector3 b)
        {
            float x = a.X - b.X;
            float y = a.Y - b.Y;
            float z = a.Z - b.Z;
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     乘
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        [GraphBinaryOperator(OpType = EBinaryOpType.Mul)]
        public static CustomVector3 Mul(CustomVector3 a, float b)
        {
            FloatUtil.CheckFloat(b);
            float x = a.X * b;
            float y = a.Y * b;
            float z = a.Z * b;
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     除
        ///     1. b 不能为 0, 否则取值失败
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        /// <exception cref="DivideByZeroException"></exception>
        [GraphValue]
        [GraphBinaryOperator(OpType = EBinaryOpType.Div)]
        public static CustomVector3 Div(CustomVector3 a, [GraphNotZero] float b)
        {
            FloatUtil.CheckFloat(b);

            if (b == 0)
            {
                throw new DivideByZeroException("Cannot divide by zero.");
            }

            float x = a.X / b;
            float y = a.Y / b;
            float z = a.Z / b;
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     点乘
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        public static float Dot(CustomVector3 a, CustomVector3 b)
        {
            float result = (a.X * b.X) + (a.Y * b.Y) + (a.Z * b.Z);
            FloatUtil.CheckFloat(result);
            return result;
        }

        /// <summary>
        ///     叉乘
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        public static CustomVector3 Cross(CustomVector3 a, CustomVector3 b)
        {
            float x = (a.Y * b.Z) - (a.Z * b.Y);
            float y = (a.Z * b.X) - (a.X * b.Z);
            float z = (a.X * b.Y) - (a.Y * b.X);
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     计算长度
        /// </summary>
        /// <param name="a"></param>
        /// <returns></returns>
        [GraphValue]
        public static float Magnitude(CustomVector3 a)
        {
            float result = (float)Math.Sqrt((a.X * a.X) + (a.Y * a.Y) + (a.Z * a.Z));
            FloatUtil.CheckFloat(result);
            return result;
        }

        /// <summary>
        ///     计算距离
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        public static float Distance(CustomVector3 a, CustomVector3 b)
        {
            float x = a.X - b.X;
            float y = a.Y - b.Y;
            float z = a.Z - b.Z;
            FloatUtil.CheckFloat(x, y, z);
            float result = (float)Math.Sqrt((x * x) + (y * y) + (z * z));
            FloatUtil.CheckFloat(result);
            return result;
        }

        /// <summary>
        ///     归一化
        /// </summary>
        /// <param name="a"></param>
        /// <returns></returns>
        [GraphValue]
        public static CustomVector3 Normalize(CustomVector3 a)
        {
            float magnitude = Magnitude(a);
            if (magnitude < 1e-5f)
            {
                return new CustomVector3(0, 0, 0);
            }

            float x = a.X / magnitude;
            float y = a.Y / magnitude;
            float z = a.Z / magnitude;
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     线性插值
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <param name="t"></param>
        /// <returns></returns>
        [GraphValue]
        public static CustomVector3 Lerp(CustomVector3 a, CustomVector3 b, float t)
        {
            FloatUtil.CheckFloat(t);
            t = Math.Clamp(t, 0, 1);
            float x = a.X + ((b.X - a.X) * t);
            float y = a.Y + ((b.Y - a.Y) * t);
            float z = a.Z + ((b.Z - a.Z) * t);
            FloatUtil.CheckFloat(x, y, z);
            return new CustomVector3(x, y, z);
        }

        /// <summary>
        ///     夹角（角度）
        ///     1. a 和 b 内部会先归一化
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <returns></returns>
        [GraphValue]
        public static float AngleInDegree(CustomVector3 a, CustomVector3 b)
        {
            Normalize(a, out float aX, out float aY, out float aZ);
            Normalize(b, out float bX, out float bY, out float bZ);
            float dot = Math.Clamp((aX * bX) + (aY * bY) + (aZ * bZ), -1, 1);
            float angle = (float)(Math.Acos(dot) * (180 / Math.PI));
            FloatUtil.CheckFloat(angle);
            return angle;
        }

        private static void Normalize(CustomVector3 a, out float x, out float y, out float z)
        {
            x = a.X;
            y = a.Y;
            z = a.Z;
            float magnitude = Magnitude(a);
            if (magnitude < 1e-5f)
            {
                x = 0;
                y = 0;
                z = 0;
                return;
            }

            x /= magnitude;
            y /= magnitude;
            z /= magnitude;
            FloatUtil.CheckFloat(x, y, z);
        }

    #region Test

        private static void AssertEqual(CustomVector3 got, CustomVector3 expect, string @case)
        {
            if (Math.Abs(got.X - expect.X) > 1e-5f || Math.Abs(got.Y - expect.Y) > 1e-5f || Math.Abs(got.Z - expect.Z) > 1e-5f)
            {
                throw new Exception($"{@case} failed, expected {expect}, but got {got}");
            }
        }

        private static void AssertEqual(float got, float expect, string @case)
        {
            if (Math.Abs(got - expect) > 1e-5f)
            {
                throw new Exception($"{@case} failed, expected {expect}, but got {got}");
            }
        }

        public static void TestVector3()
        {
            AssertEqual(Create(1, 2, 3), new CustomVector3(1, 2, 3), "Create(1, 2, 3)");

            AssertEqual(Add(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6)), new CustomVector3(5, 7, 9), "Add(1, 2, 3), (4, 5, 6)");
            AssertEqual(Sub(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6)), new CustomVector3(-3, -3, -3), "Sub(1, 2, 3), (4, 5, 6)");
            AssertEqual(Mul(new CustomVector3(1, 2, 3), 2), new CustomVector3(2, 4, 6), "Mul(1, 2, 3), 2");
            AssertEqual(Div(new CustomVector3(1, 2, 3), 2), new CustomVector3(0.5f, 1, 1.5f), "Div(1, 2, 3), 2");

            AssertEqual(Dot(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6)), 32.0f, "Dot(1, 2, 3), (4, 5, 6)");
            AssertEqual(Cross(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6)), new CustomVector3(-3, 6, -3), "Cross(1, 2, 3), (4, 5, 6)");

            AssertEqual(Magnitude(new CustomVector3(1, 2, 3)), (float)Math.Sqrt(14), "Magnitude(1, 2, 3)");
            AssertEqual(Distance(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6)), (float)Math.Sqrt((3 * 3) + (3 * 3) + (3 * 3)), "Distance(1, 2, 3), (4, 5, 6)");
            AssertEqual(Normalize(new CustomVector3(1, 2, 3)), new CustomVector3(1 / (float)Math.Sqrt(14), 2 / (float)Math.Sqrt(14), 3 / (float)Math.Sqrt(14)), "Normalize(1, 2, 3)");
            AssertEqual(Normalize(new CustomVector3(0, 0, 0)), new CustomVector3(0, 0, 0), "Normalize(0, 0, 0)");

            AssertEqual(Lerp(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6), 0.5f), new CustomVector3(2.5f, 3.5f, 4.5f), "Lerp(1, 2, 3), (4, 5, 6), 0.5");
            AssertEqual(Lerp(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6), 0), new CustomVector3(1, 2, 3), "Lerp(1, 2, 3), (4, 5, 6), 0");
            AssertEqual(Lerp(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6), 1), new CustomVector3(4, 5, 6), "Lerp(1, 2, 3), (4, 5, 6), 1");
            AssertEqual(Lerp(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6), -1), new CustomVector3(1, 2, 3), "Lerp(1, 2, 3), (4, 5, 6), -1");
            AssertEqual(Lerp(new CustomVector3(1, 2, 3), new CustomVector3(4, 5, 6), 2), new CustomVector3(4, 5, 6), "Lerp(1, 2, 3), (4, 5, 6), 2");
        }

        public static void TestInvalidVector3()
        {
            try { Div(new CustomVector3(1, 2, 3), 0); }
            catch (DivideByZeroException) { }

            try { Add(new CustomVector3(1, 2, 3), new CustomVector3(float.NaN, 0, 0)); }
            catch (ArgumentException) { }

            try { Mul(new CustomVector3(1, 2, 3), float.NaN); }
            catch (ArgumentException) { }

            // 非正常结果
            try { Add(new CustomVector3(float.MaxValue, 0, 0), new CustomVector3(float.MaxValue, 0, 0)); }
            catch (ArgumentException) { }
        }

        public static void TestAngleInDegree()
        {
            if (Math.Abs(AngleInDegree(new CustomVector3(1, 0, 0), new CustomVector3(0, 1, 0)) - 90) > 1e-5f)
            {
                throw new Exception("Test failed");
            }

            if (Math.Abs(AngleInDegree(new CustomVector3(-1, 0, 0), new CustomVector3(0, 1, 0)) - 90) > 1e-5f)
            {
                throw new Exception("Test failed");
            }
        }

    #endregion
    }
}