>InitEntity PlayerEntity.42769
>PlayerEntity.42769 LifeCycleFlags set to [1]
>ServerInstanceEntity.12 FunctionSwitchComponent Root.Nodes.32776.Value set to [False]
>PlayerEntity.42769 PlayerTaskComponent
>PlayerEntity.42769 PlayerTaskComponent SystemRoot set to [19(19)[19] PlayerTaskRootNode]
>PlayerEntity.42769 PlayerTaskComponent Guide2Count set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.ChildDict.6 set to [6]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6 set to [6(6)[6] BadgeTaskContainer]
>PlayerEntity.42769 RootNodeComponent Node2System.6 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.CompletedTaskIds set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.TaskSpawnInfos set to [{

}]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.TaskSpawnEntities set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.SpawnEntity2TimerId set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.TaskSpawnEntitiesOnlySelf set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.ChildDict.1 set to [61036]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036 set to [1(1)[61036] DirectoryNode]
>PlayerEntity.42769 RootNodeComponent Node2System.61036 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.ChildDict.2 set to [61044]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61044 set to [2(2)[61044] DirectoryNode]
>PlayerEntity.42769 RootNodeComponent Node2System.61044 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020001 set to [100000]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100000 set to [TaskNode-100000 BizId 2020001 Index 2020001 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100000 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100001 set to [SubTaskNode--2020001--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100001 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020002 set to [100002]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100002 set to [TaskNode-100002 BizId 2020002 Index 2020002 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100002 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100003 set to [SubTaskNode--2020002--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100003 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020003 set to [100004]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100004 set to [TaskNode-100004 BizId 2020003 Index 2020003 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100004 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100005 set to [SubTaskNode--2020003--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100005 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020004 set to [100006]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100006 set to [TaskNode-100006 BizId 2020004 Index 2020004 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100006 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100007 set to [SubTaskNode--2020004--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100007 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020005 set to [100008]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100008 set to [TaskNode-100008 BizId 2020005 Index 2020005 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100008 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100009 set to [SubTaskNode--2020005--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100009 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020006 set to [100010]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100010 set to [TaskNode-100010 BizId 2020006 Index 2020006 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100010 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100011 set to [SubTaskNode--2020006--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100011 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020007 set to [100012]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100012 set to [TaskNode-100012 BizId 2020007 Index 2020007 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100012 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100013 set to [SubTaskNode--2020007--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100013 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020008 set to [100014]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100014 set to [TaskNode-100014 BizId 2020008 Index 2020008 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100014 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100015 set to [SubTaskNode--2020008--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100015 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020009 set to [100016]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100016 set to [TaskNode-100016 BizId 2020009 Index 2020009 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100016 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100017 set to [SubTaskNode--2020009--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100017 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020010 set to [100018]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100018 set to [TaskNode-100018 BizId 2020010 Index 2020010 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100018 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100019 set to [SubTaskNode--2020010--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100019 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020011 set to [100020]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100020 set to [TaskNode-100020 BizId 2020011 Index 2020011 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100020 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100021 set to [SubTaskNode--2020011--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100021 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020012 set to [100022]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100022 set to [TaskNode-100022 BizId 2020012 Index 2020012 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100022 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100023 set to [SubTaskNode--2020012--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100023 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020013 set to [100024]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100024 set to [TaskNode-100024 BizId 2020013 Index 2020013 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100024 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100025 set to [SubTaskNode--2020013--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100025 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020014 set to [100026]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100026 set to [TaskNode-100026 BizId 2020014 Index 2020014 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100026 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100027 set to [SubTaskNode--2020014--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100027 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020015 set to [100028]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100028 set to [TaskNode-100028 BizId 2020015 Index 2020015 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100028 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100029 set to [SubTaskNode--2020015--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100029 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020016 set to [100030]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100030 set to [TaskNode-100030 BizId 2020016 Index 2020016 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100030 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100031 set to [SubTaskNode--2020016--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100031 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020017 set to [100032]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100032 set to [TaskNode-100032 BizId 2020017 Index 2020017 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100032 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100033 set to [SubTaskNode--2020017--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100033 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020018 set to [100034]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100034 set to [TaskNode-100034 BizId 2020018 Index 2020018 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100034 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100035 set to [SubTaskNode--2020018--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100035 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020019 set to [100036]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100036 set to [TaskNode-100036 BizId 2020019 Index 2020019 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100036 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100037 set to [SubTaskNode--2020019--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100037 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020020 set to [100038]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100038 set to [TaskNode-100038 BizId 2020020 Index 2020020 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100038 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100039 set to [SubTaskNode--2020020--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100039 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020021 set to [100040]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100040 set to [TaskNode-100040 BizId 2020021 Index 2020021 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100040 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100041 set to [SubTaskNode--2020021--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100041 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020022 set to [100042]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100042 set to [TaskNode-100042 BizId 2020022 Index 2020022 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100042 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100043 set to [SubTaskNode--2020022--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100043 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020023 set to [100044]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100044 set to [TaskNode-100044 BizId 2020023 Index 2020023 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100044 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100045 set to [SubTaskNode--2020023--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100045 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020024 set to [100046]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100046 set to [TaskNode-100046 BizId 2020024 Index 2020024 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100046 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100047 set to [SubTaskNode--2020024--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100047 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020025 set to [100048]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100048 set to [TaskNode-100048 BizId 2020025 Index 2020025 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100048 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100049 set to [SubTaskNode--2020025--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100049 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020026 set to [100050]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100050 set to [TaskNode-100050 BizId 2020026 Index 2020026 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100050 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100051 set to [SubTaskNode--2020026--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100051 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020027 set to [100052]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100052 set to [TaskNode-100052 BizId 2020027 Index 2020027 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100052 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100053 set to [SubTaskNode--2020027--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100053 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020028 set to [100054]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100054 set to [TaskNode-100054 BizId 2020028 Index 2020028 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100054 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100055 set to [SubTaskNode--2020028--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100055 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020029 set to [100056]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100056 set to [TaskNode-100056 BizId 2020029 Index 2020029 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100056 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100057 set to [SubTaskNode--2020029--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100057 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036.ChildDict.2020030 set to [100058]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100058 set to [TaskNode-100058 BizId 2020030 Index 2020030 Count 0 Type 6 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100058 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100059 set to [SubTaskNode--2020030--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100059 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot set to [19(19)[19] PlayerTaskRootNode]
>PlayerEntity.42769 PlayerTaskComponent Guide2Count set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.ChildDict.6 set to [6]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6 set to [6(6)[6] BadgeTaskContainer]
>PlayerEntity.42769 RootNodeComponent Node2System.6 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.CompletedTaskIds set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.TaskSpawnInfos set to [{

}]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.TaskSpawnEntities set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.SpawnEntity2TimerId set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.TaskSpawnEntitiesOnlySelf set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.ChildDict.1 set to [61036]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61036 set to [1(1)[61036] DirectoryNode]
>PlayerEntity.42769 RootNodeComponent Node2System.61036 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.6.ChildDict.2 set to [61044]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.61044 set to [2(2)[61044] DirectoryNode]
>PlayerEntity.42769 RootNodeComponent Node2System.61044 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.ChildDict.10 set to [10]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10 set to [10(10)[10] MedalTaskContainer]
>PlayerEntity.42769 RootNodeComponent Node2System.10 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.CompletedTaskIds set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.TaskSpawnInfos set to [{

}]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.TaskSpawnEntities set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.SpawnEntity2TimerId set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.TaskSpawnEntitiesOnlySelf set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.ChildDict.1 set to [101036]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101036 set to [1(1)[101036] DirectoryNode]
>PlayerEntity.42769 RootNodeComponent Node2System.101036 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.ChildDict.2 set to [101044]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101044 set to [2(2)[101044] DirectoryNode]
>PlayerEntity.42769 RootNodeComponent Node2System.101044 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.StyleToRank set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.StyleToScore set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.MedalSyncRecord set to [[]]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101036.ChildDict.1000021 set to [100060]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100060 set to [MedalTaskNode-100060 BizId 1000021 Index 1000021 Count 0 Type 10 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100060 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100061 set to [SubTaskNode--1000021--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100061 set to [19]
>-10(10)[10] MedalTaskContainer
|-1(1)[101036] DirectoryNode
|--MedalTaskNode-100060 BizId 1000021 Index 1000021 Count 0 Type 10 CreateTime 1744723447
|---SubTaskNode--1000021--0--False
|-2(2)[101044] DirectoryNode
CompletedTaskIds:[]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100061 remove
>PlayerEntity.42769 RootNodeComponent Node2System.100061 remove
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100060 remove
>PlayerEntity.42769 RootNodeComponent Node2System.100060 remove
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101036.ChildDict.1000021 remove
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101044.ChildDict.1000021 set to [100060]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100060 set to [MedalTaskNode-100060 BizId 1000021 Index 1000021 Count 0 Type 10 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100060 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100061 set to [SubTaskNode--1000021--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100061 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.StyleToScore.1 set to [10]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101036.ChildDict.1000022 set to [100062]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100062 set to [MedalTaskNode-100062 BizId 1000022 Index 1000022 Count 0 Type 10 CreateTime 1744723447]
>PlayerEntity.42769 RootNodeComponent Node2System.100062 set to [19]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100063 set to [SubTaskNode--1000022--0--False]
>PlayerEntity.42769 RootNodeComponent Node2System.100063 set to [19]
>-10(10)[10] MedalTaskContainer
|-1(1)[101036] DirectoryNode
|--MedalTaskNode-100062 BizId 1000022 Index 1000022 Count 0 Type 10 CreateTime 1744723447
|---SubTaskNode--1000022--0--False
|-2(2)[101044] DirectoryNode
|--MedalTaskNode-100060 BizId 1000021 Index 1000021 Count 0 Type 10 CreateTime 1744723447
|---SubTaskNode--1000021--0--False
CompletedTaskIds:[]
>[]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100061 remove
>PlayerEntity.42769 RootNodeComponent Node2System.100061 remove
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.100060 remove
>PlayerEntity.42769 RootNodeComponent Node2System.100060 remove
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.101044.ChildDict.1000021 remove
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.CompletedTaskIds.0 set to [0]
>PlayerEntity.42769 PlayerTaskComponent SystemRoot.Nodes.10.MedalSyncRecord.0 set to [MedalRecord(Detached 0)]
>-10(10)[10] MedalTaskContainer
|-1(1)[101036] DirectoryNode
|--MedalTaskNode-100062 BizId 1000022 Index 1000022 Count 0 Type 10 CreateTime 1744723447
|---SubTaskNode--1000022--0--False
|-2(2)[101044] DirectoryNode
CompletedTaskIds:[1000021]
>[{"Hash":700924152,"SyncTimestamp":26400247,"StyleId":1,"RankPointsChange":10,"MedalsObtained":{"1":1},"Rank":0}]
>RemoveEntity PlayerEntity.42769
