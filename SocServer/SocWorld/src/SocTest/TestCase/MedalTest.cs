using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocWorld.Component;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.SocConst.Soc.Const;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld.Lobby;
using Gameserver;
using Google.Protobuf;
using WizardGames.Soc.Common.Data.task;
using WizardGames.Soc.SocWorld.GRpcServer;
using WizardGames.Soc.SocWorld.Framework.Network;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Common.Framework.Network;

namespace WizardGames.Soc.Test
{
    public class MedalTest : TestCaseBase
    {
        PlayerEntity player;
        MedalTaskContainer medalTaskContainer;

        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            player.AddComponent(new PlayerDataStatisticComponent());
            AddEntityOnlyInitComponents(player);
            player.LifeCycleFlags |= LifeFlags.Alive;
            ServerConfig.Instance.ClosePushToLobby = 1;
            GlobalInfoSyncEntity.CreateInstance();
            GlobalInfoSyncEntity.Instance.TeamMemberLimit = 2;
            InitMedalTaskContainer();
            // TestCaloriesNum();
            TestAcceptNonPreCountTask();
            TestSyncDeltaMedalInfo();
            TestReceivePushStyleRankReq();
            // TestHighGradeOreTeaCraftingTaskCount();
            // TestGetFertilizerFromComposter();
        }

        private void TestReceivePushStyleRankReq()
        {
            var req = new PushStyleRankChangeReq();
            req.RoleID = player.RoleId;
            req.StyleRankUpdates.Add(1, 10);
            var packet = WorldNetworkHelper.GetGateToWorldPacket(EntityConstId.RPC_ENTITY_ID, ComponentBase.INVALID_ID,
                FixedRpcMethod.RPC_ENTITY_PUSH_STYLE_RANK_CHANGE, ERpcTarget.World);
            packet.AppendParam(req.ToByteArray());
            WorldNetworkHelper.GateCallWorld(packet);
        }

        private void TestGetFertilizerFromComposter()
        {
            // 设置勋章任务：从堆肥箱中获取肥料
            SetupMedalTaskConfig(8410140, 184, new long[] { 2, 1000001, ItemConst.Manure });
            SetupMedalReconstructData(8410140, 1, 1, 1, 0);
            medalTaskContainer.AcceptTask(8410140, false);
            player.AddComponent(new PlayerLootingComponent());
            Print("初始任务状态:");
            Print(medalTaskContainer.ToTreeString(0));

            var composterEntity = CreateComposterEntity();

            TestNormalFertilizerLooting(composterEntity);
        }

        private PartEntity CreateComposterEntity()
        {
            var composter = new PartEntity(IdGeneratorUtil.GenInWorldFreqId());
            composter.TemplateId = ItemConst.Composter;
            composter.AddComponent(new RootNodeComponent());
            var composterComp = new ComposterComponent();
            composter.AddComponent(composterComp);
            composter.PosX = 100;
            composter.PosZ = 100;
            AddEntityOnlyInitComponents(composter);

            return composter;
        }

        private void TestNormalFertilizerLooting(PartEntity composterEntity)
        {
            Print("=== 测试1 正常从堆肥箱获取肥料 ===");

            // 设置玩家位置在堆肥箱附近
            player.PosX = 100;
            player.PosZ = 100;

            // 添加肥料到堆肥箱输出容器
            var composterComp = composterEntity.GetComponent<ComposterComponent>(EComponentIdEnum.Composter);
            var rootComp = composterEntity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
            {
                using var ctx = NodeOpContext.GetNew("OnComposting").SetServerOperation(true);
                var mergeReq =
                    new NodeOpByIndex(ComposterComponent.outputPathAny, new BizIdWithCount(ItemConst.Manure, 5)).WithOption(
                        EOversizeOptionEnum.Drop);
                var opCodeMerge = rootComp.MergeNode(mergeReq);
                Print($"添加肥料到堆肥箱结果: {opCodeMerge}");
            }

            // 开始掠夺
            player.ComponentLooting.StartLooting(composterEntity.EntityId);
            // 从堆肥箱获取肥料
            player.ComponentLooting.QuickRequire(composterEntity.EntityId, ItemConst.Manure, 2);
            FlipTimewheel(1);
            player.ComponentLooting.StopLooting();

            Print(medalTaskContainer.ToTreeString(0));
        }

        private void SetupMedalReconstructData(long taskId, int medalId, int medalLevel, int styleId, int points,
            bool shouldPreCount = false)
        {
            GlobalInfoSyncEntity.Instance.MedalReconstructData ??= [];
            GlobalInfoSyncEntity.Instance.MedalReconstructData.Add(taskId, new MedalInfo(medalId, styleId, medalLevel, points));
            SetupMedalConfig(medalId, medalLevel, styleId, points, taskId, shouldPreCount);
        }

        private void InitMedalTaskContainer()
        {
            FunctionConst.Task.EnableDynamicSwitch();
            FunctionSwitchComponent.Instance.SetEnable(FunctionConst.Task, false);
            var taskComponent = new PlayerTaskComponent();
            player.AddComponent(taskComponent);
            taskComponent.Init();

            SetupCommonConditionConfigs();

            medalTaskContainer = new MedalTaskContainer();
            taskComponent.AddTaskContainer(medalTaskContainer);
            medalTaskContainer.PostInit(true);
        }

        private static void SetupCommonConditionConfigs()
        {
            LobbyTaskTest.OverrideQuestCondition(151, CompareType.GreaterOrEqual,TargetData.BuildNum, CounterChange.Increment);
            LobbyTaskTest.OverrideQuestCondition(234, CompareType.GreaterOrEqual,TargetData.OpenPlayerDeathDropBox, CounterChange.GetValue);
            LobbyTaskTest.OverrideQuestCondition(184, CompareType.GreaterOrEqual,TargetData.GetItem, CounterChange.Increment);
        }

        private void TestCaloriesNum()
        {
            SetupMedalTaskConfig(100001, 234, [50], 10);
            SetupMedalReconstructData(100001, 1, 1, 1, 0);
            using var ctx = NodeOpContext.GetNew("test");
            var opCode = player.ComponentTask.SystemRoot.Input(new NodeOpByBizId(100001, 1), ctx);
            Print($"opCode: {opCode}");
            // 增量累计
            player.Calories = 10;
            Print(medalTaskContainer.ToTreeString(0));
            player.Calories = 30;
            Print(medalTaskContainer.ToTreeString(0));
            // 减少
            player.Calories = 0;
            Print(medalTaskContainer.ToTreeString(0));
            // 非存活状态
            player.LifeCycleFlags = ByteUtil.RemoveFlag(player.LifeCycleFlags, LifeFlags.Alive);
            player.Calories = 10;
            Print(medalTaskContainer.ToTreeString(0));
            player.LifeCycleFlags = ByteUtil.AddFlag(player.LifeCycleFlags, LifeFlags.Alive);
            player.Calories = 30;
            FlipTimewheel(1);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private void TestAcceptNonPreCountTask()
        {
            player.ComponentDataStatistic.SystemRoot.ClearChildren();
            SetupMedalTaskConfig(1000021, 234, [50], 10);
            SetupMedalTaskConfig(1000022, 234, [100], 10);
            SetupMedalReconstructData(1000021, 1, 1, 1, 10);
            SetupMedalReconstructData(1000022, 1, 2, 1, 20);
            medalTaskContainer.AcceptTask(1000021, false);
            Print(medalTaskContainer.ToTreeString(0));
            var taskNode = medalTaskContainer.GetTaskNode(TaskNodeIndex.InProgress, 1000021) as MedalTaskNode;
            medalTaskContainer.inProcessTask.RemoveChildTask(taskNode);
            medalTaskContainer.CompleteTask(taskNode);
            Print(medalTaskContainer.ToTreeString(0));
        }

        /// <summary>
        /// 测试制作上等矿茶任务计数功能
        /// </summary>
        private void TestHighGradeOreTeaCraftingTaskCount()
        {
            // 清理之前的数据统计
            player.ComponentDataStatistic.SystemRoot.ClearChildren();

            long craftingTeaTaskId = 8203140;
            long recipeId = 607;

            LobbyTaskTest.OverrideQuestCondition(184, CompareType.GreaterOrEqual,TargetData.GetItem, CounterChange.Increment);
            SetupMedalTaskConfig(craftingTeaTaskId, 184, [2, 1000024, 990620], 10, 0, 0);

            // 接取制作上等矿茶任务
            var opCode = player.ComponentTask.SystemRoot.Input(new NodeOpByBizId(craftingTeaTaskId, 1),
                NodeOpContext.GetNew("test_crafting_tea"));
            Print($"接取任务操作码: {opCode}");
            Print(medalTaskContainer.ToTreeString(0));

            // 构造 PartEntity TemplateId = Mixing
            var mixingPart = new PartEntity(IdGeneratorUtil.GenInWorldFreqId());
            mixingPart.TemplateId = (long)PartType.MixingTable;
            mixingPart.AddComponent(new RootNodeComponent());
            var oven = new OvenComponent();
            mixingPart.AddComponent(oven);
            AddEntityOnlyInitComponents(mixingPart);

            var composeNode = new OvenComposeNode(recipeId) { Interval = 1, Count = 1, };
            oven.rootNodeComponent.MergeNode(new NodeOpByIndex(new List<long>() { NodeSystemType.Oven },
                new ExistingNode(composeNode)));
            FlipTimewheel(1);
            // 模拟制作上等矿茶操作
            using var ctx = NodeOpContext.GetNew(OpContextConst.MIXING);
            OvenComponentHotfix.ComposeQueueOnClaim(oven, player, oven.queueNode, 0);

            FlipTimewheel(1);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private void TestSyncDeltaMedalInfo()
        {
            Print(medalTaskContainer.MedalSyncRecord.ToEJson().ToString());
            ServerInstanceEntity.Instance.OnSendSettleStyleRankPointsToLobbyCallback(null, System.Net.HttpStatusCode.OK, 0);
            Print(medalTaskContainer.ToTreeString(0));
            Print(medalTaskContainer.MedalSyncRecord.ToEJson().ToString());
        }

        private static void SetupMedalConfig(int medalId, int level, int styleId, int points, long nextLevelTaskId,
            bool shouldPreCount = false)
        {
            var medalJson = @"{
      ""medalID"": " + medalId + @",
      ""level"": " + level + @",
      ""type"": 1,
      ""styleID"": " + styleId + @",
      ""task"": [
        [
          2,
          " + nextLevelTaskId + @"
        ]
      ],
      ""styleRankPoints"": [
        [
          1,
          " + points + @"
        ]
      ],
      ""rankID"": 1,
      ""shouldPreCount"": " + shouldPreCount + @"
    }";
            McCommon.Tables.TBMedal.Update(SimpleJSON.JSONNode.Parse(medalJson));
        }

        private static void SetupMedalTaskConfig(long taskId, int endCondition, long[] endConditionParameter = null,
            int taskType = 10, int endConditionMode = 0, int isSubTask = 0, int[] subTasks = null)
        {
            
        }
    }
}