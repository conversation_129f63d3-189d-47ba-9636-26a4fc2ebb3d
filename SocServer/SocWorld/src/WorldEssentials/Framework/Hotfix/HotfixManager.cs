using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace WizardGames.Soc.SocWorld.Framework.Hotfix
{
    /// <summary>
    /// 热更新模块管理器
    /// <para>通过主函数Load来加载Dll模块</para>
    /// </summary>
    public static class HotfixManager
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(HotfixManager));
        /// <summary>
        /// 加载次数
        /// </summary>
        private static int loadCount = 0;
        /// <summary>
        private static bool isLoading = false;
        /// <summary>
        /// 是否为第一次加载
        /// <para>只有在第一次加载才为true，之后都是false</para>
        /// <para>第二次调用Load后变化</para>
        /// </summary>
        public static bool IsFirstLoad => loadCount == 1;
        /// <summary>
        /// 程序集装载器对象
        /// </summary>
        private static AssemblyLoader assemblyLoader;

        public static Dictionary<int, IHotfixDelegate> HotfixableTimerDict;
        public static Dictionary<int, IHotfixDelegate> HotfixableScheduleDict;

        /// <summary>
        /// 动态加载程序集并执行指定类的入口方法。
        /// </summary>
        /// <param name="assemblyName">要加载的程序集文件名。</param>
        /// <param name="className">要执行的类的全名。</param>
        /// <param name="loadMethodName">加载方法名称。</param>
        /// <param name="unloadMethodName">卸载方法名称。</param>
        public static void Load(string assemblyName, string className, string firstLoadMethodName = "FirstLoad", string loadMethodName = "AfterLoad", string unloadMethodName = "BeforeUnLoad")
        {
            if (!assemblyName.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
            {
                logger.Error($"[{assemblyName}] does not end with '.dll'");
                isLoading = false;  // Set the loading state back
                return;
            }

            if (isLoading)
            {
                logger.Error($"[{assemblyName}] file is loading ......");
                return;
            }

            isLoading = true;
            loadCount++;


            var pdbName = assemblyName.Replace(".dll", ".pdb");
            var oldAssemblyLoader = assemblyLoader;
            try
            {
                if (!File.Exists(assemblyName))
                {
                    logger.Error($"[{assemblyName}] file is not found!");
                    return;
                }

                using (var fsRead = new FileStream(assemblyName, FileMode.Open, FileAccess.Read))
                using (FileStream pdbRead = File.Exists(pdbName) ? new FileStream(pdbName, FileMode.Open, FileAccess.Read) : null)
                {
                    if (fsRead.Length <= 0)
                    {
                        logger.Error($"Assembly file is empty!");
                        return;
                    }

                    assemblyLoader = new AssemblyLoader(fsRead, pdbRead);
                    if (!assemblyLoader.IsAlive)
                    {
                        logger.Error($"The assembly is not alive!");
                        return;
                    }

                    if (oldAssemblyLoader != null && oldAssemblyLoader.IsAlive)
                    {
                        var unloadInfo = GetMethodInfo(oldAssemblyLoader.GetAssembly(), className, unloadMethodName);
                        unloadInfo.Invoke(oldAssemblyLoader, null);
                        oldAssemblyLoader.Unload();
                        oldAssemblyLoader = null;
                    }

                    if (IsFirstLoad)
                    {
                        var loadInfo = GetMethodInfo(assemblyLoader.GetAssembly(), className, firstLoadMethodName);
                        loadInfo.Invoke(assemblyLoader, null);
                    }
                    else
                    {
                        var loadInfo = GetMethodInfo(assemblyLoader.GetAssembly(), className, loadMethodName);
                        loadInfo.Invoke(assemblyLoader, null);
                    }
                }
                PrintGitVersion(assemblyName);
            }
            catch (Exception ex)
            {
                logger.Error($"Error loading and executing assembly: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                logger.Info($"===========load assembly:{assemblyName}.{className} successfully=============");
            }
        }

        public static void PrintGitVersion(string assemblyName)
        {
            if (!File.Exists(assemblyName))
            {
                logger.Error($"hotfix plugin {assemblyName} not exist");
                return;
            }

            string currentDirectory = Path.GetDirectoryName(assemblyName);
            string gitVersionFilePath = Path.Combine(currentDirectory, "git_version.txt");

            if (File.Exists(gitVersionFilePath))
            {
                var gitVersion = File.ReadAllText(gitVersionFilePath);
                logger.Info($"hotfix plugin GitVersion: {gitVersion}");
            }
            else
            {
                logger.Info($"hotfix plugin {gitVersionFilePath} not exist!");
            }
        }
        public static MethodInfo GetMethodInfo(Assembly assembly,string className, string MethodName)
        {
            var typeInfo = assembly.GetType(className);
            if (typeInfo == null)
            {
                throw new Exception($"[{className}] class is not found!");
            }

            MethodInfo methodInfo = typeInfo.GetMethod(MethodName, BindingFlags.Public | BindingFlags.Static);

            if (methodInfo == null)
            {
                throw new Exception($"[{MethodName}] public static method is not found!");
            }
            return methodInfo;
        }
    }
}
