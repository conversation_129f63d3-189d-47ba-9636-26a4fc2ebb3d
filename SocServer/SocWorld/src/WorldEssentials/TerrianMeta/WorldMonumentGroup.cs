using System.Collections.Generic;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.SocWorld.Spawn;

namespace WizardGames.Soc.SocWorld.TerrianMeta
{
    public class SpawnAreaBox
    {
        public int AreaId { get; init; }
        public Vector3 Center { get; init; }
        public float RangeX { get; init; }
        public float RangeZ { get; init; }
    }

    public class WorldMonumentGroup
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(WorldMonumentGroup));

        public int MonumentTableId;

        public WorldTerrainMonument Monument { get; init; }

        public TierData Tier { get; private set; }

        public GroupElement[] Elements { get; private set; }
        public GroupPoint[] Points { get; private set; }
        public int EntityType { get; private set; }

        public SpawnGroupElement Table;

        public long TableId => Table.Id;
        public List<SpawnAreaBox> AreaBoxList { get; private set; }

        public static WorldMonumentGroup Create(WorldTerrainMonument monument, SpawnGroupElement config)
        {
            if (config == null)
            {
                logger.WarnFormat($"groupId:{config.Id}, fail to get Create WorldMonumentGroup by group config");
                return null;
            }

            Vector3 pos = new Vector3(config.PosX, config.PosY, config.PosZ);
            var topology = WorldTerrainMeta.Topology.GetTopology(pos);
            int filterAll = TerrainTopology.TIER0 | TerrainTopology.TIER1 | TerrainTopology.TIER2;

            TierData findTierData = null;
            foreach (var tierData in config.TierGroups)
            {
                int filter = SpawnHelper.TierToMask((MonumentTier)tierData.Tier);
                if (filter != filterAll && (filter & topology) == 0)
                    continue;

                findTierData = tierData;
                break;
            }
            //难度线不匹配
            if (findTierData == null)
            {
                logger.Info($"Create {monument.Name} TierGroups group:{config.Id}, fail to find TierData");
                return null;
            }
            return new WorldMonumentGroup(config, monument, findTierData);

        }

        //关卡编辑器中的一个spawnGroup就是一个SpawnGroupElement
        private WorldMonumentGroup(SpawnGroupElement config, WorldTerrainMonument monument,TierData tier)
        {
            Tier = tier;
            Table = config;
            Monument = monument;
            InitRandElemnet();
            InitRandPoint();
            InitAreaList();
        }

        private void InitRandElemnet()
        {
            Elements = new GroupElement[Tier.Elements.Count];
            for (int i = 0; i < Tier.Elements.Count; i++)
            {
                Elements[i] = new GroupElement() { TemplateId = Tier.Elements[i].TemplateId, Weight = Tier.Elements[i].Weight };
            }
            EntityType = EntityTypeId.EntityName2Type[Tier.EntityType];
        }

        private void InitRandPoint()
        {
            Points = new GroupPoint[Table.Points.Count];
            for (int i = 0; i < Table.Points.Count; i++)
            {
                var pointData = Table.Points[i];
                Points[i] = new GroupPoint();
                Points[i].Id = i + 1;
                Points[i].EditorId = pointData.Id;

                Vector3 pos = new Vector3(pointData.PosX, pointData.PosY, pointData.PosZ);
                Points[i].Position = Monument.Transform.TransformPoint(pos);
                pos.x = pointData.RotateX;
                pos.y = pointData.RotateY;
                pos.z = pointData.RotateZ;
                Quaternion rot = Quaternion.Euler(pos);

                Points[i].Rotation = Monument.Transform.Rotation * rot;
                Points[i].EulerAngles = Points[i].Rotation.eulerAngles;
                Points[i].Type = (PointLogicType)pointData.Type;
                Points[i].Weight = pointData.Weight;
                Points[i].Raduis = pointData.Radius;
                Points[i].RandRot = pointData.RandRot;
                Points[i].DropGround = pointData.DropGround;
                Points[i].LadderId = pointData.LadderId;
            }
        }

        private void InitAreaList()
        {
            if (!McCommon.Tables.TbMonumentInformation.DataMap.TryGetValue(Monument.Name, out var ruin))
            {
                return;
            }

            MonumentTableId = ruin.Id;

            var config = McCommon.Tables.TbAreaBoxData.GetOrDefault(ruin.Identification);
            if (config == null)
            {
                return;
            }

            Dictionary<int, SpawnAreaBox> areaBoxDict = new();
            for (int i =0;i < config.AreaBoxes.Count; i++)
            {
                var areaBox = config.AreaBoxes[i];
                var center = Monument.Transform.TransformPoint(new Vector3() { x = areaBox.PosX, y = areaBox.PosY, z = areaBox.PosZ });
                areaBoxDict.Add(areaBox.Id, new SpawnAreaBox()
                {
                    Center = center,
                    AreaId = areaBox.Id,
                    RangeX = areaBox.SizeX,
                    RangeZ = areaBox.SizeZ,
                });
            }
            AreaBoxList = new();
            foreach (var areaBoxId in Table.AreaBoxesIds)
            {
                if (areaBoxDict.TryGetValue(areaBoxId,out var area))
                {
                    AreaBoxList.Add(area);
                }
            }
        }

        public int RandomElement()
        {
            int totalWeight = 0;
            foreach (var element in Elements)
            {
                totalWeight += element.Weight;
            }

            int randWeight = SpawnHelper.Range(0, totalWeight);
            foreach (var element in Elements)
            {
                if (randWeight < totalWeight)
                    return element.TemplateId;
            }
            return 0;
        }
    }
}
