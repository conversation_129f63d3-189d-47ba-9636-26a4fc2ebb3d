using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Collections.Generic;
using System.Net.Http;

namespace WizardGames.Soc.SocWorld.Lobby
{
    /// <summary>
    /// 注册战斗服
    /// </summary>
    public class RegisterBattleMessage
    {
        public string battleServerId;
        public string ip;
        public int category;
        public string battleServerData;
        public int tcpPort;
        public int udpPort;
        public int rpcPort;
        public string innerIp;
        public int gameMode;
        public int onlinePlayers;
        public int totalPlayers;
        public int maxOnlinePlayers;
        public int mapID;
        public string[] whitelistTags;
        // 上报监听内网服务端端口
        public int port;
        public bool isPublic;
        public string region;

        [JsonProperty("versionInfo")]
        public string VersionInfo;
        public bool ShouldSerializewhitelistTags()
        {
            return whitelistTags != null && whitelistTags.Length > 0;
        }
    }

    /// <summary>
    /// 注销战斗服
    /// </summary>
    public class UnRegisterBattleMessage
    {
        public string battleServerId;
        public bool canDelete;
    }

    /// <summary>
    /// 战斗服心跳
    /// 
    /// </summary>
    public class BattleHeartBeatMessage
    {
        public string battleServerId;
        public string battleServerData;
        public int onlinePlayers;
        public int totalPlayers;
    }

    /// <summary>
    /// 战斗服获取角色数据
    /// </summary>
    public class BattelGetRoleData
    {
        public string token;
        public string roleId;
    }

    /// <summary>
    /// 验证token
    /// </summary>
    public class VerifyTokenData
    {
        public string token;
        public string roleID;
        public string battleServerID;
    }

    public class MinganciSceneId
    {
        // 取名和昵称
        public const int RENAME = 1001;
        // 私聊
        public const int CHANGE_NAME = 101;
        // 搜索
        public const int SEARCH_PLAYER = 4001;
        // 世界聊天
        public const int WORLD_CHAT = 1002;
        // 小队聊天
        public const int TEAM_CHAT = 1003;
        // 创建招募名称 or 修改招募名称
        public const int TEAM_RECRUIT = 2004;
        // 创建招募宣言 or 修改招募宣言
        public const int TEAM_RECRUIT_STATEMENT = 2005;
        // 局内招募申请
        public const int TEAM_RECRUIT_APPLY = 2016;
        // 建筑取名 除了床和睡袋
        public const int PART_RENAME = 108;
        // 领地取名
        public const int TERRITORY_RENAME = 104;
        // 睡袋取名
        public const int SLEEPING_BAG_RENAME = 105;
        // 床取名
        public const int BED_RENAME = 107;
        // 自动售货机取名
        public const int VENDING_MACHINE_RENAME = 106;
        
        /// <summary>
        /// 玩家保存蓝图
        /// </summary>
        public const int CONSTRUCTION_BLUEPRINT_NAME_SAVE = 109;
        /// <summary>
        /// 玩家替换蓝图
        /// </summary>
        public const int CONSTRUCTION_BLUEPRINT_NAME_REPLACE = 110;
    }

    /// <summary>
    /// 排行榜单项数据
    /// snowflakeID 唯一ID
    /// score 计算分数, 如果有多个计算因子组成的分数，自己计算分数，中台不理解业务
    /// data 排行榜需要显示的内容，是json字符串
    /// </summary>
    public class PlayScoreRankItemData
    {
        public string uniqueID;
        public long score;
        public string data;
    }

    /// <summary>
    /// 大厅上报数据, 因为序列化json 保证跟proto的字段命名一致，所以会跟服务器命名规范冲突
    /// </summary>
    public class PlayScoreRankData
    {
        public string boardJsonData;
        public string BoardName => "board-total";
    }

    /// <summary>
    /// 发送邮件数据
    /// </summary>
    public class SendMailData
    {
        /// <summary>
        /// 收到邮件的玩家列表
        /// </summary>
        public List<string> roleId;
        /// <summary>
        /// 待发邮件内容
        /// </summary>
        public NewMailData request;
    }
    /// <summary>
    /// 发送邮件数据
    /// </summary>
    public class Resources
    {
        /// <summary>
        /// 日志名
        /// </summary>
        public int amount;
        /// <summary>
        /// 日志信息
        /// </summary>
        public long rid;
    }
    /// <summary>
    /// 发送lobby奖励
    /// </summary>
    public class SendLobbyRewardData
    {
        public string reason;
        public List<Resources> resources;
        public string roleId;
    }
    /// <summary>
    /// 扣除资源
    /// </summary>
    public class DelLobbyResource
    {
        public string reason;
        public List<Resources> resources;
        public string roleId;
    }
    /// <summary>
    /// 发送玩家日志
    /// </summary>
    struct PlayerLog
    {
        /// <summary>
        /// 日志名
        /// </summary>
        public string logName;
        /// <summary>
        /// 日志信息
        /// </summary>
        public string msg;
        /// <summary>
        /// 玩家id
        /// </summary>
        public string roleID;
    }

    /// <summary>
    /// 待发邮件数据
    /// </summary>
    public class NewMailData
    {
        /// <summary>
        /// 公告邮件类型
        /// </summary>
        public const string NOTIC_EMAIL_TYPE = "NOTICE";
        /// <summary>
        /// 系统邮件类型
        /// </summary>
        public const string SYSTEM_MAIL_TYPE = "SYSTEM";

        /// <summary>
        /// 邮件文案数据
        /// </summary>
        public MailDetailData Detail;
        /// <summary>
        /// 邮件模板Id，如果不为0，忽��上文Detail的数据
        /// </summary>
        public int TemplateID;
        /// <summary>
        /// 邮件附件数据
        /// </summary>
        public List<MailAttachData> attach;
        /// <summary>
        /// 邮件类型
        /// </summary>
        public string mailType;
        /// <summary>
        /// 可选参数
        /// </summary>
        public List<string> @params;
        /// <summary>
        /// 邮件有效期，单位为天
        /// </summary>
        public int validDay;
    }

    /// <summary>
    /// 邮件文案数据
    /// </summary>
    public class MailDetailData
    {
        /// <summary>
        /// 邮件内容
        /// </summary>
        public string content;
        /// <summary>
        /// 邮件标题
        /// </summary>
        public string title;
    }

    /// <summary>
    /// 邮件附件数据
    /// </summary>
    public class MailAttachData
    {
        /// <summary>
        /// 物品Id
        /// </summary>
        public string itemId;
        /// <summary>
        /// 物品数量
        /// </summary>
        public int itemNum;
    }

    public class BattlePlayerEnterOrLeaveInfo
    {
        public string battleServerID;
        public ulong roleID;
        public long battledSeconds;
    }

    public class UpdatePlayerBattleInfoReq
    {
        public UpdatePlayerBattleInfoReq()
        {
            battleTeamMemberData = new List<BattleTeamMember>();
        }
        public string battleServerID;
        public string teamID;
        public List<BattleTeamMember> battleTeamMemberData;
    }

    public class BattleTeamMember
    {
        public ulong roleID;
        public long joinTime;
        public string battlePlayerData;
    }

    public class InviteFromBattleReq
    {
        public int gameMode;
        public string gameServerID;
        public string gameServerName;
        public string receiverID;
        public string senderID;
        public string teamID;
    }

    public class PersonalMessage
    {
        public string sender;
        public string receiver;
        public string msg;
    }

    public class PlayerEnterDynamicBattleMessage
    {
        public string battleID;
        public ulong roleID;
    }

    public class PlayerLeaveDynamicBattleMessage
    {
        public string battleID;
        public ulong[] roleIDList;
    }

    public delegate void PostAsyncCallback(HttpResponseMessage response);

    public enum ETargetThreadType
    {
        None,
        Main,
        Logic,
    }

    public class RecentFriendData
    {
        public List<int> reason;
        public string roleID;
        public string lastModifyTime;
    }
    public class SendSaveRecentFriendsData
    {
        public List<RecentFriendData> players;
        public List<string> removeList;
        public string roleID;
    }

    public class FriendIntimacy
    {
        public string roleID;
        public float intimacy;
    }

    public class SendSaveIntimacysData
    {
        public List<FriendIntimacy> intimacyList;
        public string roleID;
        public bool IsBothInc;
    }

    public class UnlockReputationRewardData
    {
        public string roleID;
        public List<int> rewardIDs;
    }

    public class ReportBattleEndTimeData
    {
        public string battleEndTime;
        public string battleServerID;
    }

    public class AppMessageItemData
    {
        public string content;
        public string title;
        public string customContent;
    }

    public class AppPushMessageData
    {
        public List<string> roleIDs;
        public AppMessageItemData message;
    }

    public class TeamRecruitInfo
    {
        public int gameMode;
        public List<int> jobFilter;
        public string leaderRoleID;
        public List<string> memberRoleIDList;
        public int recruitVersion;
        public string openServerTime;
        public string serverID;
        public string serverName;
        public string teamID;
        public string teamName;
        public string teamRemark;
        public int requireMIC;
        public int teamSize;
        public int platform;
    }

    public class TeamRecruitResponse
    {
        public string applicantID;
        public int handle;
        public string operatorID;
        public string serverID;
        public string serverName;
        public string teamID;
        public int requireMIC;
    }

    public class TaskServiceGroupInfo
    {
        [JsonProperty("groupID")]
        public int GroupId;
        [JsonProperty("taskIDs")]
        public List<int> TaskIds;

        [JsonProperty("version")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long Version;
    }

    public class TaskServiceInnerTasksFinishedReq
    {
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long RoleId;
        [JsonProperty("DSFinishedTaskGroups")]
        public List<TaskServiceGroupInfo> DsFinishedTaskGroups;
    }

    public class TaskServiceInnerTaskFinishedRsp
    {
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long RoleId;

        [JsonProperty("DSNewTaskGroups")]
        public List<TaskServiceGroupInfo> DsNewTaskGroups;

        [JsonProperty("DSNeedResendTaskGroups")]
        public List<TaskServiceGroupInfo> DsNeedResendTaskGroups;
    }

    public class GetLobbyMainTaskRewardRsp
    {
        [JsonProperty("groupID")]
        public int GroupId;
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long RoleId;
        [JsonProperty("resourceLimit")]
        [JsonConverter(typeof(WildcardConverter))]
        public Dictionary<string, long> ResourceLimit;
    }

    public class TaskServiceInnerTaskGroup
    {
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long RoleId;

        [JsonProperty("DSTaskGroups")]
        public TaskServiceRoleTaskGroup DsTaskGroups;
    }

    public class TaskServiceRoleTaskGroup
    {
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long RoleId;

        [JsonProperty("taskGroups")]
        public List<CommonTaskGroup> TaskGroups;
    }

    public class CommonTaskGroup
    {
        [JsonProperty("enable")]
        public bool Enable;

        [JsonProperty("state")]
        public int State;

        [JsonProperty("groupID")]
        public int GroupId;

        [JsonProperty("resourceLimit")]
        [JsonConverter(typeof(WildcardConverter))]
        public Dictionary<string, long> ResourceLimit;

        [JsonProperty("tasks")]
        public List<CommonTask> Tasks;

        //周一早上5点刷新，版本号是周一0点时间戳
        [JsonProperty("version")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long Version;
    }

    public class CommonTask
    {
        [JsonProperty("taskID")]
        public int TaskId;

        [JsonProperty("state")]
        public int State;

        [JsonProperty("counters")]
        public List<CommonTaskCounter> Counters;

        [JsonProperty("rewardCount")]
        public long RewardCount;
    }

    public class CommonTaskCounter
    {
        [JsonProperty("counterID")]
        public int CounterId;

        [JsonProperty("countValue")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long CountValue;

        [JsonProperty("targetValue")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long TargetValue;
    }

    public class AcquiredResource
    {
        [JsonProperty("rid")]
        public int ResourceId;
        [JsonProperty("acquiredAmount")]
        public int AcquiredAmount;
        [JsonProperty("exchangedAmount")]
        public int ExchangedAmount;
        [JsonProperty("expireAt")]
        [JsonConverter(typeof(StringToLongConverter))]
        public long ExpireAt;
    }

    public class AddResourceRsp
    {
        [JsonProperty("decomposes")]
        public List<bool> Decompose;
        [JsonProperty("acquiredResources")]
        public List<AcquiredResource> AcquiredResources;
    }
    /// <summary>
    /// 战区服上报战局结算数据
    /// </summary>
    public class WarzoneServiceReportBattleEndSummaryReq
    {
        [JsonProperty("battleServerID")]
        public string BattleServerId;
        [JsonProperty("battleSummaryData")]
        public string BattleSummaryData;
        [JsonProperty("gameMode")]
        public int GameMode;
        [JsonProperty("reputationLevel")]
        public List<int> ReputationLevel = new ();
        [JsonProperty("roleIDs")]
        [JsonConverter(typeof(StringToULongListConverter))]
        public List<ulong> RoleIds = new ();
        [JsonProperty("newBieTaskFlag")]
        public List<bool> NewbieTaskFlag = new ();
    }

    public class PlayerGrowthServiceMedalLevelInfo
    {
        [JsonProperty("level")]
        public int Level { get; set; }

        [JsonProperty("medalID")]
        public int MedalID { get; set; }
    }

    public class PlayerGrowthServiceSettleStyleRankPointsInfo
    {
        [JsonProperty("addStyleRankPointsMap")]
        public Dictionary<int, int> AddStyleRankPointsMap { get; set; }

        [JsonProperty("medalLevelInfoList")]
        public List<PlayerGrowthServiceMedalLevelInfo> MedalLevelInfoList { get; set; }

        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToULongConverter))]
        public ulong RoleID { get; set; }
    }

    public class PlayerGrowthServiceFullSettleStyleRankPointsInfo
    {
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToULongConverter))]
        public ulong RoleID { get; set; }

        [JsonProperty("styleRankPoints")]
        public Dictionary<int, int> StyleRankPoints { get; set; }
    }

    public class PlayerGrowthServiceInnerSettleStyleRankPointsReq
    {
        [JsonProperty("fullSettleStyleRankPointsInfoList")]
        public List<PlayerGrowthServiceFullSettleStyleRankPointsInfo> FullSettleStyleRankPointsInfoList { get; set; }

        [JsonProperty("gameMode")]
        public int GameMode { get; set; }

        [JsonProperty("isLastSettle")]
        public bool IsLastSettle { get; set; }

        [JsonProperty("settleStyleRankPointsInfoList")]
        public List<PlayerGrowthServiceSettleStyleRankPointsInfo> SettleStyleRankPointsInfoList { get; set; }

        [JsonProperty("battleServerID")]
        public string BattleServerId { get; set; }
    }

    /// <summary>
    /// 拉取玩家风格段位响应 JSON 结构
    /// 对应 playerservice.proto 中的 InnerPullStyleRankRsp
    /// </summary>
    public class InnerPullStyleRankRsp
    {
        /// <summary>
        /// 5维风格段位
        /// key: 风格ID (uint32)
        /// value: 段位积分 (int32)
        /// </summary>
        [JsonProperty("styleRank")]
        public Dictionary<uint, int> StyleRank { get; set; } = new();
    }

    /// <summary>
    /// 皮肤信息
    /// </summary>
    public class SkinInfo
    {
        /// <summary>
        /// 皮肤ID
        /// </summary>
        [JsonProperty("itemID")]
        public long ItemID { get; set; }

        /// <summary>
        /// 收藏时间(秒级时间戳)
        /// </summary>
        [JsonProperty("starredTime")]
        public long StarredTime { get; set; }

        /// <summary>
        /// 获得时间(秒级时间戳)
        /// </summary>
        [JsonProperty("gotTime")]
        public long GotTime { get; set; }

        /// <summary>
        /// 装备"装备道具"的ID 时装都为0
        /// </summary>
        [JsonProperty("hostItemID")]
        public long HostItemID { get; set; }

        /// <summary>
        /// 过期时间(秒级时间戳)
        /// </summary>
        [JsonProperty("expireAt")]
        public long ExpireAt { get; set; }
    }

    /// <summary>
    /// 时装皮肤数据
    /// </summary>
    public class CostumeSkin
    {
        /// <summary>
        /// 使用中的时装ID
        /// </summary>
        [JsonProperty("costumeEquipedSkinIDList")]
        public List<long> CostumeEquippedSkinIdList { get; set; }

        /// <summary>
        /// 全量时装皮肤
        /// </summary>
        [JsonProperty("skinInfo")]
        public List<SkinInfo> SkinInfo { get; set; }
    }

    /// <summary>
    /// 获取勋章结算的服务器Id判断当前战局是否开启结算
    /// </summary>
    public class GetSettleStyleRankPointsSwitchResp
    {
        [JsonProperty("openBattleServerID")]
        public string BattleServerId { get; set; }
        [JsonProperty("roleID")]
        [JsonConverter(typeof(StringToULongConverter))]
        public ulong RoleId { get; set; }
    }
}
