using System.Collections.Generic;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Algorithm;

namespace WizardGames.Soc.SocWorld.Construction
{
    public partial class SampleXYPlanePart
    {
        public TerritoryOutsidePartWithCounter Part;

        public float Z;
        public Vector2 Point1;
        public Vector2 Point2;

        [HotfixableVirtual]
        internal virtual partial bool IsInBound(Vector2 vector2);

        [HotfixableVirtual]
        internal virtual partial bool IsTransparent(Vector2 vector2);
    }

    public partial class SampleXZPlanePart : SampleXYPlanePart
    {
        public Vector2[] Points;

        [HotfixableVirtual]
        internal override partial bool IsInBound(Vector2 vector2);

        [HotfixableVirtual]
        internal override partial bool IsTransparent(Vector2 vector2);
    }

    public partial class SamplePlanePartComparer : IComparer<SampleXYPlanePart>
    {
        public static readonly SamplePlanePartComparer Instance = new();

        [HotfixableVirtual]
        public partial int Compare(SampleXYPlanePart xPart, SampleXYPlanePart yPart);
    }

    public partial class SamplePlanePartRComparer : IComparer<SampleXYPlanePart>
    {
        public static readonly SamplePlanePartRComparer Instance = new();

        [HotfixableVirtual]
        public partial int Compare(SampleXYPlanePart xPart, SampleXYPlanePart yPart);
    }

    public partial class SamplePlanePointDoubleDir : SamplePlanePoint
    {
        //如果是瓶颈的话待优化，由于时间有限且还有其他更优的方案因此初版先不处理
        // 性能优化空间巨大，C#的有序容器不支持反向迭代器，没法持有元素节点，没有lower_bound，upper_bound
        // 没时间自己写一个了，自己写的话四个容器可以用一个容器加两个树节点或双向链表存储来处理
        public readonly SortedSet<SampleXYPlanePart> ZOrderSetR = new(SamplePlanePartRComparer.Instance);
        public readonly List<SampleXYPlanePart> ZMinOutside = new();

        public SamplePlanePointDoubleDir(int id, SampleXYPlane belongPlane, float x, float y)
            : base(id, belongPlane, x, y) { }

        [HotfixableVirtual]
        internal override partial void AddPart(SampleXYPlanePart part);

        [HotfixableVirtual]
        internal override partial void RemovePart(SampleXYPlanePart part);
    }

    public partial class SamplePlanePoint
    {
        protected static readonly SocLogger logger = LogHelper.GetLogger(typeof(SamplePlanePointDoubleDir));
        public int Id;
        public Vector2 Point;
        public SampleXYPlane BelongPlane;
        public readonly SortedSet<SampleXYPlanePart> ZOrderSet = new(SamplePlanePartComparer.Instance);
        public readonly List<SampleXYPlanePart> ZMaxOutside = new();

        public SocLogger Logger => logger;
        public SamplePlanePoint(int id, SampleXYPlane belongPlane, float x, float y)
        {
            Id = id;
            BelongPlane = belongPlane;
            Point = new(x, y);
        }

        [HotfixableVirtual]
        internal virtual partial void AddPart(SampleXYPlanePart part);

        [HotfixableVirtual]
        internal virtual partial void RemovePart(SampleXYPlanePart part);
    }

    public partial class SampleXYPlane
    {
        protected static readonly SocLogger logger = LogHelper.GetLogger(typeof(SampleXYPlane));
        public readonly Dictionary<long, SampleXYPlanePart> PartDetailDic = new();
        public readonly Dictionary<int, SamplePlanePoint> SamplePoints = new();
        public static float X = 1f;
        public static float Y = 1.5f;

        public SocLogger Logger => logger;


        internal readonly Matrix4x4 coordinatesMat;

        public SampleXYPlane(Matrix4x4 matrix4X4)
        {
            coordinatesMat = matrix4X4;
        }
        ~SampleXYPlane() { ReturnToPool(); }

        public void ReturnToPool()
        {
            foreach (var (_, part) in PartDetailDic)
            {
                ReturnSamplePart(part);
            }
            PartDetailDic.Clear();
        }

        [HotfixableVirtual]
        internal virtual partial float GetDeltaDistX();

        [HotfixableVirtual]
        internal virtual partial float GetDeltaDistY();

        [HotfixableVirtual]
        internal virtual partial SampleXYPlanePart GetSamplePart();
        [HotfixableVirtual]
        internal virtual partial void ReturnSamplePart(SampleXYPlanePart part);
        [HotfixableVirtual]
        internal virtual partial SamplePlanePoint CreateSamplePoint(int id, float x, float y);

        [HotfixableVirtual]
        internal virtual partial void GetBoundPoint(PartEntity targetEntity, SampleXYPlanePart singlePart);
    }

    public partial class SampleXZPlane : SampleXYPlane
    {
        public SampleXZPlane(Matrix4x4 matrix4X4) : base(matrix4X4) { }

        [HotfixableVirtual]
        internal override partial float GetDeltaDistX();
        [HotfixableVirtual]
        internal override partial float GetDeltaDistY();
        [HotfixableVirtual]
        internal override partial SampleXYPlanePart GetSamplePart();
        [HotfixableVirtual]
        internal override partial void ReturnSamplePart(SampleXYPlanePart part);
        [HotfixableVirtual]
        internal override partial SamplePlanePoint CreateSamplePoint(int id, float x, float y);
        [HotfixableVirtual]
        internal override partial void GetBoundPoint(PartEntity targetEntity, SampleXYPlanePart singlePart);
    }
}
