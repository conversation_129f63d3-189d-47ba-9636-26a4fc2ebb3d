using System.Collections.Generic;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.Data.constraction;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Component
{
    public partial class StabilityComponent
    {
        public int CachedDistanceFromGround;

        public float cachedStability = 0.0f;
        public int stabilityStrikes = 0;
        public bool isDirty = false;
        public BuildingCore BuildingConfig => McCommon.Tables.TbBuildingCore.GetOrDefault(ParentPartEntity.TemplateId);
        public long PartType => ParentPartEntity.TemplateId;
        public List<PartSupport> Supports;
        /// <summary>
        /// 改造使用，继承cache来减少支撑力重复计算
        /// </summary>
        /// <param name="value"></param>
        public void SetCachedStability(float value) => cachedStability = value;


        
        public bool Grounded
        {
            get => SocketComponent?.Grounded ?? false;
        }

        public PartEntity ParentPartEntity => ParentEntity as PartEntity;

        public ConstructionSocketBaseComponent SocketComponent
        {
            get => (ParentEntity as PartEntity)?.GetComponent<ConstructionSocketBaseComponent>(EComponentIdEnum
                .ConstructionSocket) as ConstructionSocketBaseComponent;
        }


        [HotfixableVirtual]
        public override partial void InitFromDb();


    }


}