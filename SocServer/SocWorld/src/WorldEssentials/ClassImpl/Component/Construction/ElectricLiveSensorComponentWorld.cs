using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Component
{
    /// <summary>
    /// World
    /// </summary>
    public partial class ElectricLiveSensorComponent: ElectricBaseComponent
    {
        //public override int Id => (int)EComponentIdEnum.ElectricLiveSensor;
        internal ElectricCLiveSensor liveSensor;
        [HotfixableVirtual]
        public override partial void PostInit(bool isLoadFromDb);
    }
}