using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Common.Entity
{
    public partial class RpcEntity
    {
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 2094208211;
#else
        public const int CLASS_HASH = 2094208211;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public const string TYPE_NAME = "RpcEntity";
        public override string GetTypeName() => TYPE_NAME;
        public override int EntityType => EntityTypeId.RpcEntity;
        public RpcEntity() { }
        public RpcEntity(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public RpcEntity(bool useless, JSONNode json) : base(useless, json) { }
        public override ESyncRange SyncRange{ get; } = ESyncRange.LocalOnly;
        public override EPersistentType Persistent { get; set; } = EPersistentType.None;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int PROPERTY_EXTEND = 0;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.PROPERTY_EXTEND
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            12
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.PROPERTY_EXTEND
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.PROPERTY_EXTEND
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            1079267420
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.LocalOnly
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false
        };
        public static readonly int[] LodArray = new int[]
        {
            0
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "PropertyExtend"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0 + 10000
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.PROPERTY_EXTEND
        };
        public const int VALUE_TYPE_COUNT = 0;
        public const int REF_TYPE_COUNT = 1;
        public const int AVAILABLE_LODS = 0;
        public const int CLASS_LOD = 0;
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.PROPERTY_EXTEND:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _propertyExtend = null;
                        }
                        else
                        {
                            _propertyExtend = (EntityBasePropertyExtend)CustomTypeHelper.DeserializeCustomTypeBase<EntityBasePropertyExtend>(ref reader, ESerializeMode.Db);
                            _propertyExtend?.SetParentInfo(this, PropertyIds.PROPERTY_EXTEND);
                        }
                    }
                    else
                    {
                        return _propertyExtend.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            EntityId = reader.ReadInt64();
            DeserializeEssentialFields(ref reader, mode);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
            DeserializeComponents(ref reader, mode);
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            writer.Write(CLASS_HASH);
            writer.Write(EntityId);
            SerializeEssentialFields(ref writer, mode);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
            SerializeComponents(ref writer, mode);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            jsonObj["EntityId"] = EntityId;
            jsonObj["Components"] = WriteComponentsToEJson();
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            EntityId = json["EntityId"];
            ReadComponentsFromEJson(json["Components"].AsObject);
            DeserializeEssentialFields(json);
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.PROPERTY_EXTEND:
                    return DiffToStringPropertyExtend(ref reader, path, pathLen, pathString, depth);
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.PROPERTY_EXTEND:
                    propertyName = "PropertyExtend";
                    propertyBase = propertyExtend;
                    break;
                default: break;
            }
        }
    }
}