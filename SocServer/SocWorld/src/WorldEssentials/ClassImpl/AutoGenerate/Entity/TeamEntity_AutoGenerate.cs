using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Share.Framework
{
    public static partial class ContainerDecoder
    {
        public static object DeserializeTeamEntityContainer(ref MessagePackReader reader, int propertyId, ESerializeMode mode)
        {
            switch (propertyId)
            {
                case TeamEntity.PropertyIds.MEMBER_DIC:
                    {
                        return DictOfArrayDataSet.GetFromPool(ref reader, mode, 1291036587);
                    }
                case TeamEntity.PropertyIds.RECRUITMENT_APPLICATION:
                    {
                        return DictOfArrayDataSet.GetFromPool(ref reader, mode, 1785208763);
                    }
                case TeamEntity.PropertyIds.VOTE_ROLES:
                    {
                        return new BasicValueDictionary<ulong, bool>(ref reader, mode);
                    }
                default: return null;
            }
        }
    }
    public static partial class ShadowContainerDecoder
    {
        public static SyncContainerBase DeserializeTeamEntityContainer(ref MessagePackReader reader, int propertyId, long entityId)
        {
            switch (propertyId)
            {
                case TeamEntity.PropertyIds.MEMBER_DIC:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncDictionary.GetFromPool(ref reader, 1291036587, entityId);
                    }
                case TeamEntity.PropertyIds.RECRUITMENT_APPLICATION:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncDictionary.GetFromPool(ref reader, 1785208763, entityId);
                    }
                case TeamEntity.PropertyIds.VOTE_ROLES:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicValueDictionary<ulong, bool>(ref reader, ESerializeMode.All));
                    }
                default: return null;
            }
        }
    }
}
namespace WizardGames.Soc.Common.Entity
{
    public partial class TeamEntity
    {
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 659405843;
#else
        public const int CLASS_HASH = 659405843;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public const string TYPE_NAME = "TeamEntity";
        public override string GetTypeName() => TYPE_NAME;
        public override int EntityType => EntityTypeId.TeamEntity;
        public TeamEntity() { }
        public TeamEntity(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public TeamEntity(bool useless, JSONNode json) : base(useless, json) { }
        public override ESyncRange SyncRange{ get; } = ESyncRange.All;
        public override EPersistentType Persistent { get; set; } = EPersistentType.Normal;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int PROPERTY_EXTEND = 0;
            public const int CAPTAIN_ROLE_ID = 1;
            public const int MEMBER_DIC = 2;
            public const int TEAM_REPUTATION_INFO = 3;
            public const int TEAM_BORN_INFO = 4;
            public const int RECRUITMENT_INFO = 5;
            public const int RECRUITMENT_APPLICATION = 6;
            public const int IS_REMAINED_WHEN_NO_MEMBER = 7;
            public const int IMPEACH_TIMER_ID = 8;
            public const int IMPEACH_STATE = 9;
            public const int APPLICATION_PLAYER = 10;
            public const int VOTE_TIMER_ID = 11;
            public const int VOTE_ROLES = 12;
            public const int TERRITORY_TIMER_ID = 13;
            public const int INVITE_OTHER_COUNT = 14;
            public const int CAN_SHARE_RECRUIT = 15;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.PROPERTY_EXTEND, PropertyIds.CAPTAIN_ROLE_ID, PropertyIds.MEMBER_DIC, PropertyIds.TEAM_REPUTATION_INFO, PropertyIds.TEAM_BORN_INFO, PropertyIds.RECRUITMENT_INFO, PropertyIds.RECRUITMENT_APPLICATION, PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, PropertyIds.IMPEACH_TIMER_ID, PropertyIds.IMPEACH_STATE, PropertyIds.APPLICATION_PLAYER, PropertyIds.VOTE_TIMER_ID, PropertyIds.VOTE_ROLES, PropertyIds.TERRITORY_TIMER_ID, PropertyIds.INVITE_OTHER_COUNT, PropertyIds.CAN_SHARE_RECRUIT
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            12, 11, 13, 12, 12, 12, 13, 6, 2, 1, 11, 2, 13, 2, 1, 6
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.PROPERTY_EXTEND, PropertyIds.CAPTAIN_ROLE_ID, PropertyIds.MEMBER_DIC, PropertyIds.TEAM_REPUTATION_INFO, PropertyIds.TEAM_BORN_INFO, PropertyIds.RECRUITMENT_INFO, PropertyIds.RECRUITMENT_APPLICATION, PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, PropertyIds.IMPEACH_TIMER_ID, PropertyIds.IMPEACH_STATE, PropertyIds.APPLICATION_PLAYER, PropertyIds.VOTE_TIMER_ID, PropertyIds.VOTE_ROLES, PropertyIds.TERRITORY_TIMER_ID, PropertyIds.INVITE_OTHER_COUNT, PropertyIds.CAN_SHARE_RECRUIT
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.PROPERTY_EXTEND, PropertyIds.CAPTAIN_ROLE_ID, PropertyIds.MEMBER_DIC, PropertyIds.TEAM_REPUTATION_INFO, PropertyIds.TEAM_BORN_INFO, PropertyIds.RECRUITMENT_INFO, PropertyIds.RECRUITMENT_APPLICATION, PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, PropertyIds.IMPEACH_TIMER_ID, PropertyIds.IMPEACH_STATE, PropertyIds.APPLICATION_PLAYER, PropertyIds.VOTE_TIMER_ID, PropertyIds.VOTE_ROLES, PropertyIds.TERRITORY_TIMER_ID, PropertyIds.INVITE_OTHER_COUNT, PropertyIds.CAN_SHARE_RECRUIT
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.CAPTAIN_ROLE_ID, PropertyIds.MEMBER_DIC, PropertyIds.TEAM_REPUTATION_INFO, PropertyIds.RECRUITMENT_INFO, PropertyIds.IMPEACH_STATE, PropertyIds.APPLICATION_PLAYER, PropertyIds.VOTE_ROLES, PropertyIds.INVITE_OTHER_COUNT, PropertyIds.CAN_SHARE_RECRUIT
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.CAPTAIN_ROLE_ID, PropertyIds.MEMBER_DIC, PropertyIds.TEAM_REPUTATION_INFO, PropertyIds.RECRUITMENT_INFO, PropertyIds.IMPEACH_STATE, PropertyIds.APPLICATION_PLAYER, PropertyIds.VOTE_ROLES, PropertyIds.INVITE_OTHER_COUNT, PropertyIds.CAN_SHARE_RECRUIT
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.ULONG << 16) | PropertyIds.CAPTAIN_ROLE_ID, (TypeDefine.CONTAINER << 16) | PropertyIds.MEMBER_DIC, (TypeDefine.CUSTOM_TYPE << 16) | PropertyIds.TEAM_REPUTATION_INFO, (TypeDefine.CUSTOM_TYPE << 16) | PropertyIds.RECRUITMENT_INFO, (TypeDefine.INT << 16) | PropertyIds.IMPEACH_STATE, (TypeDefine.ULONG << 16) | PropertyIds.APPLICATION_PLAYER, (TypeDefine.CONTAINER << 16) | PropertyIds.VOTE_ROLES, (TypeDefine.INT << 16) | PropertyIds.INVITE_OTHER_COUNT, (TypeDefine.BOOL << 16) | PropertyIds.CAN_SHARE_RECRUIT
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.CAPTAIN_ROLE_ID, PropertyIds.MEMBER_DIC
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            1079267420, 0, 0, 1523684862, 2123961122, 1425930436, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.LocalOnly, ESyncRange.All, ESyncRange.All, ESyncRange.Clients, ESyncRange.LocalOnly, ESyncRange.Clients, ESyncRange.LocalOnly, ESyncRange.LocalOnly, ESyncRange.LocalOnly, ESyncRange.Clients, ESyncRange.Clients, ESyncRange.LocalOnly, ESyncRange.Clients, ESyncRange.LocalOnly, ESyncRange.Clients, ESyncRange.Clients
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false
        };
        public static readonly int[] LodArray = new int[]
        {
            0, 1, 1, 1, 0, 1, 0, 0, 0, 1, 1, 0, 1, 0, 1, 1
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "PropertyExtend", "CaptainRoleId", "MemberDic", "TeamReputationInfo", "TeamBornInfo", "RecruitmentInfo", "RecruitmentApplication", "IsRemainedWhenNoMember", "ImpeachTimerId", "ImpeachState", "ApplicationPlayer", "VoteTimerId", "VoteRoles", "TerritoryTimerId", "InviteOtherCount", "CanShareRecruit"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0 + 10000, 0, 1 + 10000, 2 + 10000, 3 + 10000, 4 + 10000, 5 + 10000, 1, 2, 3, 4, 5, 6 + 10000, 6, 7, 8
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.PROPERTY_EXTEND, PropertyIds.MEMBER_DIC, PropertyIds.TEAM_REPUTATION_INFO, PropertyIds.TEAM_BORN_INFO, PropertyIds.RECRUITMENT_INFO, PropertyIds.RECRUITMENT_APPLICATION, PropertyIds.VOTE_ROLES
        };
        public const int VALUE_TYPE_COUNT = 9;
        public const int REF_TYPE_COUNT = 7;
        public const int AVAILABLE_LODS = 1;
        public const int CLASS_LOD = 1;
        public ulong CaptainRoleId
        {
            get => _captainRoleId;
            set
            {
                if (_captainRoleId == value) return;
                var oldValue = _captainRoleId;
                _captainRoleId = value;
                TransactionAssignBackup(PropertyIds.CAPTAIN_ROLE_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.CAPTAIN_ROLE_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.CAPTAIN_ROLE_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 队伍成员列表，该列表里的RoleId可能会在战局内找不到PlayerEntity，只有玩家进服了之后才创建PlayerEntity
        /// </summary>
        public CustomValueDictionary<ulong, TeamMemberInfo> MemberDic
        {
            get => _memberDic;
            set
            {
                if (_memberDic == value) return;
                var oldValue = _memberDic;
                _memberDic?.ClearParentInfo();
                _memberDic = value;
                value?.SetParentInfo(this, PropertyIds.MEMBER_DIC);
                TransactionAssignBackup(PropertyIds.MEMBER_DIC, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 1291036587};
                if (!SyncPropValue(ref ctx, PropertyIds.MEMBER_DIC, value)) return;
                TryInvokeFieldChange(PropertyIds.MEMBER_DIC, oldValue, value);
            }
        }
        public ReputationTeamInfo TeamReputationInfo
        {
            get => _teamReputationInfo;
            set
            {
                if (_teamReputationInfo == value) return;
                var oldValue = _teamReputationInfo;
                _teamReputationInfo?.ClearParentInfo();
                _teamReputationInfo = value;
                value?.SetParentInfo(this, PropertyIds.TEAM_REPUTATION_INFO);
                TransactionAssignBackup(PropertyIds.TEAM_REPUTATION_INFO, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 1523684862};
                if (!SyncPropValue(ref ctx, PropertyIds.TEAM_REPUTATION_INFO, value)) return;
                TryInvokeFieldChange(PropertyIds.TEAM_REPUTATION_INFO, oldValue, value);
            }
        }
        public TeamBornInfo TeamBornInfo
        {
            get => _teamBornInfo;
            set
            {
                if (_teamBornInfo == value) return;
                var oldValue = _teamBornInfo;
                _teamBornInfo?.ClearParentInfo();
                _teamBornInfo = value;
                value?.SetParentInfo(this, PropertyIds.TEAM_BORN_INFO);
                TransactionAssignBackup(PropertyIds.TEAM_BORN_INFO, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 2123961122};
                if (!SyncPropValue(ref ctx, PropertyIds.TEAM_BORN_INFO, value)) return;
                TryInvokeFieldChange(PropertyIds.TEAM_BORN_INFO, oldValue, value);
            }
        }
        public LobbyRecruitmentInfo RecruitmentInfo
        {
            get => _recruitmentInfo;
            set
            {
                if (_recruitmentInfo == value) return;
                var oldValue = _recruitmentInfo;
                _recruitmentInfo?.ClearParentInfo();
                _recruitmentInfo = value;
                value?.SetParentInfo(this, PropertyIds.RECRUITMENT_INFO);
                TransactionAssignBackup(PropertyIds.RECRUITMENT_INFO, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 1425930436};
                if (!SyncPropValue(ref ctx, PropertyIds.RECRUITMENT_INFO, value)) return;
                TryInvokeFieldChange(PropertyIds.RECRUITMENT_INFO, oldValue, value);
            }
        }
        public CustomValueDictionary<ulong, TeamRecruitmentApplicationInfo> RecruitmentApplication
        {
            get => _recruitmentApplication;
            set
            {
                if (_recruitmentApplication == value) return;
                var oldValue = _recruitmentApplication;
                _recruitmentApplication?.ClearParentInfo();
                _recruitmentApplication = value;
                value?.SetParentInfo(this, PropertyIds.RECRUITMENT_APPLICATION);
                TransactionAssignBackup(PropertyIds.RECRUITMENT_APPLICATION, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 1785208763};
                if (!SyncPropValue(ref ctx, PropertyIds.RECRUITMENT_APPLICATION, value)) return;
                TryInvokeFieldChange(PropertyIds.RECRUITMENT_APPLICATION, oldValue, value);
            }
        }
        public bool IsRemainedWhenNoMember
        {
            get => _isRemainedWhenNoMember;
            set
            {
                if (_isRemainedWhenNoMember == value) return;
                var oldValue = _isRemainedWhenNoMember;
                _isRemainedWhenNoMember = value;
                TransactionAssignBackup(PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, value)) return;
                TryInvokeFieldChange(PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, oldValue, value);
            }
        }
        /// <summary>
        /// 检测弹劾队长计时器Id
        /// </summary>
        public long ImpeachTimerId
        {
            get => _impeachTimerId;
            set
            {
                if (_impeachTimerId == value) return;
                var oldValue = _impeachTimerId;
                _impeachTimerId = value;
                TransactionAssignBackup(PropertyIds.IMPEACH_TIMER_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.IMPEACH_TIMER_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.IMPEACH_TIMER_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 弹劾状态
        /// </summary>
        public int ImpeachState
        {
            get => _impeachState;
            set
            {
                if (_impeachState == value) return;
                var oldValue = _impeachState;
                _impeachState = value;
                TransactionAssignBackup(PropertyIds.IMPEACH_STATE, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.IMPEACH_STATE, value)) return;
                TryInvokeFieldChange(PropertyIds.IMPEACH_STATE, oldValue, value);
            }
        }
        /// <summary>
        /// 弹劾队长申请成为队长的玩家RoleId
        /// </summary>
        public ulong ApplicationPlayer
        {
            get => _applicationPlayer;
            set
            {
                if (_applicationPlayer == value) return;
                var oldValue = _applicationPlayer;
                _applicationPlayer = value;
                TransactionAssignBackup(PropertyIds.APPLICATION_PLAYER, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.APPLICATION_PLAYER, value)) return;
                TryInvokeFieldChange(PropertyIds.APPLICATION_PLAYER, oldValue, value);
            }
        }
        /// <summary>
        /// 弹劾投票计时器Id
        /// </summary>
        public long VoteTimerId
        {
            get => _voteTimerId;
            set
            {
                if (_voteTimerId == value) return;
                var oldValue = _voteTimerId;
                _voteTimerId = value;
                TransactionAssignBackup(PropertyIds.VOTE_TIMER_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.VOTE_TIMER_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.VOTE_TIMER_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 弹劾投票票数
        /// </summary>
        public BasicValueDictionary<ulong, bool> VoteRoles
        {
            get => _voteRoles;
            set
            {
                if (_voteRoles == value) return;
                var oldValue = _voteRoles;
                _voteRoles?.ClearParentInfo();
                _voteRoles = value;
                value?.SetParentInfo(this, PropertyIds.VOTE_ROLES);
                TransactionAssignBackup(PropertyIds.VOTE_ROLES, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = -1};
                if (!SyncPropValue(ref ctx, PropertyIds.VOTE_ROLES, value)) return;
                TryInvokeFieldChange(PropertyIds.VOTE_ROLES, oldValue, value);
            }
        }
        /// <summary>
        /// 检测队伍中拥有领地权限的玩家多长时间没上线 则启动转移领地权限的逻辑
        /// </summary>
        public long TerritoryTimerId
        {
            get => _territoryTimerId;
            set
            {
                if (_territoryTimerId == value) return;
                var oldValue = _territoryTimerId;
                _territoryTimerId = value;
                TransactionAssignBackup(PropertyIds.TERRITORY_TIMER_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.TERRITORY_TIMER_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.TERRITORY_TIMER_ID, oldValue, value);
            }
        }
        public int InviteOtherCount
        {
            get => _inviteOtherCount;
            set
            {
                if (_inviteOtherCount == value) return;
                var oldValue = _inviteOtherCount;
                _inviteOtherCount = value;
                TransactionAssignBackup(PropertyIds.INVITE_OTHER_COUNT, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.INVITE_OTHER_COUNT, value)) return;
                TryInvokeFieldChange(PropertyIds.INVITE_OTHER_COUNT, oldValue, value);
            }
        }
        /// <summary>
        /// 是否可以分享招募信息到兴趣部落
        /// </summary>
        public bool CanShareRecruit
        {
            get => _canShareRecruit;
            set
            {
                if (_canShareRecruit == value) return;
                var oldValue = _canShareRecruit;
                _canShareRecruit = value;
                TransactionAssignBackup(PropertyIds.CAN_SHARE_RECRUIT, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.CAN_SHARE_RECRUIT, value)) return;
                TryInvokeFieldChange(PropertyIds.CAN_SHARE_RECRUIT, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.CAPTAIN_ROLE_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.ULONG) throw new Exception($"Type mismatch, expecting ulong got {seg.BasicType.Type}");
#endif
                    ulong newValue = seg.BasicType.ULongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.CAPTAIN_ROLE_ID, newValue);
                    var oldValue = _captainRoleId;
                    _captainRoleId = newValue;
                    TryInvokeFieldChange(1, oldValue, _captainRoleId);
                    return this;
                }
                case PropertyIds.MEMBER_DIC:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _memberDic?.ClearParentInfo();
                            _memberDic?.ResetWhenDeserialize();
                            CustomValueDictionary<ulong, TeamMemberInfo> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 1291036587, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.MEMBER_DIC, newValue);
                            var oldValue = _memberDic;
                            _memberDic = newValue;
                            _memberDic?.SetParentInfo(this, PropertyIds.MEMBER_DIC);
                            TryInvokeFieldChange(2, oldValue, _memberDic);
                            return this;
                        }
                        else
                        {
                            return _memberDic.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.TEAM_REPUTATION_INFO:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _teamReputationInfo?.ClearParentInfo();
                            ReputationTeamInfo newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<ReputationTeamInfo>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 1523684862, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.TEAM_REPUTATION_INFO, newValue);
                            var oldValue = _teamReputationInfo;
                            _teamReputationInfo = newValue;
                            _teamReputationInfo?.SetParentInfo(this, PropertyIds.TEAM_REPUTATION_INFO);
                            TryInvokeFieldChange(3, oldValue, _teamReputationInfo);
                            return this;
                        }
                        else
                        {
                            return _teamReputationInfo.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.TEAM_BORN_INFO:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _teamBornInfo?.ClearParentInfo();
                            TeamBornInfo newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<TeamBornInfo>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 2123961122, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.TEAM_BORN_INFO, newValue);
                            var oldValue = _teamBornInfo;
                            _teamBornInfo = newValue;
                            _teamBornInfo?.SetParentInfo(this, PropertyIds.TEAM_BORN_INFO);
                            TryInvokeFieldChange(4, oldValue, _teamBornInfo);
                            return this;
                        }
                        else
                        {
                            return _teamBornInfo.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.RECRUITMENT_INFO:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _recruitmentInfo?.ClearParentInfo();
                            LobbyRecruitmentInfo newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<LobbyRecruitmentInfo>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 1425930436, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.RECRUITMENT_INFO, newValue);
                            var oldValue = _recruitmentInfo;
                            _recruitmentInfo = newValue;
                            _recruitmentInfo?.SetParentInfo(this, PropertyIds.RECRUITMENT_INFO);
                            TryInvokeFieldChange(5, oldValue, _recruitmentInfo);
                            return this;
                        }
                        else
                        {
                            return _recruitmentInfo.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.RECRUITMENT_APPLICATION:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _recruitmentApplication?.ClearParentInfo();
                            _recruitmentApplication?.ResetWhenDeserialize();
                            CustomValueDictionary<ulong, TeamRecruitmentApplicationInfo> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 1785208763, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.RECRUITMENT_APPLICATION, newValue);
                            var oldValue = _recruitmentApplication;
                            _recruitmentApplication = newValue;
                            _recruitmentApplication?.SetParentInfo(this, PropertyIds.RECRUITMENT_APPLICATION);
                            TryInvokeFieldChange(6, oldValue, _recruitmentApplication);
                            return this;
                        }
                        else
                        {
                            return _recruitmentApplication.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.BOOL) throw new Exception($"Type mismatch, expecting bool got {seg.BasicType.Type}");
#endif
                    bool newValue = seg.BasicType.BoolValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.IS_REMAINED_WHEN_NO_MEMBER, newValue);
                    var oldValue = _isRemainedWhenNoMember;
                    _isRemainedWhenNoMember = newValue;
                    TryInvokeFieldChange(7, oldValue, _isRemainedWhenNoMember);
                    return this;
                }
                case PropertyIds.IMPEACH_TIMER_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.IMPEACH_TIMER_ID, newValue);
                    var oldValue = _impeachTimerId;
                    _impeachTimerId = newValue;
                    TryInvokeFieldChange(8, oldValue, _impeachTimerId);
                    return this;
                }
                case PropertyIds.IMPEACH_STATE:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.IMPEACH_STATE, newValue);
                    var oldValue = _impeachState;
                    _impeachState = newValue;
                    TryInvokeFieldChange(9, oldValue, _impeachState);
                    return this;
                }
                case PropertyIds.APPLICATION_PLAYER:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.ULONG) throw new Exception($"Type mismatch, expecting ulong got {seg.BasicType.Type}");
#endif
                    ulong newValue = seg.BasicType.ULongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.APPLICATION_PLAYER, newValue);
                    var oldValue = _applicationPlayer;
                    _applicationPlayer = newValue;
                    TryInvokeFieldChange(10, oldValue, _applicationPlayer);
                    return this;
                }
                case PropertyIds.VOTE_TIMER_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.VOTE_TIMER_ID, newValue);
                    var oldValue = _voteTimerId;
                    _voteTimerId = newValue;
                    TryInvokeFieldChange(11, oldValue, _voteTimerId);
                    return this;
                }
                case PropertyIds.VOTE_ROLES:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _voteRoles?.ClearParentInfo();
                            _voteRoles?.ResetWhenDeserialize();
                            BasicValueDictionary<ulong, bool> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.VOTE_ROLES, newValue);
                            var oldValue = _voteRoles;
                            _voteRoles = newValue;
                            _voteRoles?.SetParentInfo(this, PropertyIds.VOTE_ROLES);
                            TryInvokeFieldChange(12, oldValue, _voteRoles);
                            return this;
                        }
                        else
                        {
                            return _voteRoles.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.TERRITORY_TIMER_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.TERRITORY_TIMER_ID, newValue);
                    var oldValue = _territoryTimerId;
                    _territoryTimerId = newValue;
                    TryInvokeFieldChange(13, oldValue, _territoryTimerId);
                    return this;
                }
                case PropertyIds.INVITE_OTHER_COUNT:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.INVITE_OTHER_COUNT, newValue);
                    var oldValue = _inviteOtherCount;
                    _inviteOtherCount = newValue;
                    TryInvokeFieldChange(14, oldValue, _inviteOtherCount);
                    return this;
                }
                case PropertyIds.CAN_SHARE_RECRUIT:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.BOOL) throw new Exception($"Type mismatch, expecting bool got {seg.BasicType.Type}");
#endif
                    bool newValue = seg.BasicType.BoolValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.CAN_SHARE_RECRUIT, newValue);
                    var oldValue = _canShareRecruit;
                    _canShareRecruit = newValue;
                    TryInvokeFieldChange(15, oldValue, _canShareRecruit);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.PROPERTY_EXTEND:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _propertyExtend = null;
                        }
                        else
                        {
                            _propertyExtend = (EntityBasePropertyExtend)CustomTypeHelper.DeserializeCustomTypeBase<EntityBasePropertyExtend>(ref reader, ESerializeMode.Db);
                            _propertyExtend?.SetParentInfo(this, PropertyIds.PROPERTY_EXTEND);
                        }
                    }
                    else
                    {
                        return _propertyExtend.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.CAPTAIN_ROLE_ID:
                {
                    _captainRoleId = reader.ReadUInt64();
                    return true;
                }
                case PropertyIds.MEMBER_DIC:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _memberDic = null;
                        }
                        else
                        {
                            _memberDic = new CustomValueDictionary<ulong, TeamMemberInfo>(ref reader, ESerializeMode.Db);
                            _memberDic?.SetParentInfo(this, PropertyIds.MEMBER_DIC);
                        }
                    }
                    else
                    {
                        return _memberDic.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.TEAM_REPUTATION_INFO:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _teamReputationInfo = null;
                        }
                        else
                        {
                            _teamReputationInfo = (ReputationTeamInfo)CustomTypeHelper.DeserializeCustomTypeBase<ReputationTeamInfo>(ref reader, ESerializeMode.Db);
                            _teamReputationInfo?.SetParentInfo(this, PropertyIds.TEAM_REPUTATION_INFO);
                        }
                    }
                    else
                    {
                        return _teamReputationInfo.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.TEAM_BORN_INFO:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _teamBornInfo = null;
                        }
                        else
                        {
                            _teamBornInfo = (TeamBornInfo)CustomTypeHelper.DeserializeCustomTypeBase<TeamBornInfo>(ref reader, ESerializeMode.Db);
                            _teamBornInfo?.SetParentInfo(this, PropertyIds.TEAM_BORN_INFO);
                        }
                    }
                    else
                    {
                        return _teamBornInfo.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.RECRUITMENT_INFO:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _recruitmentInfo = null;
                        }
                        else
                        {
                            _recruitmentInfo = (LobbyRecruitmentInfo)CustomTypeHelper.DeserializeCustomTypeBase<LobbyRecruitmentInfo>(ref reader, ESerializeMode.Db);
                            _recruitmentInfo?.SetParentInfo(this, PropertyIds.RECRUITMENT_INFO);
                        }
                    }
                    else
                    {
                        return _recruitmentInfo.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.RECRUITMENT_APPLICATION:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _recruitmentApplication = null;
                        }
                        else
                        {
                            _recruitmentApplication = new CustomValueDictionary<ulong, TeamRecruitmentApplicationInfo>(ref reader, ESerializeMode.Db);
                            _recruitmentApplication?.SetParentInfo(this, PropertyIds.RECRUITMENT_APPLICATION);
                        }
                    }
                    else
                    {
                        return _recruitmentApplication.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                {
                    _isRemainedWhenNoMember = reader.ReadBoolean();
                    return true;
                }
                case PropertyIds.IMPEACH_TIMER_ID:
                {
                    _impeachTimerId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.IMPEACH_STATE:
                {
                    _impeachState = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.APPLICATION_PLAYER:
                {
                    _applicationPlayer = reader.ReadUInt64();
                    return true;
                }
                case PropertyIds.VOTE_TIMER_ID:
                {
                    _voteTimerId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.VOTE_ROLES:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _voteRoles = null;
                        }
                        else
                        {
                            _voteRoles = new BasicValueDictionary<ulong, bool>(ref reader, ESerializeMode.Db);
                            _voteRoles?.SetParentInfo(this, PropertyIds.VOTE_ROLES);
                        }
                    }
                    else
                    {
                        return _voteRoles.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.TERRITORY_TIMER_ID:
                {
                    _territoryTimerId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.INVITE_OTHER_COUNT:
                {
                    _inviteOtherCount = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.CAN_SHARE_RECRUIT:
                {
                    _canShareRecruit = reader.ReadBoolean();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.CAPTAIN_ROLE_ID:
                {
                    _captainRoleId = valueUnion.SimpleValue.ULongValue;
                    break;
                }
                case PropertyIds.MEMBER_DIC:
                {
                    _memberDic = valueUnion.CustomTypeValue as CustomValueDictionary<ulong, TeamMemberInfo>;
                    break;
                }
                case PropertyIds.TEAM_REPUTATION_INFO:
                {
                    _teamReputationInfo = valueUnion.CustomTypeValue as ReputationTeamInfo;
                    break;
                }
                case PropertyIds.TEAM_BORN_INFO:
                {
                    _teamBornInfo = valueUnion.CustomTypeValue as TeamBornInfo;
                    break;
                }
                case PropertyIds.RECRUITMENT_INFO:
                {
                    _recruitmentInfo = valueUnion.CustomTypeValue as LobbyRecruitmentInfo;
                    break;
                }
                case PropertyIds.RECRUITMENT_APPLICATION:
                {
                    _recruitmentApplication = valueUnion.CustomTypeValue as CustomValueDictionary<ulong, TeamRecruitmentApplicationInfo>;
                    break;
                }
                case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                {
                    _isRemainedWhenNoMember = valueUnion.SimpleValue.BoolValue;
                    break;
                }
                case PropertyIds.IMPEACH_TIMER_ID:
                {
                    _impeachTimerId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.IMPEACH_STATE:
                {
                    _impeachState = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.APPLICATION_PLAYER:
                {
                    _applicationPlayer = valueUnion.SimpleValue.ULongValue;
                    break;
                }
                case PropertyIds.VOTE_TIMER_ID:
                {
                    _voteTimerId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.VOTE_ROLES:
                {
                    _voteRoles = valueUnion.CustomTypeValue as BasicValueDictionary<ulong, bool>;
                    break;
                }
                case PropertyIds.TERRITORY_TIMER_ID:
                {
                    _territoryTimerId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.INVITE_OTHER_COUNT:
                {
                    _inviteOtherCount = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.CAN_SHARE_RECRUIT:
                {
                    _canShareRecruit = valueUnion.SimpleValue.BoolValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            EntityId = reader.ReadInt64();
            DeserializeEssentialFields(ref reader, mode);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.CAPTAIN_ROLE_ID:
                    _captainRoleId = reader.ReadUInt64();
                    break;
                case PropertyIds.MEMBER_DIC:
                    {
                        if (_memberDic != null)
                        {
                            _memberDic.ClearParentInfo();
                            _memberDic.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _memberDic = null;
                            continue;
                        }
                        _memberDic = new(ref reader, mode);
                        _memberDic.SetParentInfo(this, PropertyIds.MEMBER_DIC);
                    }
                    break;
                case PropertyIds.TEAM_REPUTATION_INFO:
                    {
                        _teamReputationInfo?.ClearParentInfo();
                        _teamReputationInfo = CustomTypeHelper.DeserializeCustomTypeBase<ReputationTeamInfo>(ref reader, mode);
                        _teamReputationInfo?.SetParentInfo(this, PropertyIds.TEAM_REPUTATION_INFO);
                    }
                    break;
                case PropertyIds.TEAM_BORN_INFO:
                    {
                        _teamBornInfo?.ClearParentInfo();
                        _teamBornInfo = CustomTypeHelper.DeserializeCustomTypeBase<TeamBornInfo>(ref reader, mode);
                        _teamBornInfo?.SetParentInfo(this, PropertyIds.TEAM_BORN_INFO);
                    }
                    break;
                case PropertyIds.RECRUITMENT_INFO:
                    {
                        _recruitmentInfo?.ClearParentInfo();
                        _recruitmentInfo = CustomTypeHelper.DeserializeCustomTypeBase<LobbyRecruitmentInfo>(ref reader, mode);
                        _recruitmentInfo?.SetParentInfo(this, PropertyIds.RECRUITMENT_INFO);
                    }
                    break;
                case PropertyIds.RECRUITMENT_APPLICATION:
                    {
                        if (_recruitmentApplication != null)
                        {
                            _recruitmentApplication.ClearParentInfo();
                            _recruitmentApplication.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _recruitmentApplication = null;
                            continue;
                        }
                        _recruitmentApplication = new(ref reader, mode);
                        _recruitmentApplication.SetParentInfo(this, PropertyIds.RECRUITMENT_APPLICATION);
                    }
                    break;
                case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                    _isRemainedWhenNoMember = reader.ReadBoolean();
                    break;
                case PropertyIds.IMPEACH_TIMER_ID:
                    _impeachTimerId = reader.ReadInt64();
                    break;
                case PropertyIds.IMPEACH_STATE:
                    _impeachState = reader.ReadInt32();
                    break;
                case PropertyIds.APPLICATION_PLAYER:
                    _applicationPlayer = reader.ReadUInt64();
                    break;
                case PropertyIds.VOTE_TIMER_ID:
                    _voteTimerId = reader.ReadInt64();
                    break;
                case PropertyIds.VOTE_ROLES:
                    {
                        if (_voteRoles != null)
                        {
                            _voteRoles.ClearParentInfo();
                            _voteRoles.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _voteRoles = null;
                            continue;
                        }
                        _voteRoles = new(ref reader, mode);
                        _voteRoles.SetParentInfo(this, PropertyIds.VOTE_ROLES);
                    }
                    break;
                case PropertyIds.TERRITORY_TIMER_ID:
                    _territoryTimerId = reader.ReadInt64();
                    break;
                case PropertyIds.INVITE_OTHER_COUNT:
                    _inviteOtherCount = reader.ReadInt32();
                    break;
                case PropertyIds.CAN_SHARE_RECRUIT:
                    _canShareRecruit = reader.ReadBoolean();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
            DeserializeComponents(ref reader, mode);
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            writer.Write(CLASS_HASH);
            writer.Write(EntityId);
            SerializeEssentialFields(ref writer, mode);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.CAPTAIN_ROLE_ID:
                        writer.Write(_captainRoleId);
                        break;
                    case PropertyIds.MEMBER_DIC:
                        if (_memberDic == null)
                            writer.WriteNil();
                        else
                            _memberDic.SerializeCore(ref writer, mode, 1291036587);
                        break;
                    case PropertyIds.TEAM_REPUTATION_INFO:
                        if (_teamReputationInfo == null)
                            writer.WriteNil();
                        else
                            _teamReputationInfo.SerializeCore(ref writer, mode, 1523684862);
                        break;
                    case PropertyIds.TEAM_BORN_INFO:
                        if (_teamBornInfo == null)
                            writer.WriteNil();
                        else
                            _teamBornInfo.SerializeCore(ref writer, mode, 2123961122);
                        break;
                    case PropertyIds.RECRUITMENT_INFO:
                        if (_recruitmentInfo == null)
                            writer.WriteNil();
                        else
                            _recruitmentInfo.SerializeCore(ref writer, mode, 1425930436);
                        break;
                    case PropertyIds.RECRUITMENT_APPLICATION:
                        if (_recruitmentApplication == null)
                            writer.WriteNil();
                        else
                            _recruitmentApplication.SerializeCore(ref writer, mode, 1785208763);
                        break;
                    case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                        writer.Write(_isRemainedWhenNoMember);
                        break;
                    case PropertyIds.IMPEACH_TIMER_ID:
                        writer.Write(_impeachTimerId);
                        break;
                    case PropertyIds.IMPEACH_STATE:
                        writer.Write(_impeachState);
                        break;
                    case PropertyIds.APPLICATION_PLAYER:
                        writer.Write(_applicationPlayer);
                        break;
                    case PropertyIds.VOTE_TIMER_ID:
                        writer.Write(_voteTimerId);
                        break;
                    case PropertyIds.VOTE_ROLES:
                        if (_voteRoles == null)
                            writer.WriteNil();
                        else
                            _voteRoles.SerializeCore(ref writer, mode, -1);
                        break;
                    case PropertyIds.TERRITORY_TIMER_ID:
                        writer.Write(_territoryTimerId);
                        break;
                    case PropertyIds.INVITE_OTHER_COUNT:
                        writer.Write(_inviteOtherCount);
                        break;
                    case PropertyIds.CAN_SHARE_RECRUIT:
                        writer.Write(_canShareRecruit);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
            SerializeComponents(ref writer, mode);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            jsonObj["EntityId"] = EntityId;
            jsonObj["Components"] = WriteComponentsToEJson();
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.CAPTAIN_ROLE_ID:
                        jsonObj["CaptainRoleId"] = CaptainRoleId;
                        break;
                    case PropertyIds.MEMBER_DIC:
                        if (MemberDic != null) jsonObj["MemberDic"] = MemberDic.ToEJson();
                        break;
                    case PropertyIds.TEAM_REPUTATION_INFO:
                        if (TeamReputationInfo != null) jsonObj["TeamReputationInfo"] = TeamReputationInfo.ToEJson();
                        break;
                    case PropertyIds.TEAM_BORN_INFO:
                        if (TeamBornInfo != null) jsonObj["TeamBornInfo"] = TeamBornInfo.ToEJson();
                        break;
                    case PropertyIds.RECRUITMENT_INFO:
                        if (RecruitmentInfo != null) jsonObj["RecruitmentInfo"] = RecruitmentInfo.ToEJson();
                        break;
                    case PropertyIds.RECRUITMENT_APPLICATION:
                        if (RecruitmentApplication != null) jsonObj["RecruitmentApplication"] = RecruitmentApplication.ToEJson();
                        break;
                    case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                        jsonObj["IsRemainedWhenNoMember"] = IsRemainedWhenNoMember;
                        break;
                    case PropertyIds.IMPEACH_TIMER_ID:
                        jsonObj["ImpeachTimerId"] = ImpeachTimerId;
                        break;
                    case PropertyIds.IMPEACH_STATE:
                        jsonObj["ImpeachState"] = ImpeachState;
                        break;
                    case PropertyIds.APPLICATION_PLAYER:
                        jsonObj["ApplicationPlayer"] = ApplicationPlayer;
                        break;
                    case PropertyIds.VOTE_TIMER_ID:
                        jsonObj["VoteTimerId"] = VoteTimerId;
                        break;
                    case PropertyIds.VOTE_ROLES:
                        if (VoteRoles != null) jsonObj["VoteRoles"] = VoteRoles.ToEJson();
                        break;
                    case PropertyIds.TERRITORY_TIMER_ID:
                        jsonObj["TerritoryTimerId"] = TerritoryTimerId;
                        break;
                    case PropertyIds.INVITE_OTHER_COUNT:
                        jsonObj["InviteOtherCount"] = InviteOtherCount;
                        break;
                    case PropertyIds.CAN_SHARE_RECRUIT:
                        jsonObj["CanShareRecruit"] = CanShareRecruit;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            EntityId = json["EntityId"];
            ReadComponentsFromEJson(json["Components"].AsObject);
            DeserializeEssentialFields(json);
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.CAPTAIN_ROLE_ID:
                        if (json.HasKey("CaptainRoleId"))
                        {
                            _captainRoleId = json["CaptainRoleId"].AsULong;
                        }
                        break;
                    case PropertyIds.MEMBER_DIC:
                    {
                        if (_memberDic != null)
                        {
                            _memberDic.ClearParentInfo();
                            _memberDic.ResetWhenDeserialize();
                        }
                        if (json.HasKey("MemberDic"))
                        {
                            _memberDic = new(json["MemberDic"]);
                            _memberDic.SetParentInfo(this, PropertyIds.MEMBER_DIC);
                        }
                    }
                    break;
                    case PropertyIds.TEAM_REPUTATION_INFO:
                    {
                        _teamReputationInfo?.ClearParentInfo();
                        if (json.HasKey("TeamReputationInfo"))
                        {
                            _teamReputationInfo = CustomTypeFactory.ConstructCustomTypeByJson(json["TeamReputationInfo"]) as ReputationTeamInfo;
                            _teamReputationInfo?.SetParentInfo(this, PropertyIds.TEAM_REPUTATION_INFO);
                        }
                    }
                    break;
                    case PropertyIds.TEAM_BORN_INFO:
                    {
                        _teamBornInfo?.ClearParentInfo();
                        if (json.HasKey("TeamBornInfo"))
                        {
                            _teamBornInfo = CustomTypeFactory.ConstructCustomTypeByJson(json["TeamBornInfo"]) as TeamBornInfo;
                            _teamBornInfo?.SetParentInfo(this, PropertyIds.TEAM_BORN_INFO);
                        }
                    }
                    break;
                    case PropertyIds.RECRUITMENT_INFO:
                    {
                        _recruitmentInfo?.ClearParentInfo();
                        if (json.HasKey("RecruitmentInfo"))
                        {
                            _recruitmentInfo = CustomTypeFactory.ConstructCustomTypeByJson(json["RecruitmentInfo"]) as LobbyRecruitmentInfo;
                            _recruitmentInfo?.SetParentInfo(this, PropertyIds.RECRUITMENT_INFO);
                        }
                    }
                    break;
                    case PropertyIds.RECRUITMENT_APPLICATION:
                    {
                        if (_recruitmentApplication != null)
                        {
                            _recruitmentApplication.ClearParentInfo();
                            _recruitmentApplication.ResetWhenDeserialize();
                        }
                        if (json.HasKey("RecruitmentApplication"))
                        {
                            _recruitmentApplication = new(json["RecruitmentApplication"]);
                            _recruitmentApplication.SetParentInfo(this, PropertyIds.RECRUITMENT_APPLICATION);
                        }
                    }
                    break;
                    case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                        if (json.HasKey("IsRemainedWhenNoMember"))
                        {
                            _isRemainedWhenNoMember = json["IsRemainedWhenNoMember"].AsBool;
                        }
                        break;
                    case PropertyIds.IMPEACH_TIMER_ID:
                        if (json.HasKey("ImpeachTimerId"))
                        {
                            if(json["ImpeachTimerId"].Tag == JSONNodeType.String && json["ImpeachTimerId"].Value.StartsWith("NumberLong"))
                                _impeachTimerId = long.Parse(json["ImpeachTimerId"].Value.Substring(11, json["ImpeachTimerId"].Value.Length - 12));
                            else
                                _impeachTimerId = json["ImpeachTimerId"].AsLong;
                        }
                        break;
                    case PropertyIds.IMPEACH_STATE:
                        if (json.HasKey("ImpeachState"))
                        {
                            _impeachState = json["ImpeachState"].AsInt;
                        }
                        break;
                    case PropertyIds.APPLICATION_PLAYER:
                        if (json.HasKey("ApplicationPlayer"))
                        {
                            _applicationPlayer = json["ApplicationPlayer"].AsULong;
                        }
                        break;
                    case PropertyIds.VOTE_TIMER_ID:
                        if (json.HasKey("VoteTimerId"))
                        {
                            if(json["VoteTimerId"].Tag == JSONNodeType.String && json["VoteTimerId"].Value.StartsWith("NumberLong"))
                                _voteTimerId = long.Parse(json["VoteTimerId"].Value.Substring(11, json["VoteTimerId"].Value.Length - 12));
                            else
                                _voteTimerId = json["VoteTimerId"].AsLong;
                        }
                        break;
                    case PropertyIds.VOTE_ROLES:
                    {
                        if (_voteRoles != null)
                        {
                            _voteRoles.ClearParentInfo();
                            _voteRoles.ResetWhenDeserialize();
                        }
                        if (json.HasKey("VoteRoles"))
                        {
                            _voteRoles = new(json["VoteRoles"]);
                            _voteRoles.SetParentInfo(this, PropertyIds.VOTE_ROLES);
                        }
                    }
                    break;
                    case PropertyIds.TERRITORY_TIMER_ID:
                        if (json.HasKey("TerritoryTimerId"))
                        {
                            if(json["TerritoryTimerId"].Tag == JSONNodeType.String && json["TerritoryTimerId"].Value.StartsWith("NumberLong"))
                                _territoryTimerId = long.Parse(json["TerritoryTimerId"].Value.Substring(11, json["TerritoryTimerId"].Value.Length - 12));
                            else
                                _territoryTimerId = json["TerritoryTimerId"].AsLong;
                        }
                        break;
                    case PropertyIds.INVITE_OTHER_COUNT:
                        if (json.HasKey("InviteOtherCount"))
                        {
                            _inviteOtherCount = json["InviteOtherCount"].AsInt;
                        }
                        break;
                    case PropertyIds.CAN_SHARE_RECRUIT:
                        if (json.HasKey("CanShareRecruit"))
                        {
                            _canShareRecruit = json["CanShareRecruit"].AsBool;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.PROPERTY_EXTEND:
                    return DiffToStringPropertyExtend(ref reader, path, pathLen, pathString, depth);
                case PropertyIds.CAPTAIN_ROLE_ID:
                    return $"{pathString}.CaptainRoleId Set to {reader.ReadUInt64()}";
                case PropertyIds.MEMBER_DIC:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new CustomValueDictionary<ulong, TeamMemberInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.MemberDic Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".MemberDic");
                        return MemberDic.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.TEAM_REPUTATION_INFO:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<ReputationTeamInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.TeamReputationInfo Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".TeamReputationInfo");
                        return TeamReputationInfo.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.TEAM_BORN_INFO:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<TeamBornInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.TeamBornInfo Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".TeamBornInfo");
                        return TeamBornInfo.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.RECRUITMENT_INFO:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<LobbyRecruitmentInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.RecruitmentInfo Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".RecruitmentInfo");
                        return RecruitmentInfo.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.RECRUITMENT_APPLICATION:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new CustomValueDictionary<ulong, TeamRecruitmentApplicationInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.RecruitmentApplication Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".RecruitmentApplication");
                        return RecruitmentApplication.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                    return $"{pathString}.IsRemainedWhenNoMember Set to {reader.ReadBoolean()}";
                case PropertyIds.IMPEACH_TIMER_ID:
                    return $"{pathString}.ImpeachTimerId Set to {reader.ReadInt64()}";
                case PropertyIds.IMPEACH_STATE:
                    return $"{pathString}.ImpeachState Set to {reader.ReadInt32()}";
                case PropertyIds.APPLICATION_PLAYER:
                    return $"{pathString}.ApplicationPlayer Set to {reader.ReadUInt64()}";
                case PropertyIds.VOTE_TIMER_ID:
                    return $"{pathString}.VoteTimerId Set to {reader.ReadInt64()}";
                case PropertyIds.VOTE_ROLES:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicValueDictionary<ulong, bool>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.VoteRoles Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".VoteRoles");
                        return VoteRoles.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.TERRITORY_TIMER_ID:
                    return $"{pathString}.TerritoryTimerId Set to {reader.ReadInt64()}";
                case PropertyIds.INVITE_OTHER_COUNT:
                    return $"{pathString}.InviteOtherCount Set to {reader.ReadInt32()}";
                case PropertyIds.CAN_SHARE_RECRUIT:
                    return $"{pathString}.CanShareRecruit Set to {reader.ReadBoolean()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("CaptainRoleId", DebugDetailHelper.ToDetailObject(this.CaptainRoleId));
            output.Add("MemberDic", DebugDetailHelper.ToDetailObject(this.MemberDic));
            output.Add("TeamReputationInfo", DebugDetailHelper.ToDetailObject(this.TeamReputationInfo));
            output.Add("TeamBornInfo", DebugDetailHelper.ToDetailObject(this.TeamBornInfo));
            output.Add("RecruitmentInfo", DebugDetailHelper.ToDetailObject(this.RecruitmentInfo));
            output.Add("RecruitmentApplication", DebugDetailHelper.ToDetailObject(this.RecruitmentApplication));
            output.Add("IsRemainedWhenNoMember", DebugDetailHelper.ToDetailObject(this.IsRemainedWhenNoMember));
            output.Add("ImpeachTimerId", DebugDetailHelper.ToDetailObject(this.ImpeachTimerId));
            output.Add("ImpeachState", DebugDetailHelper.ToDetailObject(this.ImpeachState));
            output.Add("ApplicationPlayer", DebugDetailHelper.ToDetailObject(this.ApplicationPlayer));
            output.Add("VoteTimerId", DebugDetailHelper.ToDetailObject(this.VoteTimerId));
            output.Add("VoteRoles", DebugDetailHelper.ToDetailObject(this.VoteRoles));
            output.Add("TerritoryTimerId", DebugDetailHelper.ToDetailObject(this.TerritoryTimerId));
            output.Add("InviteOtherCount", DebugDetailHelper.ToDetailObject(this.InviteOtherCount));
            output.Add("CanShareRecruit", DebugDetailHelper.ToDetailObject(this.CanShareRecruit));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.PROPERTY_EXTEND:
                    propertyName = "PropertyExtend";
                    propertyBase = propertyExtend;
                    break;
                case PropertyIds.CAPTAIN_ROLE_ID:
                    propertyName = "CaptainRoleId";
                    propertyValue = _captainRoleId.ToString();
                    propertyValueIsDefault = _captainRoleId == default;
                    break;
                case PropertyIds.MEMBER_DIC:
                    propertyName = "MemberDic";
                    propertyBase = _memberDic;
                    break;
                case PropertyIds.TEAM_REPUTATION_INFO:
                    propertyName = "TeamReputationInfo";
                    propertyBase = _teamReputationInfo;
                    break;
                case PropertyIds.TEAM_BORN_INFO:
                    propertyName = "TeamBornInfo";
                    propertyBase = _teamBornInfo;
                    break;
                case PropertyIds.RECRUITMENT_INFO:
                    propertyName = "RecruitmentInfo";
                    propertyBase = _recruitmentInfo;
                    break;
                case PropertyIds.RECRUITMENT_APPLICATION:
                    propertyName = "RecruitmentApplication";
                    propertyBase = _recruitmentApplication;
                    break;
                case PropertyIds.IS_REMAINED_WHEN_NO_MEMBER:
                    propertyName = "IsRemainedWhenNoMember";
                    propertyValue = _isRemainedWhenNoMember.ToString();
                    propertyValueIsDefault = _isRemainedWhenNoMember == default;
                    break;
                case PropertyIds.IMPEACH_TIMER_ID:
                    propertyName = "ImpeachTimerId";
                    propertyValue = _impeachTimerId.ToString();
                    propertyValueIsDefault = _impeachTimerId == default;
                    break;
                case PropertyIds.IMPEACH_STATE:
                    propertyName = "ImpeachState";
                    propertyValue = _impeachState.ToString();
                    propertyValueIsDefault = _impeachState == default;
                    break;
                case PropertyIds.APPLICATION_PLAYER:
                    propertyName = "ApplicationPlayer";
                    propertyValue = _applicationPlayer.ToString();
                    propertyValueIsDefault = _applicationPlayer == default;
                    break;
                case PropertyIds.VOTE_TIMER_ID:
                    propertyName = "VoteTimerId";
                    propertyValue = _voteTimerId.ToString();
                    propertyValueIsDefault = _voteTimerId == default;
                    break;
                case PropertyIds.VOTE_ROLES:
                    propertyName = "VoteRoles";
                    propertyBase = _voteRoles;
                    break;
                case PropertyIds.TERRITORY_TIMER_ID:
                    propertyName = "TerritoryTimerId";
                    propertyValue = _territoryTimerId.ToString();
                    propertyValueIsDefault = _territoryTimerId == default;
                    break;
                case PropertyIds.INVITE_OTHER_COUNT:
                    propertyName = "InviteOtherCount";
                    propertyValue = _inviteOtherCount.ToString();
                    propertyValueIsDefault = _inviteOtherCount == default;
                    break;
                case PropertyIds.CAN_SHARE_RECRUIT:
                    propertyName = "CanShareRecruit";
                    propertyValue = _canShareRecruit.ToString();
                    propertyValueIsDefault = _canShareRecruit == default;
                    break;
                default: break;
            }
        }
    }
}