using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Construction;
namespace WizardGames.Soc.Common.Component
{
    public partial class ElectricSolarPanelComponent
    {
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public new const int CLASS_HASH = 1139234185;
#else
        public new const int CLASS_HASH = 1139234185;
#endif
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public new const string TYPE_NAME = "ElectricSolarPanelComponent";
        public override string GetTypeName() => TYPE_NAME;
        public override int Id => (int)EComponentIdEnum.ElectricBase;
        public ElectricSolarPanelComponent() { }
        public ElectricSolarPanelComponent(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public ElectricSolarPanelComponent(bool useless, JSONNode json) : base(useless, json) { }
        public override ESyncRange SyncRange{ get; } = ESyncRange.All;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ELECTRIC_C = 0;
            public const int IS_NOT_VISIABLE = 1;
        }
#pragma warning restore CS0108
        public static new readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ELECTRIC_C, PropertyIds.IS_NOT_VISIABLE
        };
        public static new readonly int[] PropertyTypeArray = new int[]
        {
            12, 6
        };
        public static new readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.ELECTRIC_C
        };
        public static new readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.ELECTRIC_C
        };
        public static new readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.ELECTRIC_C
        };
        public static new readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.ELECTRIC_C
        };
        public static new readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.CUSTOM_TYPE << 16) | PropertyIds.ELECTRIC_C
        };
        public static new readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.ELECTRIC_C, PropertyIds.IS_NOT_VISIABLE
        };
        public static new readonly int[] CustomHashValueArray = new int[]
        {
            2135046969, 0
        };
        public static new readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.UnityDs
        };
        public static new readonly bool[] SyncDelayArray = new bool[]
        {
            false, false
        };
        public static new readonly int[] LodArray = new int[]
        {
            9, 0
        };
        public static new readonly string[] PropNameArray = new string[]
        {
            "ElectricC", "IsNotVisiable"
        };
        public static new readonly int[] PropId2Index = new int[]
        {
            0 + 10000, 0
        };
        public static new readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.ELECTRIC_C
        };
        public new const int VALUE_TYPE_COUNT = 1;
        public new const int REF_TYPE_COUNT = 1;
        public new const int AVAILABLE_LODS = 9;
        public new const int CLASS_LOD = 9;
        public bool IsNotVisiable
        {
            get => _isNotVisiable;
            set
            {
                if (_isNotVisiable == value) return;
                var oldValue = _isNotVisiable;
                _isNotVisiable = value;
                TransactionAssignBackup(PropertyIds.IS_NOT_VISIABLE, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.IS_NOT_VISIABLE, value)) return;
                TryInvokeFieldChange(PropertyIds.IS_NOT_VISIABLE, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.ELECTRIC_C:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _electricC?.ClearParentInfo();
                            ElectricCBase newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<ElectricCBase>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 2135046969, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.ELECTRIC_C, newValue);
                            var oldValue = _electricC;
                            _electricC = newValue;
                            _electricC?.SetParentInfo(this, PropertyIds.ELECTRIC_C);
                            TryInvokeFieldChange(0, oldValue, _electricC);
                            return this;
                        }
                        else
                        {
                            return _electricC.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.IS_NOT_VISIABLE:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.BOOL) throw new Exception($"Type mismatch, expecting bool got {seg.BasicType.Type}");
#endif
                    bool newValue = seg.BasicType.BoolValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.IS_NOT_VISIABLE, newValue);
                    var oldValue = _isNotVisiable;
                    _isNotVisiable = newValue;
                    TryInvokeFieldChange(1, oldValue, _isNotVisiable);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.ELECTRIC_C:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _electricC = null;
                        }
                        else
                        {
                            _electricC = (ElectricCBase)CustomTypeHelper.DeserializeCustomTypeBase<ElectricCBase>(ref reader, ESerializeMode.Db);
                            _electricC?.SetParentInfo(this, PropertyIds.ELECTRIC_C);
                        }
                    }
                    else
                    {
                        return _electricC.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.ELECTRIC_C:
                {
                    _electricC = valueUnion.CustomTypeValue as ElectricCBase;
                    break;
                }
                case PropertyIds.IS_NOT_VISIABLE:
                {
                    _isNotVisiable = valueUnion.SimpleValue.BoolValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.ELECTRIC_C:
                    {
                        _electricC?.ClearParentInfo();
                        _electricC = CustomTypeHelper.DeserializeCustomTypeBase<ElectricCBase>(ref reader, mode);
                        _electricC?.SetParentInfo(this, PropertyIds.ELECTRIC_C);
                    }
                    break;
                case PropertyIds.IS_NOT_VISIABLE:
                    _isNotVisiable = reader.ReadBoolean();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.ELECTRIC_C:
                        if (_electricC == null)
                            writer.WriteNil();
                        else
                            _electricC.SerializeCore(ref writer, mode, 2135046969);
                        break;
                    case PropertyIds.IS_NOT_VISIABLE:
                        writer.Write(_isNotVisiable);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.ELECTRIC_C:
                        if (ElectricC != null) jsonObj["ElectricC"] = ElectricC.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ELECTRIC_C:
                    {
                        _electricC?.ClearParentInfo();
                        if (json.HasKey("ElectricC"))
                        {
                            _electricC = CustomTypeFactory.ConstructCustomTypeByJson(json["ElectricC"]) as ElectricCBase;
                            _electricC?.SetParentInfo(this, PropertyIds.ELECTRIC_C);
                        }
                    }
                    break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.ELECTRIC_C:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<ElectricCBase>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.ElectricC Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".ElectricC");
                        return ElectricC.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.IS_NOT_VISIABLE:
                    return $"{pathString}.IsNotVisiable Set to {reader.ReadBoolean()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("ElectricC", DebugDetailHelper.ToDetailObject(this.ElectricC));
            output.Add("IsNotVisiable", DebugDetailHelper.ToDetailObject(this.IsNotVisiable));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.ELECTRIC_C:
                    propertyName = "ElectricC";
                    propertyBase = _electricC;
                    break;
                case PropertyIds.IS_NOT_VISIABLE:
                    propertyName = "IsNotVisiable";
                    propertyValue = _isNotVisiable.ToString();
                    propertyValueIsDefault = _isNotVisiable == default;
                    break;
                default: break;
            }
        }
        public override JSONNode ConstructionExportedToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.ELECTRIC_C:
                        if (ElectricC != null)
                        {
                            if (ElectricC is IConstructionExport iexport)
                            {
                                jsonObj["ElectricC"] = iexport.ConstructionExportedToEJson();
                            }
                            else
                            {
                                jsonObj["ElectricC"] = ElectricC.ToEJson();
                            }
                        }
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
    }
}