using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Common.Component
{
    public partial class OvenComponent
    {
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public new const int CLASS_HASH = 1845104910;
#else
        public new const int CLASS_HASH = 1845104910;
#endif
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public new const string TYPE_NAME = "OvenComponent";
        public override string GetTypeName() => TYPE_NAME;
        public override int Id => (int)EComponentIdEnum.Oven;
        public OvenComponent() { }
        public OvenComponent(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public OvenComponent(bool useless, JSONNode json) : base(useless, json) { }
        public override ESyncRange SyncRange{ get; } = ESyncRange.Clients;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ROOT = 0;
        }
#pragma warning restore CS0108
        public static new readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ROOT
        };
        public static new readonly int[] PropertyTypeArray = new int[]
        {
            12
        };
        public static new readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.ROOT
        };
        public static new readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.ROOT
        };
        public static new readonly int[] OwnClientPropertyInfoArray = new int[]
        {
        };
        public static new readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.ROOT
        };
        public static new readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.CUSTOM_TYPE << 16) | PropertyIds.ROOT
        };
        public static new readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.ROOT
        };
        public static new readonly int[] CustomHashValueArray = new int[]
        {
            997205420
        };
        public static new readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.OtherAndUnityDs
        };
        public static new readonly bool[] SyncDelayArray = new bool[]
        {
            false
        };
        public static new readonly int[] LodArray = new int[]
        {
            1
        };
        public static new readonly string[] PropNameArray = new string[]
        {
            "Root"
        };
        public static new readonly int[] PropId2Index = new int[]
        {
            0 + 10000
        };
        public static new readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.ROOT
        };
        public new const int VALUE_TYPE_COUNT = 0;
        public new const int REF_TYPE_COUNT = 1;
        public new const int AVAILABLE_LODS = 1;
        public new const int CLASS_LOD = 1;
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.ROOT:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _root?.ClearParentInfo();
                            ItemSystemRootNode newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<ItemSystemRootNode>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 997205420, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.ROOT, newValue);
                            var oldValue = _root;
                            _root = newValue;
                            _root?.SetParentInfo(this, PropertyIds.ROOT);
                            TryInvokeFieldChange(0, oldValue, _root);
                            return this;
                        }
                        else
                        {
                            return _root.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.ROOT:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _root = null;
                        }
                        else
                        {
                            _root = (ItemSystemRootNode)CustomTypeHelper.DeserializeCustomTypeBase<ItemSystemRootNode>(ref reader, ESerializeMode.Db);
                            _root?.SetParentInfo(this, PropertyIds.ROOT);
                        }
                    }
                    else
                    {
                        return _root.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.ROOT:
                {
                    _root = valueUnion.CustomTypeValue as ItemSystemRootNode;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.ROOT:
                    {
                        _root?.ClearParentInfo();
                        _root = CustomTypeHelper.DeserializeCustomTypeBase<ItemSystemRootNode>(ref reader, mode);
                        _root?.SetParentInfo(this, PropertyIds.ROOT);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.ROOT:
                        if (_root == null)
                            writer.WriteNil();
                        else
                            _root.SerializeCore(ref writer, mode, 997205420);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.ROOT:
                        if (Root != null) jsonObj["Root"] = Root.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ROOT:
                    {
                        _root?.ClearParentInfo();
                        if (json.HasKey("Root"))
                        {
                            _root = CustomTypeFactory.ConstructCustomTypeByJson(json["Root"]) as ItemSystemRootNode;
                            _root?.SetParentInfo(this, PropertyIds.ROOT);
                        }
                    }
                    break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.ROOT:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<ItemSystemRootNode>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.Root Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".Root");
                        return Root.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("Root", DebugDetailHelper.ToDetailObject(this.Root));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.ROOT:
                    propertyName = "Root";
                    propertyBase = _root;
                    break;
                default: break;
            }
        }
    }
}