using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Common.Component
{
    public partial class ConstructionMoveComponent
    {
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 1680943661;
#else
        public const int CLASS_HASH = 1680943661;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public const string TYPE_NAME = "ConstructionMoveComponent";
        public override string GetTypeName() => TYPE_NAME;
        public override int Id => (int)EComponentIdEnum.ConstructionMove;
        public ConstructionMoveComponent() { }
        public ConstructionMoveComponent(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public ConstructionMoveComponent(bool useless, JSONNode json) : base(useless, json) { }
        public override ESyncRange SyncRange{ get; } = ESyncRange.All;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int LAST_MOVE_TIME = 0;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.LAST_MOVE_TIME
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            2
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.LAST_MOVE_TIME
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.LAST_MOVE_TIME
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.LocalOnly
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false
        };
        public static readonly int[] LodArray = new int[]
        {
            0
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "LastMoveTime"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
        };
        public const int VALUE_TYPE_COUNT = 1;
        public const int REF_TYPE_COUNT = 0;
        public const int AVAILABLE_LODS = 5;
        public const int CLASS_LOD = 5;
        public long LastMoveTime
        {
            get => _lastMoveTime;
            set
            {
                if (_lastMoveTime == value) return;
                var oldValue = _lastMoveTime;
                _lastMoveTime = value;
                TransactionAssignBackup(PropertyIds.LAST_MOVE_TIME, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.LAST_MOVE_TIME, value)) return;
                TryInvokeFieldChange(PropertyIds.LAST_MOVE_TIME, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.LAST_MOVE_TIME:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.LAST_MOVE_TIME, newValue);
                    var oldValue = _lastMoveTime;
                    _lastMoveTime = newValue;
                    TryInvokeFieldChange(0, oldValue, _lastMoveTime);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.LAST_MOVE_TIME:
                {
                    _lastMoveTime = reader.ReadInt64();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.LAST_MOVE_TIME:
                {
                    _lastMoveTime = valueUnion.SimpleValue.LongValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.LAST_MOVE_TIME:
                    _lastMoveTime = reader.ReadInt64();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.LAST_MOVE_TIME:
                        writer.Write(_lastMoveTime);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.LAST_MOVE_TIME:
                        jsonObj["LastMoveTime"] = LastMoveTime;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.LAST_MOVE_TIME:
                        if (json.HasKey("LastMoveTime"))
                        {
                            if(json["LastMoveTime"].Tag == JSONNodeType.String && json["LastMoveTime"].Value.StartsWith("NumberLong"))
                                _lastMoveTime = long.Parse(json["LastMoveTime"].Value.Substring(11, json["LastMoveTime"].Value.Length - 12));
                            else
                                _lastMoveTime = json["LastMoveTime"].AsLong;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.LAST_MOVE_TIME:
                    return $"{pathString}.LastMoveTime Set to {reader.ReadInt64()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("LastMoveTime", DebugDetailHelper.ToDetailObject(this.LastMoveTime));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.LAST_MOVE_TIME:
                    propertyName = "LastMoveTime";
                    propertyValue = _lastMoveTime.ToString();
                    propertyValueIsDefault = _lastMoveTime == default;
                    break;
                default: break;
            }
        }
    }
}