using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Share.Framework
{
    public static partial class ContainerDecoder
    {
        public static object DeserializeDictionaryLongTableFloatArrayFieldInfoContainer(ref MessagePackReader reader, int propertyId, ESerializeMode mode)
        {
            switch (propertyId)
            {
                case DictionaryLongTableFloatArrayFieldInfo.PropertyIds.CONTAINER:
                    {
                        return DictOfArrayDataSet.GetFromPool(ref reader, mode, 1294126622);
                    }
                default: return null;
            }
        }
    }
    public static partial class ShadowContainerDecoder
    {
        public static SyncContainerBase DeserializeDictionaryLongTableFloatArrayFieldInfoContainer(ref MessagePackReader reader, int propertyId, long entityId)
        {
            switch (propertyId)
            {
                case DictionaryLongTableFloatArrayFieldInfo.PropertyIds.CONTAINER:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncDictionary.GetFromPool(ref reader, 1294126622, entityId);
                    }
                default: return null;
            }
        }
    }
}
namespace WizardGames.Soc.Common.CustomType
{
    public partial class DictionaryLongTableFloatArrayFieldInfo
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(DictionaryLongTableFloatArrayFieldInfo));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 883020931;
#else
        public const int CLASS_HASH = 883020931;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public DictionaryLongTableFloatArrayFieldInfo() { }
        public DictionaryLongTableFloatArrayFieldInfo(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public DictionaryLongTableFloatArrayFieldInfo(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int CONTAINER = 0;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.CONTAINER
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            13
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.CONTAINER
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.CONTAINER
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.CONTAINER
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.CONTAINER
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.CONTAINER << 16) | PropertyIds.CONTAINER
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.CONTAINER
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false
        };
        public static readonly int[] LodArray = new int[]
        {
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "Container"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0 + 10000
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.CONTAINER
        };
        public const int VALUE_TYPE_COUNT = 0;
        public const int REF_TYPE_COUNT = 1;
        public CustomValueDictionary<long, TableFloatArrayFieldInfo> Container
        {
            get => _container;
            set
            {
                if (_container == value) return;
                var oldValue = _container;
                _container?.ClearParentInfo();
                _container = value;
                value?.SetParentInfo(this, PropertyIds.CONTAINER);
                TransactionAssignBackup(PropertyIds.CONTAINER, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 1294126622};
                if (!TriggerPropertyChange(ref ctx, PropertyIds.CONTAINER, value)) return;
                TryInvokeFieldChange(PropertyIds.CONTAINER, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.CONTAINER:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _container?.ClearParentInfo();
                            _container?.ResetWhenDeserialize();
                            CustomValueDictionary<long, TableFloatArrayFieldInfo> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 1294126622, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                            var oldValue = _container;
                            _container = newValue;
                            _container?.SetParentInfo(this, PropertyIds.CONTAINER);
                            TryInvokeFieldChange(0, oldValue, _container);
                            return this;
                        }
                        else
                        {
                            return _container.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.CONTAINER:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _container = null;
                        }
                        else
                        {
                            _container = new CustomValueDictionary<long, TableFloatArrayFieldInfo>(ref reader, ESerializeMode.Db);
                            _container?.SetParentInfo(this, PropertyIds.CONTAINER);
                        }
                    }
                    else
                    {
                        return _container.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.CONTAINER:
                {
                    _container = valueUnion.CustomTypeValue as CustomValueDictionary<long, TableFloatArrayFieldInfo>;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.CONTAINER:
                    {
                        if (_container != null)
                        {
                            _container.ClearParentInfo();
                            _container.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _container = null;
                            continue;
                        }
                        _container = new(ref reader, mode);
                        _container.SetParentInfo(this, PropertyIds.CONTAINER);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.CONTAINER:
                        if (_container == null)
                            writer.WriteNil();
                        else
                            _container.SerializeCore(ref writer, mode, 1294126622);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.CONTAINER:
                        if (Container != null) jsonObj["Container"] = Container.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.CONTAINER:
                    {
                        if (_container != null)
                        {
                            _container.ClearParentInfo();
                            _container.ResetWhenDeserialize();
                        }
                        if (json.HasKey("Container"))
                        {
                            _container = new(json["Container"]);
                            _container.SetParentInfo(this, PropertyIds.CONTAINER);
                        }
                    }
                    break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.CONTAINER:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new CustomValueDictionary<long, TableFloatArrayFieldInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.Container Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".Container");
                        return Container.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("Container", DebugDetailHelper.ToDetailObject(this.Container));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.CONTAINER:
                    propertyName = "Container";
                    propertyBase = _container;
                    break;
                default: break;
            }
        }
    }
}