using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Common.CustomType
{
    public partial class SendInviteInfo
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(SendInviteInfo));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 1872188351;
#else
        public const int CLASS_HASH = 1872188351;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public SendInviteInfo(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public SendInviteInfo(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int INVITE_TIME = 0;
            public const int TIMER_ID = 1;
            public const int INVITE_SOURCE = 2;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.INVITE_TIME, PropertyIds.TIMER_ID, PropertyIds.INVITE_SOURCE
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            2, 2, 5
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.INVITE_TIME, PropertyIds.TIMER_ID, PropertyIds.INVITE_SOURCE
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.INVITE_TIME, PropertyIds.TIMER_ID, PropertyIds.INVITE_SOURCE
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.INVITE_TIME, PropertyIds.TIMER_ID
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.INVITE_TIME, PropertyIds.TIMER_ID
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.LONG << 16) | PropertyIds.INVITE_TIME, (TypeDefine.LONG << 16) | PropertyIds.TIMER_ID
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.INVITE_TIME, PropertyIds.TIMER_ID
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0, 0, 0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.All, ESyncRange.LocalOnly
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false
        };
        public static readonly int[] LodArray = new int[]
        {
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "InviteTime", "TimerId", "InviteSource"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0, 1, 0 + 10000
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.INVITE_SOURCE
        };
        public const int VALUE_TYPE_COUNT = 2;
        public const int REF_TYPE_COUNT = 1;
        /// <summary>
        /// 发出邀请的时间戳
        /// </summary>
        public long InviteTime
        {
            get => _inviteTime;
            set
            {
                if (_inviteTime == value) return;
                var oldValue = _inviteTime;
                _inviteTime = value;
                TransactionAssignBackup(PropertyIds.INVITE_TIME, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.INVITE_TIME, value)) return;
                TryInvokeFieldChange(PropertyIds.INVITE_TIME, oldValue, value);
            }
        }
        /// <summary>
        /// 发出邀请的时间戳
        /// </summary>
        public long TimerId
        {
            get => _timerId;
            set
            {
                if (_timerId == value) return;
                var oldValue = _timerId;
                _timerId = value;
                TransactionAssignBackup(PropertyIds.TIMER_ID, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.TIMER_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.TIMER_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 邀请来源
        /// </summary>
        public string InviteSource
        {
            get => _inviteSource;
            set
            {
                if (_inviteSource == value) return;
                var oldValue = _inviteSource;
                _inviteSource = value;
                TransactionAssignBackup(PropertyIds.INVITE_SOURCE, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.INVITE_SOURCE, value)) return;
                TryInvokeFieldChange(PropertyIds.INVITE_SOURCE, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.INVITE_TIME:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _inviteTime;
                    _inviteTime = newValue;
                    TryInvokeFieldChange(0, oldValue, _inviteTime);
                    return this;
                }
                case PropertyIds.TIMER_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _timerId;
                    _timerId = newValue;
                    TryInvokeFieldChange(1, oldValue, _timerId);
                    return this;
                }
                case PropertyIds.INVITE_SOURCE:
                {
                    string newValue;
                    if (seg.BasicType.Type == TypeDefine.NULL)
                    {
                        newValue = null;
                    }
                    else
                    {
                        var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                        newValue = reader.ReadString();
                    }
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _inviteSource;
                    _inviteSource = newValue;
                    TryInvokeFieldChange(2, oldValue, _inviteSource);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.INVITE_TIME:
                {
                    _inviteTime = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.TIMER_ID:
                {
                    _timerId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.INVITE_SOURCE:
                {
                    _inviteSource = reader.ReadString();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.INVITE_TIME:
                {
                    _inviteTime = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.TIMER_ID:
                {
                    _timerId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.INVITE_SOURCE:
                {
                    _inviteSource = valueUnion.StringValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.INVITE_TIME:
                    _inviteTime = reader.ReadInt64();
                    break;
                case PropertyIds.TIMER_ID:
                    _timerId = reader.ReadInt64();
                    break;
                case PropertyIds.INVITE_SOURCE:
                    _inviteSource = reader.ReadString();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.INVITE_TIME:
                        writer.Write(_inviteTime);
                        break;
                    case PropertyIds.TIMER_ID:
                        writer.Write(_timerId);
                        break;
                    case PropertyIds.INVITE_SOURCE:
                        writer.Write(_inviteSource);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.INVITE_TIME:
                        jsonObj["InviteTime"] = InviteTime;
                        break;
                    case PropertyIds.TIMER_ID:
                        jsonObj["TimerId"] = TimerId;
                        break;
                    case PropertyIds.INVITE_SOURCE:
                        jsonObj["InviteSource"] = InviteSource;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.INVITE_TIME:
                        if (json.HasKey("InviteTime"))
                        {
                            if(json["InviteTime"].Tag == JSONNodeType.String && json["InviteTime"].Value.StartsWith("NumberLong"))
                                _inviteTime = long.Parse(json["InviteTime"].Value.Substring(11, json["InviteTime"].Value.Length - 12));
                            else
                                _inviteTime = json["InviteTime"].AsLong;
                        }
                        break;
                    case PropertyIds.TIMER_ID:
                        if (json.HasKey("TimerId"))
                        {
                            if(json["TimerId"].Tag == JSONNodeType.String && json["TimerId"].Value.StartsWith("NumberLong"))
                                _timerId = long.Parse(json["TimerId"].Value.Substring(11, json["TimerId"].Value.Length - 12));
                            else
                                _timerId = json["TimerId"].AsLong;
                        }
                        break;
                    case PropertyIds.INVITE_SOURCE:
                        if (json.HasKey("InviteSource"))
                        {
                            _inviteSource = json["InviteSource"];
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.INVITE_TIME:
                    return $"{pathString}.InviteTime Set to {reader.ReadInt64()}";
                case PropertyIds.TIMER_ID:
                    return $"{pathString}.TimerId Set to {reader.ReadInt64()}";
                case PropertyIds.INVITE_SOURCE:
                    return $"{pathString}.InviteSource Set to {reader.ReadString()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("InviteTime", DebugDetailHelper.ToDetailObject(this.InviteTime));
            output.Add("TimerId", DebugDetailHelper.ToDetailObject(this.TimerId));
            output.Add("InviteSource", DebugDetailHelper.ToDetailObject(this.InviteSource));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.INVITE_TIME:
                    propertyName = "InviteTime";
                    propertyValue = _inviteTime.ToString();
                    propertyValueIsDefault = _inviteTime == default;
                    break;
                case PropertyIds.TIMER_ID:
                    propertyName = "TimerId";
                    propertyValue = _timerId.ToString();
                    propertyValueIsDefault = _timerId == default;
                    break;
                case PropertyIds.INVITE_SOURCE:
                    propertyName = "InviteSource";
                    propertyValue = _inviteSource?.ToString();
                    propertyValueIsDefault = _inviteSource == default;
                    break;
                default: break;
            }
        }
    }
}