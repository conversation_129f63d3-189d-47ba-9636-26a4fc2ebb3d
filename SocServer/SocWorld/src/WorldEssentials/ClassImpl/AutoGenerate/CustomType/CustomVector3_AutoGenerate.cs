using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.Common.Construction;
namespace WizardGames.Soc.Common.CustomType
{
    public partial class CustomVector3
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(CustomVector3));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 1008441098;
#else
        public const int CLASS_HASH = 1008441098;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public CustomVector3(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public CustomVector3(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int X = 0;
            public const int Y = 1;
            public const int Z = 2;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.X, PropertyIds.Y, PropertyIds.Z
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            3, 3, 3
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.X, PropertyIds.Y, PropertyIds.Z
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.X, PropertyIds.Y, PropertyIds.Z
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.X, PropertyIds.Y, PropertyIds.Z
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.X, PropertyIds.Y, PropertyIds.Z
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.FLOAT << 16) | PropertyIds.X, (TypeDefine.FLOAT << 16) | PropertyIds.Y, (TypeDefine.FLOAT << 16) | PropertyIds.Z
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.X, PropertyIds.Y, PropertyIds.Z
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0, 0, 0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.All, ESyncRange.All
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false
        };
        public static readonly int[] LodArray = new int[]
        {
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "X", "Y", "Z"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0, 1, 2
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
        };
        public const int VALUE_TYPE_COUNT = 3;
        public const int REF_TYPE_COUNT = 0;
        public float X
        {
            get => _x;
            set
            {
                if (_x == value) return;
                var oldValue = _x;
                _x = value;
                TransactionAssignBackup(PropertyIds.X, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.X, value)) return;
                TryInvokeFieldChange(PropertyIds.X, oldValue, value);
            }
        }
        public float Y
        {
            get => _y;
            set
            {
                if (_y == value) return;
                var oldValue = _y;
                _y = value;
                TransactionAssignBackup(PropertyIds.Y, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.Y, value)) return;
                TryInvokeFieldChange(PropertyIds.Y, oldValue, value);
            }
        }
        public float Z
        {
            get => _z;
            set
            {
                if (_z == value) return;
                var oldValue = _z;
                _z = value;
                TransactionAssignBackup(PropertyIds.Z, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.Z, value)) return;
                TryInvokeFieldChange(PropertyIds.Z, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.X:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.FLOAT) throw new Exception($"Type mismatch, expecting float got {seg.BasicType.Type}");
#endif
                    float newValue = seg.BasicType.FloatValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _x;
                    _x = newValue;
                    TryInvokeFieldChange(0, oldValue, _x);
                    return this;
                }
                case PropertyIds.Y:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.FLOAT) throw new Exception($"Type mismatch, expecting float got {seg.BasicType.Type}");
#endif
                    float newValue = seg.BasicType.FloatValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _y;
                    _y = newValue;
                    TryInvokeFieldChange(1, oldValue, _y);
                    return this;
                }
                case PropertyIds.Z:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.FLOAT) throw new Exception($"Type mismatch, expecting float got {seg.BasicType.Type}");
#endif
                    float newValue = seg.BasicType.FloatValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _z;
                    _z = newValue;
                    TryInvokeFieldChange(2, oldValue, _z);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.X:
                {
                    _x = reader.ReadSingle();
                    return true;
                }
                case PropertyIds.Y:
                {
                    _y = reader.ReadSingle();
                    return true;
                }
                case PropertyIds.Z:
                {
                    _z = reader.ReadSingle();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.X:
                {
                    _x = valueUnion.SimpleValue.FloatValue;
                    break;
                }
                case PropertyIds.Y:
                {
                    _y = valueUnion.SimpleValue.FloatValue;
                    break;
                }
                case PropertyIds.Z:
                {
                    _z = valueUnion.SimpleValue.FloatValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.X:
                    _x = reader.ReadSingle();
                    break;
                case PropertyIds.Y:
                    _y = reader.ReadSingle();
                    break;
                case PropertyIds.Z:
                    _z = reader.ReadSingle();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.X:
                        writer.Write(_x);
                        break;
                    case PropertyIds.Y:
                        writer.Write(_y);
                        break;
                    case PropertyIds.Z:
                        writer.Write(_z);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.X:
                        jsonObj["X"] = X;
                        break;
                    case PropertyIds.Y:
                        jsonObj["Y"] = Y;
                        break;
                    case PropertyIds.Z:
                        jsonObj["Z"] = Z;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.X:
                        if (json.HasKey("X"))
                        {
                            _x = json["X"].AsFloat;
                        }
                        break;
                    case PropertyIds.Y:
                        if (json.HasKey("Y"))
                        {
                            _y = json["Y"].AsFloat;
                        }
                        break;
                    case PropertyIds.Z:
                        if (json.HasKey("Z"))
                        {
                            _z = json["Z"].AsFloat;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.X:
                    return $"{pathString}.X Set to {reader.ReadSingle()}";
                case PropertyIds.Y:
                    return $"{pathString}.Y Set to {reader.ReadSingle()}";
                case PropertyIds.Z:
                    return $"{pathString}.Z Set to {reader.ReadSingle()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("X", DebugDetailHelper.ToDetailObject(this.X));
            output.Add("Y", DebugDetailHelper.ToDetailObject(this.Y));
            output.Add("Z", DebugDetailHelper.ToDetailObject(this.Z));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.X:
                    propertyName = "X";
                    propertyValue = _x.ToString();
                    propertyValueIsDefault = _x == default;
                    break;
                case PropertyIds.Y:
                    propertyName = "Y";
                    propertyValue = _y.ToString();
                    propertyValueIsDefault = _y == default;
                    break;
                case PropertyIds.Z:
                    propertyName = "Z";
                    propertyValue = _z.ToString();
                    propertyValueIsDefault = _z == default;
                    break;
                default: break;
            }
        }
    }
}