using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Share.Framework
{
    public static partial class ContainerDecoder
    {
        public static object DeserializeBiologicalSampleTaskNodeContainer(ref MessagePackReader reader, int propertyId, ESerializeMode mode)
        {
            switch (propertyId)
            {
                case BiologicalSampleTaskNode.PropertyIds.CHILD_DICT:
                    {
                        return new BasicValueDictionary<long, long>(ref reader, mode);
                    }
                case BiologicalSampleTaskNode.PropertyIds.GUIDE_DATA:
                    {
                        return ListOfArrayDataSet.GetFromPool(ref reader, mode, 1485959689);
                    }
                default: return null;
            }
        }
    }
    public static partial class ShadowContainerDecoder
    {
        public static SyncContainerBase DeserializeBiologicalSampleTaskNodeContainer(ref MessagePackReader reader, int propertyId, long entityId)
        {
            switch (propertyId)
            {
                case BiologicalSampleTaskNode.PropertyIds.CHILD_DICT:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicValueDictionary<long, long>(ref reader, ESerializeMode.All));
                    }
                case BiologicalSampleTaskNode.PropertyIds.GUIDE_DATA:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncList.GetFromPool(ref reader, 1485959689, entityId);
                    }
                default: return null;
            }
        }
    }
}
namespace WizardGames.Soc.Common.CustomType
{
    public partial class BiologicalSampleTaskNode
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BiologicalSampleTaskNode));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public new const int CLASS_HASH = 2047439651;
#else
        public new const int CLASS_HASH = 2047439651;
#endif
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public BiologicalSampleTaskNode(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public BiologicalSampleTaskNode(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int BIZ_ID = 0;
            public const int ID = 1;
            public const int INDEX = 2;
            public const int PARENT_ID = 3;
            public const int CHILD_DICT = 4;
            public const int DEADLINE = 5;
            public const int CREATE_TIME_STAMP = 6;
            public const int TIMER_ID = 7;
            public const int TASK_ONLINE_WITHOUT_IDLE_TIME = 8;
            public const int GUIDE_DATA = 9;
            public const int SLOT_INDEX = 10;
        }
#pragma warning restore CS0108
        public static new readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CHILD_DICT, PropertyIds.DEADLINE, PropertyIds.CREATE_TIME_STAMP, PropertyIds.TIMER_ID, PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME, PropertyIds.GUIDE_DATA, PropertyIds.SLOT_INDEX
        };
        public static new readonly int[] PropertyTypeArray = new int[]
        {
            2, 2, 2, 2, 13, 2, 2, 2, 1, 13, 1
        };
        public static new readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CHILD_DICT, PropertyIds.DEADLINE, PropertyIds.CREATE_TIME_STAMP, PropertyIds.TIMER_ID, PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME, PropertyIds.GUIDE_DATA, PropertyIds.SLOT_INDEX
        };
        public static new readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CHILD_DICT, PropertyIds.DEADLINE, PropertyIds.CREATE_TIME_STAMP, PropertyIds.TIMER_ID, PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME, PropertyIds.GUIDE_DATA, PropertyIds.SLOT_INDEX
        };
        public static new readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CHILD_DICT, PropertyIds.DEADLINE, PropertyIds.CREATE_TIME_STAMP, PropertyIds.GUIDE_DATA, PropertyIds.SLOT_INDEX
        };
        public static new readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CHILD_DICT, PropertyIds.DEADLINE, PropertyIds.CREATE_TIME_STAMP
        };
        public static new readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.LONG << 16) | PropertyIds.BIZ_ID, (TypeDefine.LONG << 16) | PropertyIds.ID, (TypeDefine.LONG << 16) | PropertyIds.INDEX, (TypeDefine.LONG << 16) | PropertyIds.PARENT_ID, (TypeDefine.CONTAINER << 16) | PropertyIds.CHILD_DICT, (TypeDefine.LONG << 16) | PropertyIds.DEADLINE, (TypeDefine.LONG << 16) | PropertyIds.CREATE_TIME_STAMP, (TypeDefine.CONTAINER << 16) | PropertyIds.GUIDE_DATA, (TypeDefine.INT << 16) | PropertyIds.SLOT_INDEX
        };
        public static new readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CHILD_DICT
        };
        public static new readonly int[] CustomHashValueArray = new int[]
        {
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        };
        public static new readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.Clients, ESyncRange.Clients, ESyncRange.LocalOnly, ESyncRange.LocalOnly, ESyncRange.OwnClient, ESyncRange.OwnClient
        };
        public static new readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false, false, false, false, false, false, false, false, false
        };
        public static new readonly int[] LodArray = new int[]
        {
        };
        public static new readonly string[] PropNameArray = new string[]
        {
            "BizId", "Id", "Index", "ParentId", "ChildDict", "Deadline", "CreateTimeStamp", "TimerId", "TaskOnlineWithoutIdleTime", "GuideData", "SlotIndex"
        };
        public static new readonly int[] PropId2Index = new int[]
        {
            0, 1, 2, 3, 0 + 10000, 4, 5, 6, 7, 1 + 10000, 8
        };
        public static new readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.CHILD_DICT, PropertyIds.GUIDE_DATA
        };
        public new const int VALUE_TYPE_COUNT = 9;
        public new const int REF_TYPE_COUNT = 2;
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _bizId;
                    _bizId = newValue;
                    TryInvokeFieldChange(0, oldValue, _bizId);
                    return this;
                }
                case PropertyIds.ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _id;
                    _id = newValue;
                    TryInvokeFieldChange(1, oldValue, _id);
                    return this;
                }
                case PropertyIds.INDEX:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _index;
                    _index = newValue;
                    TryInvokeFieldChange(2, oldValue, _index);
                    return this;
                }
                case PropertyIds.PARENT_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _parentId;
                    _parentId = newValue;
                    TryInvokeFieldChange(3, oldValue, _parentId);
                    return this;
                }
                case PropertyIds.CHILD_DICT:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _childDict?.ClearParentInfo();
                            _childDict?.ResetWhenDeserialize();
                            BasicValueDictionary<long, long> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                            var oldValue = _childDict;
                            _childDict = newValue;
                            _childDict?.SetParentInfo(this, PropertyIds.CHILD_DICT);
                            TryInvokeFieldChange(4, oldValue, _childDict);
                            return this;
                        }
                        else
                        {
                            return _childDict.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.DEADLINE:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _deadline;
                    _deadline = newValue;
                    TryInvokeFieldChange(5, oldValue, _deadline);
                    return this;
                }
                case PropertyIds.CREATE_TIME_STAMP:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _createTimeStamp;
                    _createTimeStamp = newValue;
                    TryInvokeFieldChange(6, oldValue, _createTimeStamp);
                    return this;
                }
                case PropertyIds.TIMER_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _timerId;
                    _timerId = newValue;
                    TryInvokeFieldChange(7, oldValue, _timerId);
                    return this;
                }
                case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _taskOnlineWithoutIdleTime;
                    _taskOnlineWithoutIdleTime = newValue;
                    TryInvokeFieldChange(8, oldValue, _taskOnlineWithoutIdleTime);
                    return this;
                }
                case PropertyIds.GUIDE_DATA:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _guideData?.ClearParentInfo();
                            _guideData?.ResetWhenDeserialize();
                            CustomTypeList<TaskGuideData> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 1485959689, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                            var oldValue = _guideData;
                            _guideData = newValue;
                            _guideData?.SetParentInfo(this, PropertyIds.GUIDE_DATA);
                            TryInvokeFieldChange(9, oldValue, _guideData);
                            return this;
                        }
                        else
                        {
                            return _guideData.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.SLOT_INDEX:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _slotIndex;
                    _slotIndex = newValue;
                    TryInvokeFieldChange(10, oldValue, _slotIndex);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                {
                    _bizId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.ID:
                {
                    _id = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.INDEX:
                {
                    _index = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.PARENT_ID:
                {
                    _parentId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.CHILD_DICT:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _childDict = null;
                        }
                        else
                        {
                            _childDict = new BasicValueDictionary<long, long>(ref reader, ESerializeMode.Db);
                            _childDict?.SetParentInfo(this, PropertyIds.CHILD_DICT);
                        }
                    }
                    else
                    {
                        return _childDict.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.DEADLINE:
                {
                    _deadline = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.CREATE_TIME_STAMP:
                {
                    _createTimeStamp = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.TIMER_ID:
                {
                    _timerId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                {
                    _taskOnlineWithoutIdleTime = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.GUIDE_DATA:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _guideData = null;
                        }
                        else
                        {
                            _guideData = new CustomTypeList<TaskGuideData>(ref reader, ESerializeMode.Db);
                            _guideData?.SetParentInfo(this, PropertyIds.GUIDE_DATA);
                        }
                    }
                    else
                    {
                        return _guideData.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.SLOT_INDEX:
                {
                    _slotIndex = reader.ReadInt32();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                {
                    _bizId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.ID:
                {
                    _id = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.INDEX:
                {
                    _index = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.PARENT_ID:
                {
                    _parentId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.CHILD_DICT:
                {
                    _childDict = valueUnion.CustomTypeValue as BasicValueDictionary<long, long>;
                    break;
                }
                case PropertyIds.DEADLINE:
                {
                    _deadline = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.CREATE_TIME_STAMP:
                {
                    _createTimeStamp = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.TIMER_ID:
                {
                    _timerId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                {
                    _taskOnlineWithoutIdleTime = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.GUIDE_DATA:
                {
                    _guideData = valueUnion.CustomTypeValue as CustomTypeList<TaskGuideData>;
                    break;
                }
                case PropertyIds.SLOT_INDEX:
                {
                    _slotIndex = valueUnion.SimpleValue.IntValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.BIZ_ID:
                    _bizId = reader.ReadInt64();
                    break;
                case PropertyIds.ID:
                    _id = reader.ReadInt64();
                    break;
                case PropertyIds.INDEX:
                    _index = reader.ReadInt64();
                    break;
                case PropertyIds.PARENT_ID:
                    _parentId = reader.ReadInt64();
                    break;
                case PropertyIds.CHILD_DICT:
                    {
                        if (_childDict != null)
                        {
                            _childDict.ClearParentInfo();
                            _childDict.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _childDict = null;
                            continue;
                        }
                        _childDict = new(ref reader, mode);
                        _childDict.SetParentInfo(this, PropertyIds.CHILD_DICT);
                    }
                    break;
                case PropertyIds.DEADLINE:
                    _deadline = reader.ReadInt64();
                    break;
                case PropertyIds.CREATE_TIME_STAMP:
                    _createTimeStamp = reader.ReadInt64();
                    break;
                case PropertyIds.TIMER_ID:
                    _timerId = reader.ReadInt64();
                    break;
                case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                    _taskOnlineWithoutIdleTime = reader.ReadInt32();
                    break;
                case PropertyIds.GUIDE_DATA:
                    {
                        if (_guideData != null)
                        {
                            _guideData.ClearParentInfo();
                            _guideData.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _guideData = null;
                            continue;
                        }
                        _guideData = new(ref reader, mode);
                        _guideData.SetParentInfo(this, PropertyIds.GUIDE_DATA);
                    }
                    break;
                case PropertyIds.SLOT_INDEX:
                    _slotIndex = reader.ReadInt32();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.BIZ_ID:
                        writer.Write(_bizId);
                        break;
                    case PropertyIds.ID:
                        writer.Write(_id);
                        break;
                    case PropertyIds.INDEX:
                        writer.Write(_index);
                        break;
                    case PropertyIds.PARENT_ID:
                        writer.Write(_parentId);
                        break;
                    case PropertyIds.CHILD_DICT:
                        if (_childDict == null)
                            writer.WriteNil();
                        else
                            _childDict.SerializeCore(ref writer, mode, -1);
                        break;
                    case PropertyIds.DEADLINE:
                        writer.Write(_deadline);
                        break;
                    case PropertyIds.CREATE_TIME_STAMP:
                        writer.Write(_createTimeStamp);
                        break;
                    case PropertyIds.TIMER_ID:
                        writer.Write(_timerId);
                        break;
                    case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                        writer.Write(_taskOnlineWithoutIdleTime);
                        break;
                    case PropertyIds.GUIDE_DATA:
                        if (_guideData == null)
                            writer.WriteNil();
                        else
                            _guideData.SerializeCore(ref writer, mode, 1485959689);
                        break;
                    case PropertyIds.SLOT_INDEX:
                        writer.Write(_slotIndex);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.BIZ_ID:
                        jsonObj["BizId"] = BizId;
                        break;
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.INDEX:
                        jsonObj["Index"] = Index;
                        break;
                    case PropertyIds.PARENT_ID:
                        jsonObj["ParentId"] = ParentId;
                        break;
                    case PropertyIds.CHILD_DICT:
                        if (ChildDict != null) jsonObj["ChildDict"] = ChildDict.ToEJson();
                        break;
                    case PropertyIds.DEADLINE:
                        jsonObj["Deadline"] = Deadline;
                        break;
                    case PropertyIds.CREATE_TIME_STAMP:
                        jsonObj["CreateTimeStamp"] = CreateTimeStamp;
                        break;
                    case PropertyIds.TIMER_ID:
                        jsonObj["TimerId"] = TimerId;
                        break;
                    case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                        jsonObj["TaskOnlineWithoutIdleTime"] = TaskOnlineWithoutIdleTime;
                        break;
                    case PropertyIds.GUIDE_DATA:
                        if (GuideData != null) jsonObj["GuideData"] = GuideData.ToEJson();
                        break;
                    case PropertyIds.SLOT_INDEX:
                        jsonObj["SlotIndex"] = SlotIndex;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.BIZ_ID:
                        if (json.HasKey("BizId"))
                        {
                            if(json["BizId"].Tag == JSONNodeType.String && json["BizId"].Value.StartsWith("NumberLong"))
                                _bizId = long.Parse(json["BizId"].Value.Substring(11, json["BizId"].Value.Length - 12));
                            else
                                _bizId = json["BizId"].AsLong;
                        }
                        break;
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            if(json["Id"].Tag == JSONNodeType.String && json["Id"].Value.StartsWith("NumberLong"))
                                _id = long.Parse(json["Id"].Value.Substring(11, json["Id"].Value.Length - 12));
                            else
                                _id = json["Id"].AsLong;
                        }
                        break;
                    case PropertyIds.INDEX:
                        if (json.HasKey("Index"))
                        {
                            if(json["Index"].Tag == JSONNodeType.String && json["Index"].Value.StartsWith("NumberLong"))
                                _index = long.Parse(json["Index"].Value.Substring(11, json["Index"].Value.Length - 12));
                            else
                                _index = json["Index"].AsLong;
                        }
                        break;
                    case PropertyIds.PARENT_ID:
                        if (json.HasKey("ParentId"))
                        {
                            if(json["ParentId"].Tag == JSONNodeType.String && json["ParentId"].Value.StartsWith("NumberLong"))
                                _parentId = long.Parse(json["ParentId"].Value.Substring(11, json["ParentId"].Value.Length - 12));
                            else
                                _parentId = json["ParentId"].AsLong;
                        }
                        break;
                    case PropertyIds.CHILD_DICT:
                    {
                        if (_childDict != null)
                        {
                            _childDict.ClearParentInfo();
                            _childDict.ResetWhenDeserialize();
                        }
                        if (json.HasKey("ChildDict"))
                        {
                            _childDict = new(json["ChildDict"]);
                            _childDict.SetParentInfo(this, PropertyIds.CHILD_DICT);
                        }
                    }
                    break;
                    case PropertyIds.DEADLINE:
                        if (json.HasKey("Deadline"))
                        {
                            if(json["Deadline"].Tag == JSONNodeType.String && json["Deadline"].Value.StartsWith("NumberLong"))
                                _deadline = long.Parse(json["Deadline"].Value.Substring(11, json["Deadline"].Value.Length - 12));
                            else
                                _deadline = json["Deadline"].AsLong;
                        }
                        break;
                    case PropertyIds.CREATE_TIME_STAMP:
                        if (json.HasKey("CreateTimeStamp"))
                        {
                            if(json["CreateTimeStamp"].Tag == JSONNodeType.String && json["CreateTimeStamp"].Value.StartsWith("NumberLong"))
                                _createTimeStamp = long.Parse(json["CreateTimeStamp"].Value.Substring(11, json["CreateTimeStamp"].Value.Length - 12));
                            else
                                _createTimeStamp = json["CreateTimeStamp"].AsLong;
                        }
                        break;
                    case PropertyIds.TIMER_ID:
                        if (json.HasKey("TimerId"))
                        {
                            if(json["TimerId"].Tag == JSONNodeType.String && json["TimerId"].Value.StartsWith("NumberLong"))
                                _timerId = long.Parse(json["TimerId"].Value.Substring(11, json["TimerId"].Value.Length - 12));
                            else
                                _timerId = json["TimerId"].AsLong;
                        }
                        break;
                    case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                        if (json.HasKey("TaskOnlineWithoutIdleTime"))
                        {
                            _taskOnlineWithoutIdleTime = json["TaskOnlineWithoutIdleTime"].AsInt;
                        }
                        break;
                    case PropertyIds.GUIDE_DATA:
                    {
                        if (_guideData != null)
                        {
                            _guideData.ClearParentInfo();
                            _guideData.ResetWhenDeserialize();
                        }
                        if (json.HasKey("GuideData"))
                        {
                            _guideData = new(json["GuideData"]);
                            _guideData.SetParentInfo(this, PropertyIds.GUIDE_DATA);
                        }
                    }
                    break;
                    case PropertyIds.SLOT_INDEX:
                        if (json.HasKey("SlotIndex"))
                        {
                            _slotIndex = json["SlotIndex"].AsInt;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.BIZ_ID:
                    return $"{pathString}.BizId Set to {reader.ReadInt64()}";
                case PropertyIds.ID:
                    return $"{pathString}.Id Set to {reader.ReadInt64()}";
                case PropertyIds.INDEX:
                    return $"{pathString}.Index Set to {reader.ReadInt64()}";
                case PropertyIds.PARENT_ID:
                    return $"{pathString}.ParentId Set to {reader.ReadInt64()}";
                case PropertyIds.CHILD_DICT:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicValueDictionary<long, long>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.ChildDict Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".ChildDict");
                        return ChildDict.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.DEADLINE:
                    return $"{pathString}.Deadline Set to {reader.ReadInt64()}";
                case PropertyIds.CREATE_TIME_STAMP:
                    return $"{pathString}.CreateTimeStamp Set to {reader.ReadInt64()}";
                case PropertyIds.TIMER_ID:
                    return $"{pathString}.TimerId Set to {reader.ReadInt64()}";
                case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                    return $"{pathString}.TaskOnlineWithoutIdleTime Set to {reader.ReadInt32()}";
                case PropertyIds.GUIDE_DATA:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new CustomTypeList<TaskGuideData>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.GuideData Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".GuideData");
                        return GuideData.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.SLOT_INDEX:
                    return $"{pathString}.SlotIndex Set to {reader.ReadInt32()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("BizId", DebugDetailHelper.ToDetailObject(this.BizId));
            output.Add("Id", DebugDetailHelper.ToDetailObject(this.Id));
            output.Add("Index", DebugDetailHelper.ToDetailObject(this.Index));
            output.Add("ParentId", DebugDetailHelper.ToDetailObject(this.ParentId));
            output.Add("ChildDict", DebugDetailHelper.ToDetailObject(this.ChildDict));
            output.Add("Deadline", DebugDetailHelper.ToDetailObject(this.Deadline));
            output.Add("CreateTimeStamp", DebugDetailHelper.ToDetailObject(this.CreateTimeStamp));
            output.Add("TimerId", DebugDetailHelper.ToDetailObject(this.TimerId));
            output.Add("TaskOnlineWithoutIdleTime", DebugDetailHelper.ToDetailObject(this.TaskOnlineWithoutIdleTime));
            output.Add("GuideData", DebugDetailHelper.ToDetailObject(this.GuideData));
            output.Add("SlotIndex", DebugDetailHelper.ToDetailObject(this.SlotIndex));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                    propertyName = "BizId";
                    propertyValue = _bizId.ToString();
                    propertyValueIsDefault = _bizId == default;
                    break;
                case PropertyIds.ID:
                    propertyName = "Id";
                    propertyValue = _id.ToString();
                    propertyValueIsDefault = _id == default;
                    break;
                case PropertyIds.INDEX:
                    propertyName = "Index";
                    propertyValue = _index.ToString();
                    propertyValueIsDefault = _index == default;
                    break;
                case PropertyIds.PARENT_ID:
                    propertyName = "ParentId";
                    propertyValue = _parentId.ToString();
                    propertyValueIsDefault = _parentId == default;
                    break;
                case PropertyIds.CHILD_DICT:
                    propertyName = "ChildDict";
                    propertyBase = _childDict;
                    break;
                case PropertyIds.DEADLINE:
                    propertyName = "Deadline";
                    propertyValue = _deadline.ToString();
                    propertyValueIsDefault = _deadline == default;
                    break;
                case PropertyIds.CREATE_TIME_STAMP:
                    propertyName = "CreateTimeStamp";
                    propertyValue = _createTimeStamp.ToString();
                    propertyValueIsDefault = _createTimeStamp == default;
                    break;
                case PropertyIds.TIMER_ID:
                    propertyName = "TimerId";
                    propertyValue = _timerId.ToString();
                    propertyValueIsDefault = _timerId == default;
                    break;
                case PropertyIds.TASK_ONLINE_WITHOUT_IDLE_TIME:
                    propertyName = "TaskOnlineWithoutIdleTime";
                    propertyValue = _taskOnlineWithoutIdleTime.ToString();
                    propertyValueIsDefault = _taskOnlineWithoutIdleTime == default;
                    break;
                case PropertyIds.GUIDE_DATA:
                    propertyName = "GuideData";
                    propertyBase = _guideData;
                    break;
                case PropertyIds.SLOT_INDEX:
                    propertyName = "SlotIndex";
                    propertyValue = _slotIndex.ToString();
                    propertyValueIsDefault = _slotIndex == default;
                    break;
                default: break;
            }
        }
    }
}