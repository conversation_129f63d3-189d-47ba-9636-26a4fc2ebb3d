using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Common.CustomType
{
    public partial class TimerULongParam
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(TimerULongParam));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 520164504;
#else
        public const int CLASS_HASH = 520164504;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public TimerULongParam(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public TimerULongParam(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int VALUE = 0;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.VALUE
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            11
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.VALUE
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.VALUE
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.LocalOnly
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false
        };
        public static readonly int[] LodArray = new int[]
        {
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "Value"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
        };
        public const int VALUE_TYPE_COUNT = 1;
        public const int REF_TYPE_COUNT = 0;
        public ulong Value
        {
            get => _value;
            set
            {
                if (_value == value) return;
                var oldValue = _value;
                _value = value;
                TransactionAssignBackup(PropertyIds.VALUE, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.VALUE, value)) return;
                TryInvokeFieldChange(PropertyIds.VALUE, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.VALUE:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.ULONG) throw new Exception($"Type mismatch, expecting ulong got {seg.BasicType.Type}");
#endif
                    ulong newValue = seg.BasicType.ULongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _value;
                    _value = newValue;
                    TryInvokeFieldChange(0, oldValue, _value);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.VALUE:
                {
                    _value = reader.ReadUInt64();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.VALUE:
                {
                    _value = valueUnion.SimpleValue.ULongValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.VALUE:
                    _value = reader.ReadUInt64();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.VALUE:
                        writer.Write(_value);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.VALUE:
                        jsonObj["Value"] = Value;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.VALUE:
                        if (json.HasKey("Value"))
                        {
                            _value = json["Value"].AsULong;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.VALUE:
                    return $"{pathString}.Value Set to {reader.ReadUInt64()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("Value", DebugDetailHelper.ToDetailObject(this.Value));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.VALUE:
                    propertyName = "Value";
                    propertyValue = _value.ToString();
                    propertyValueIsDefault = _value == default;
                    break;
                default: break;
            }
        }
    }
}