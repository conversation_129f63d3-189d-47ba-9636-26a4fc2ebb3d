using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Share.Framework
{
    public static partial class ContainerDecoder
    {
        public static object DeserializeBuildingPrivilegeGroupContainer(ref MessagePackReader reader, int propertyId, ESerializeMode mode)
        {
            switch (propertyId)
            {
                case BuildingPrivilegeGroup.PropertyIds.TEAMMATES:
                    {
                        return new BasicValueDictionary<ulong, int>(ref reader, mode);
                    }
                case BuildingPrivilegeGroup.PropertyIds.OTHER_MEMBERS:
                    {
                        return new BasicValueDictionary<ulong, int>(ref reader, mode);
                    }
                default: return null;
            }
        }
    }
    public static partial class ShadowContainerDecoder
    {
        public static SyncContainerBase DeserializeBuildingPrivilegeGroupContainer(ref MessagePackReader reader, int propertyId, long entityId)
        {
            switch (propertyId)
            {
                case BuildingPrivilegeGroup.PropertyIds.TEAMMATES:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicValueDictionary<ulong, int>(ref reader, ESerializeMode.All));
                    }
                case BuildingPrivilegeGroup.PropertyIds.OTHER_MEMBERS:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicValueDictionary<ulong, int>(ref reader, ESerializeMode.All));
                    }
                default: return null;
            }
        }
    }
}
namespace WizardGames.Soc.Common.CustomType
{
    public partial class BuildingPrivilegeGroup
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BuildingPrivilegeGroup));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 1134036908;
#else
        public const int CLASS_HASH = 1134036908;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public BuildingPrivilegeGroup(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public BuildingPrivilegeGroup(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ID = 0;
            public const int TEAMMATES = 1;
            public const int OTHER_MEMBERS = 2;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ID, PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            1, 13, 13
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.ID, PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.ID, PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.ID, PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.ID, PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.INT << 16) | PropertyIds.ID, (TypeDefine.CONTAINER << 16) | PropertyIds.TEAMMATES, (TypeDefine.CONTAINER << 16) | PropertyIds.OTHER_MEMBERS
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.ID, PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0, 0, 0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.All, ESyncRange.All
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false
        };
        public static readonly int[] LodArray = new int[]
        {
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "Id", "Teammates", "OtherMembers"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0, 0 + 10000, 1 + 10000
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.TEAMMATES, PropertyIds.OTHER_MEMBERS
        };
        public const int VALUE_TYPE_COUNT = 1;
        public const int REF_TYPE_COUNT = 2;
        /// <summary>
        /// 群组Id
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                if (_id == value) return;
                var oldValue = _id;
                _id = value;
                TransactionAssignBackup(PropertyIds.ID, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.ID, value)) return;
                TryInvokeFieldChange(PropertyIds.ID, oldValue, value);
            }
        }
        /// <summary>
        /// 队友成员
        /// </summary>
        public BasicValueDictionary<ulong, int> Teammates
        {
            get => _teammates;
            set
            {
                if (_teammates == value) return;
                var oldValue = _teammates;
                _teammates?.ClearParentInfo();
                _teammates = value;
                value?.SetParentInfo(this, PropertyIds.TEAMMATES);
                TransactionAssignBackup(PropertyIds.TEAMMATES, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = -1};
                if (!TriggerPropertyChange(ref ctx, PropertyIds.TEAMMATES, value)) return;
                TryInvokeFieldChange(PropertyIds.TEAMMATES, oldValue, value);
            }
        }
        /// <summary>
        /// 其他非队友成员
        /// </summary>
        public BasicValueDictionary<ulong, int> OtherMembers
        {
            get => _otherMembers;
            set
            {
                if (_otherMembers == value) return;
                var oldValue = _otherMembers;
                _otherMembers?.ClearParentInfo();
                _otherMembers = value;
                value?.SetParentInfo(this, PropertyIds.OTHER_MEMBERS);
                TransactionAssignBackup(PropertyIds.OTHER_MEMBERS, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = -1};
                if (!TriggerPropertyChange(ref ctx, PropertyIds.OTHER_MEMBERS, value)) return;
                TryInvokeFieldChange(PropertyIds.OTHER_MEMBERS, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _id;
                    _id = newValue;
                    TryInvokeFieldChange(0, oldValue, _id);
                    return this;
                }
                case PropertyIds.TEAMMATES:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _teammates?.ClearParentInfo();
                            _teammates?.ResetWhenDeserialize();
                            BasicValueDictionary<ulong, int> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                            var oldValue = _teammates;
                            _teammates = newValue;
                            _teammates?.SetParentInfo(this, PropertyIds.TEAMMATES);
                            TryInvokeFieldChange(1, oldValue, _teammates);
                            return this;
                        }
                        else
                        {
                            return _teammates.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.OTHER_MEMBERS:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _otherMembers?.ClearParentInfo();
                            _otherMembers?.ResetWhenDeserialize();
                            BasicValueDictionary<ulong, int> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                            var oldValue = _otherMembers;
                            _otherMembers = newValue;
                            _otherMembers?.SetParentInfo(this, PropertyIds.OTHER_MEMBERS);
                            TryInvokeFieldChange(2, oldValue, _otherMembers);
                            return this;
                        }
                        else
                        {
                            return _otherMembers.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.ID:
                {
                    _id = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.TEAMMATES:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _teammates = null;
                        }
                        else
                        {
                            _teammates = new BasicValueDictionary<ulong, int>(ref reader, ESerializeMode.Db);
                            _teammates?.SetParentInfo(this, PropertyIds.TEAMMATES);
                        }
                    }
                    else
                    {
                        return _teammates.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.OTHER_MEMBERS:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _otherMembers = null;
                        }
                        else
                        {
                            _otherMembers = new BasicValueDictionary<ulong, int>(ref reader, ESerializeMode.Db);
                            _otherMembers?.SetParentInfo(this, PropertyIds.OTHER_MEMBERS);
                        }
                    }
                    else
                    {
                        return _otherMembers.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.ID:
                {
                    _id = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.TEAMMATES:
                {
                    _teammates = valueUnion.CustomTypeValue as BasicValueDictionary<ulong, int>;
                    break;
                }
                case PropertyIds.OTHER_MEMBERS:
                {
                    _otherMembers = valueUnion.CustomTypeValue as BasicValueDictionary<ulong, int>;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.ID:
                    _id = reader.ReadInt32();
                    break;
                case PropertyIds.TEAMMATES:
                    {
                        if (_teammates != null)
                        {
                            _teammates.ClearParentInfo();
                            _teammates.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _teammates = null;
                            continue;
                        }
                        _teammates = new(ref reader, mode);
                        _teammates.SetParentInfo(this, PropertyIds.TEAMMATES);
                    }
                    break;
                case PropertyIds.OTHER_MEMBERS:
                    {
                        if (_otherMembers != null)
                        {
                            _otherMembers.ClearParentInfo();
                            _otherMembers.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _otherMembers = null;
                            continue;
                        }
                        _otherMembers = new(ref reader, mode);
                        _otherMembers.SetParentInfo(this, PropertyIds.OTHER_MEMBERS);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.ID:
                        writer.Write(_id);
                        break;
                    case PropertyIds.TEAMMATES:
                        if (_teammates == null)
                            writer.WriteNil();
                        else
                            _teammates.SerializeCore(ref writer, mode, -1);
                        break;
                    case PropertyIds.OTHER_MEMBERS:
                        if (_otherMembers == null)
                            writer.WriteNil();
                        else
                            _otherMembers.SerializeCore(ref writer, mode, -1);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.TEAMMATES:
                        if (Teammates != null) jsonObj["Teammates"] = Teammates.ToEJson();
                        break;
                    case PropertyIds.OTHER_MEMBERS:
                        if (OtherMembers != null) jsonObj["OtherMembers"] = OtherMembers.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            _id = json["Id"].AsInt;
                        }
                        break;
                    case PropertyIds.TEAMMATES:
                    {
                        if (_teammates != null)
                        {
                            _teammates.ClearParentInfo();
                            _teammates.ResetWhenDeserialize();
                        }
                        if (json.HasKey("Teammates"))
                        {
                            _teammates = new(json["Teammates"]);
                            _teammates.SetParentInfo(this, PropertyIds.TEAMMATES);
                        }
                    }
                    break;
                    case PropertyIds.OTHER_MEMBERS:
                    {
                        if (_otherMembers != null)
                        {
                            _otherMembers.ClearParentInfo();
                            _otherMembers.ResetWhenDeserialize();
                        }
                        if (json.HasKey("OtherMembers"))
                        {
                            _otherMembers = new(json["OtherMembers"]);
                            _otherMembers.SetParentInfo(this, PropertyIds.OTHER_MEMBERS);
                        }
                    }
                    break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.ID:
                    return $"{pathString}.Id Set to {reader.ReadInt32()}";
                case PropertyIds.TEAMMATES:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicValueDictionary<ulong, int>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.Teammates Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".Teammates");
                        return Teammates.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.OTHER_MEMBERS:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicValueDictionary<ulong, int>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.OtherMembers Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".OtherMembers");
                        return OtherMembers.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("Id", DebugDetailHelper.ToDetailObject(this.Id));
            output.Add("Teammates", DebugDetailHelper.ToDetailObject(this.Teammates));
            output.Add("OtherMembers", DebugDetailHelper.ToDetailObject(this.OtherMembers));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.ID:
                    propertyName = "Id";
                    propertyValue = _id.ToString();
                    propertyValueIsDefault = _id == default;
                    break;
                case PropertyIds.TEAMMATES:
                    propertyName = "Teammates";
                    propertyBase = _teammates;
                    break;
                case PropertyIds.OTHER_MEMBERS:
                    propertyName = "OtherMembers";
                    propertyBase = _otherMembers;
                    break;
                default: break;
            }
        }
    }
}