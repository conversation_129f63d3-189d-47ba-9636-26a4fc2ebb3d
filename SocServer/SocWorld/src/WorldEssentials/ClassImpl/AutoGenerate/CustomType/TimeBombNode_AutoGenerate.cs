using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Common.CustomType
{
    public partial class TimeBombNode
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(TimeBombNode));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public new const int CLASS_HASH = 1221341805;
#else
        public new const int CLASS_HASH = 1221341805;
#endif
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public TimeBombNode(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public TimeBombNode(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int BIZ_ID = 0;
            public const int ID = 1;
            public const int INDEX = 2;
            public const int PARENT_ID = 3;
            public const int CONDITION = 4;
            public const int MAX_CONDITION = 5;
            public const int OWNER_ENTITY_ID = 6;
            public const int IS_LOCKED = 7;
            public const int SKIN_ID = 8;
            public const int IS_ENTER_INVENTORY = 9;
            public const int BELONG_ROLE_ID = 10;
            public const int COUNT = 11;
            public const int FREQUENCY = 12;
            public const int IS_RF_DETONATION = 13;
        }
#pragma warning restore CS0108
        public static new readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CONDITION, PropertyIds.MAX_CONDITION, PropertyIds.OWNER_ENTITY_ID, PropertyIds.IS_LOCKED, PropertyIds.SKIN_ID, PropertyIds.IS_ENTER_INVENTORY, PropertyIds.BELONG_ROLE_ID, PropertyIds.COUNT, PropertyIds.FREQUENCY, PropertyIds.IS_RF_DETONATION
        };
        public static new readonly int[] PropertyTypeArray = new int[]
        {
            2, 2, 2, 2, 3, 3, 2, 6, 2, 6, 11, 1, 1, 6
        };
        public static new readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CONDITION, PropertyIds.MAX_CONDITION, PropertyIds.OWNER_ENTITY_ID, PropertyIds.IS_LOCKED, PropertyIds.SKIN_ID, PropertyIds.IS_ENTER_INVENTORY, PropertyIds.BELONG_ROLE_ID, PropertyIds.COUNT, PropertyIds.FREQUENCY, PropertyIds.IS_RF_DETONATION
        };
        public static new readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CONDITION, PropertyIds.MAX_CONDITION, PropertyIds.OWNER_ENTITY_ID, PropertyIds.IS_LOCKED, PropertyIds.SKIN_ID, PropertyIds.IS_ENTER_INVENTORY, PropertyIds.BELONG_ROLE_ID, PropertyIds.COUNT, PropertyIds.FREQUENCY, PropertyIds.IS_RF_DETONATION
        };
        public static new readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CONDITION, PropertyIds.MAX_CONDITION, PropertyIds.OWNER_ENTITY_ID, PropertyIds.IS_LOCKED, PropertyIds.SKIN_ID, PropertyIds.IS_ENTER_INVENTORY, PropertyIds.COUNT, PropertyIds.FREQUENCY, PropertyIds.IS_RF_DETONATION
        };
        public static new readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CONDITION, PropertyIds.MAX_CONDITION, PropertyIds.OWNER_ENTITY_ID, PropertyIds.IS_LOCKED, PropertyIds.SKIN_ID, PropertyIds.IS_ENTER_INVENTORY, PropertyIds.COUNT, PropertyIds.FREQUENCY, PropertyIds.IS_RF_DETONATION
        };
        public static new readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.LONG << 16) | PropertyIds.BIZ_ID, (TypeDefine.LONG << 16) | PropertyIds.ID, (TypeDefine.LONG << 16) | PropertyIds.INDEX, (TypeDefine.LONG << 16) | PropertyIds.PARENT_ID, (TypeDefine.FLOAT << 16) | PropertyIds.CONDITION, (TypeDefine.FLOAT << 16) | PropertyIds.MAX_CONDITION, (TypeDefine.LONG << 16) | PropertyIds.OWNER_ENTITY_ID, (TypeDefine.BOOL << 16) | PropertyIds.IS_LOCKED, (TypeDefine.LONG << 16) | PropertyIds.SKIN_ID, (TypeDefine.BOOL << 16) | PropertyIds.IS_ENTER_INVENTORY, (TypeDefine.INT << 16) | PropertyIds.COUNT, (TypeDefine.INT << 16) | PropertyIds.FREQUENCY, (TypeDefine.BOOL << 16) | PropertyIds.IS_RF_DETONATION
        };
        public static new readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.BIZ_ID, PropertyIds.ID, PropertyIds.INDEX, PropertyIds.PARENT_ID, PropertyIds.CONDITION, PropertyIds.MAX_CONDITION, PropertyIds.OWNER_ENTITY_ID, PropertyIds.IS_LOCKED, PropertyIds.SKIN_ID, PropertyIds.IS_ENTER_INVENTORY, PropertyIds.COUNT, PropertyIds.FREQUENCY, PropertyIds.IS_RF_DETONATION
        };
        public static new readonly int[] CustomHashValueArray = new int[]
        {
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
        };
        public static new readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.LocalOnly, ESyncRange.All, ESyncRange.All, ESyncRange.All
        };
        public static new readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false, false, false, false, false, false, false, false, false, false, false, false
        };
        public static new readonly int[] LodArray = new int[]
        {
        };
        public static new readonly string[] PropNameArray = new string[]
        {
            "BizId", "Id", "Index", "ParentId", "Condition", "MaxCondition", "OwnerEntityId", "IsLocked", "SkinId", "IsEnterInventory", "BelongRoleId", "Count", "Frequency", "IsRFDetonation"
        };
        public static new readonly int[] PropId2Index = new int[]
        {
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13
        };
        public static new readonly int[] RefIndexInfoArray = new int[]
        {
        };
        public new const int VALUE_TYPE_COUNT = 14;
        public new const int REF_TYPE_COUNT = 0;
        /// <summary>
        /// 是否射频引爆
        /// </summary>
        public bool IsRFDetonation
        {
            get => _isRFDetonation;
            set
            {
                if (_isRFDetonation == value) return;
                var oldValue = _isRFDetonation;
                _isRFDetonation = value;
                TransactionAssignBackup(PropertyIds.IS_RF_DETONATION, oldValue);
                var ctx = new DiffContext();
                if (!TriggerPropertyChange(ref ctx, PropertyIds.IS_RF_DETONATION, value)) return;
                TryInvokeFieldChange(PropertyIds.IS_RF_DETONATION, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _bizId;
                    _bizId = newValue;
                    TryInvokeFieldChange(0, oldValue, _bizId);
                    return this;
                }
                case PropertyIds.ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _id;
                    _id = newValue;
                    TryInvokeFieldChange(1, oldValue, _id);
                    return this;
                }
                case PropertyIds.INDEX:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _index;
                    _index = newValue;
                    TryInvokeFieldChange(2, oldValue, _index);
                    return this;
                }
                case PropertyIds.PARENT_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _parentId;
                    _parentId = newValue;
                    TryInvokeFieldChange(3, oldValue, _parentId);
                    return this;
                }
                case PropertyIds.CONDITION:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.FLOAT) throw new Exception($"Type mismatch, expecting float got {seg.BasicType.Type}");
#endif
                    float newValue = seg.BasicType.FloatValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _condition;
                    _condition = newValue;
                    TryInvokeFieldChange(4, oldValue, _condition);
                    return this;
                }
                case PropertyIds.MAX_CONDITION:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.FLOAT) throw new Exception($"Type mismatch, expecting float got {seg.BasicType.Type}");
#endif
                    float newValue = seg.BasicType.FloatValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _maxCondition;
                    _maxCondition = newValue;
                    TryInvokeFieldChange(5, oldValue, _maxCondition);
                    return this;
                }
                case PropertyIds.OWNER_ENTITY_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _ownerEntityId;
                    _ownerEntityId = newValue;
                    TryInvokeFieldChange(6, oldValue, _ownerEntityId);
                    return this;
                }
                case PropertyIds.IS_LOCKED:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.BOOL) throw new Exception($"Type mismatch, expecting bool got {seg.BasicType.Type}");
#endif
                    bool newValue = seg.BasicType.BoolValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _isLocked;
                    _isLocked = newValue;
                    TryInvokeFieldChange(7, oldValue, _isLocked);
                    return this;
                }
                case PropertyIds.SKIN_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _skinId;
                    _skinId = newValue;
                    TryInvokeFieldChange(8, oldValue, _skinId);
                    return this;
                }
                case PropertyIds.IS_ENTER_INVENTORY:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.BOOL) throw new Exception($"Type mismatch, expecting bool got {seg.BasicType.Type}");
#endif
                    bool newValue = seg.BasicType.BoolValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _isEnterInventory;
                    _isEnterInventory = newValue;
                    TryInvokeFieldChange(9, oldValue, _isEnterInventory);
                    return this;
                }
                case PropertyIds.BELONG_ROLE_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.ULONG) throw new Exception($"Type mismatch, expecting ulong got {seg.BasicType.Type}");
#endif
                    ulong newValue = seg.BasicType.ULongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _belongRoleId;
                    _belongRoleId = newValue;
                    TryInvokeFieldChange(10, oldValue, _belongRoleId);
                    return this;
                }
                case PropertyIds.COUNT:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _count;
                    _count = newValue;
                    TryInvokeFieldChange(11, oldValue, _count);
                    return this;
                }
                case PropertyIds.FREQUENCY:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _frequency;
                    _frequency = newValue;
                    TryInvokeFieldChange(12, oldValue, _frequency);
                    return this;
                }
                case PropertyIds.IS_RF_DETONATION:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.BOOL) throw new Exception($"Type mismatch, expecting bool got {seg.BasicType.Type}");
#endif
                    bool newValue = seg.BasicType.BoolValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _isRFDetonation;
                    _isRFDetonation = newValue;
                    TryInvokeFieldChange(13, oldValue, _isRFDetonation);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                {
                    _bizId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.ID:
                {
                    _id = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.INDEX:
                {
                    _index = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.PARENT_ID:
                {
                    _parentId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.CONDITION:
                {
                    _condition = reader.ReadSingle();
                    return true;
                }
                case PropertyIds.MAX_CONDITION:
                {
                    _maxCondition = reader.ReadSingle();
                    return true;
                }
                case PropertyIds.OWNER_ENTITY_ID:
                {
                    _ownerEntityId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.IS_LOCKED:
                {
                    _isLocked = reader.ReadBoolean();
                    return true;
                }
                case PropertyIds.SKIN_ID:
                {
                    _skinId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.IS_ENTER_INVENTORY:
                {
                    _isEnterInventory = reader.ReadBoolean();
                    return true;
                }
                case PropertyIds.BELONG_ROLE_ID:
                {
                    _belongRoleId = reader.ReadUInt64();
                    return true;
                }
                case PropertyIds.COUNT:
                {
                    _count = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.FREQUENCY:
                {
                    _frequency = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.IS_RF_DETONATION:
                {
                    _isRFDetonation = reader.ReadBoolean();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                {
                    _bizId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.ID:
                {
                    _id = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.INDEX:
                {
                    _index = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.PARENT_ID:
                {
                    _parentId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.CONDITION:
                {
                    _condition = valueUnion.SimpleValue.FloatValue;
                    break;
                }
                case PropertyIds.MAX_CONDITION:
                {
                    _maxCondition = valueUnion.SimpleValue.FloatValue;
                    break;
                }
                case PropertyIds.OWNER_ENTITY_ID:
                {
                    _ownerEntityId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.IS_LOCKED:
                {
                    _isLocked = valueUnion.SimpleValue.BoolValue;
                    break;
                }
                case PropertyIds.SKIN_ID:
                {
                    _skinId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.IS_ENTER_INVENTORY:
                {
                    _isEnterInventory = valueUnion.SimpleValue.BoolValue;
                    break;
                }
                case PropertyIds.BELONG_ROLE_ID:
                {
                    _belongRoleId = valueUnion.SimpleValue.ULongValue;
                    break;
                }
                case PropertyIds.COUNT:
                {
                    _count = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.FREQUENCY:
                {
                    _frequency = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.IS_RF_DETONATION:
                {
                    _isRFDetonation = valueUnion.SimpleValue.BoolValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.BIZ_ID:
                    _bizId = reader.ReadInt64();
                    break;
                case PropertyIds.ID:
                    _id = reader.ReadInt64();
                    break;
                case PropertyIds.INDEX:
                    _index = reader.ReadInt64();
                    break;
                case PropertyIds.PARENT_ID:
                    _parentId = reader.ReadInt64();
                    break;
                case PropertyIds.CONDITION:
                    _condition = reader.ReadSingle();
                    break;
                case PropertyIds.MAX_CONDITION:
                    _maxCondition = reader.ReadSingle();
                    break;
                case PropertyIds.OWNER_ENTITY_ID:
                    _ownerEntityId = reader.ReadInt64();
                    break;
                case PropertyIds.IS_LOCKED:
                    _isLocked = reader.ReadBoolean();
                    break;
                case PropertyIds.SKIN_ID:
                    _skinId = reader.ReadInt64();
                    break;
                case PropertyIds.IS_ENTER_INVENTORY:
                    _isEnterInventory = reader.ReadBoolean();
                    break;
                case PropertyIds.BELONG_ROLE_ID:
                    _belongRoleId = reader.ReadUInt64();
                    break;
                case PropertyIds.COUNT:
                    _count = reader.ReadInt32();
                    break;
                case PropertyIds.FREQUENCY:
                    _frequency = reader.ReadInt32();
                    break;
                case PropertyIds.IS_RF_DETONATION:
                    _isRFDetonation = reader.ReadBoolean();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.BIZ_ID:
                        writer.Write(_bizId);
                        break;
                    case PropertyIds.ID:
                        writer.Write(_id);
                        break;
                    case PropertyIds.INDEX:
                        writer.Write(_index);
                        break;
                    case PropertyIds.PARENT_ID:
                        writer.Write(_parentId);
                        break;
                    case PropertyIds.CONDITION:
                        writer.Write(_condition);
                        break;
                    case PropertyIds.MAX_CONDITION:
                        writer.Write(_maxCondition);
                        break;
                    case PropertyIds.OWNER_ENTITY_ID:
                        writer.Write(_ownerEntityId);
                        break;
                    case PropertyIds.IS_LOCKED:
                        writer.Write(_isLocked);
                        break;
                    case PropertyIds.SKIN_ID:
                        writer.Write(_skinId);
                        break;
                    case PropertyIds.IS_ENTER_INVENTORY:
                        writer.Write(_isEnterInventory);
                        break;
                    case PropertyIds.BELONG_ROLE_ID:
                        writer.Write(_belongRoleId);
                        break;
                    case PropertyIds.COUNT:
                        writer.Write(_count);
                        break;
                    case PropertyIds.FREQUENCY:
                        writer.Write(_frequency);
                        break;
                    case PropertyIds.IS_RF_DETONATION:
                        writer.Write(_isRFDetonation);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.BIZ_ID:
                        jsonObj["BizId"] = BizId;
                        break;
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.INDEX:
                        jsonObj["Index"] = Index;
                        break;
                    case PropertyIds.PARENT_ID:
                        jsonObj["ParentId"] = ParentId;
                        break;
                    case PropertyIds.CONDITION:
                        jsonObj["Condition"] = Condition;
                        break;
                    case PropertyIds.MAX_CONDITION:
                        jsonObj["MaxCondition"] = MaxCondition;
                        break;
                    case PropertyIds.OWNER_ENTITY_ID:
                        jsonObj["OwnerEntityId"] = OwnerEntityId;
                        break;
                    case PropertyIds.IS_LOCKED:
                        jsonObj["IsLocked"] = IsLocked;
                        break;
                    case PropertyIds.SKIN_ID:
                        jsonObj["SkinId"] = SkinId;
                        break;
                    case PropertyIds.IS_ENTER_INVENTORY:
                        jsonObj["IsEnterInventory"] = IsEnterInventory;
                        break;
                    case PropertyIds.BELONG_ROLE_ID:
                        jsonObj["BelongRoleId"] = BelongRoleId;
                        break;
                    case PropertyIds.COUNT:
                        jsonObj["Count"] = Count;
                        break;
                    case PropertyIds.FREQUENCY:
                        jsonObj["Frequency"] = Frequency;
                        break;
                    case PropertyIds.IS_RF_DETONATION:
                        jsonObj["IsRFDetonation"] = IsRFDetonation;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.BIZ_ID:
                        if (json.HasKey("BizId"))
                        {
                            if(json["BizId"].Tag == JSONNodeType.String && json["BizId"].Value.StartsWith("NumberLong"))
                                _bizId = long.Parse(json["BizId"].Value.Substring(11, json["BizId"].Value.Length - 12));
                            else
                                _bizId = json["BizId"].AsLong;
                        }
                        break;
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            if(json["Id"].Tag == JSONNodeType.String && json["Id"].Value.StartsWith("NumberLong"))
                                _id = long.Parse(json["Id"].Value.Substring(11, json["Id"].Value.Length - 12));
                            else
                                _id = json["Id"].AsLong;
                        }
                        break;
                    case PropertyIds.INDEX:
                        if (json.HasKey("Index"))
                        {
                            if(json["Index"].Tag == JSONNodeType.String && json["Index"].Value.StartsWith("NumberLong"))
                                _index = long.Parse(json["Index"].Value.Substring(11, json["Index"].Value.Length - 12));
                            else
                                _index = json["Index"].AsLong;
                        }
                        break;
                    case PropertyIds.PARENT_ID:
                        if (json.HasKey("ParentId"))
                        {
                            if(json["ParentId"].Tag == JSONNodeType.String && json["ParentId"].Value.StartsWith("NumberLong"))
                                _parentId = long.Parse(json["ParentId"].Value.Substring(11, json["ParentId"].Value.Length - 12));
                            else
                                _parentId = json["ParentId"].AsLong;
                        }
                        break;
                    case PropertyIds.CONDITION:
                        if (json.HasKey("Condition"))
                        {
                            _condition = json["Condition"].AsFloat;
                        }
                        break;
                    case PropertyIds.MAX_CONDITION:
                        if (json.HasKey("MaxCondition"))
                        {
                            _maxCondition = json["MaxCondition"].AsFloat;
                        }
                        break;
                    case PropertyIds.OWNER_ENTITY_ID:
                        if (json.HasKey("OwnerEntityId"))
                        {
                            if(json["OwnerEntityId"].Tag == JSONNodeType.String && json["OwnerEntityId"].Value.StartsWith("NumberLong"))
                                _ownerEntityId = long.Parse(json["OwnerEntityId"].Value.Substring(11, json["OwnerEntityId"].Value.Length - 12));
                            else
                                _ownerEntityId = json["OwnerEntityId"].AsLong;
                        }
                        break;
                    case PropertyIds.IS_LOCKED:
                        if (json.HasKey("IsLocked"))
                        {
                            _isLocked = json["IsLocked"].AsBool;
                        }
                        break;
                    case PropertyIds.SKIN_ID:
                        if (json.HasKey("SkinId"))
                        {
                            if(json["SkinId"].Tag == JSONNodeType.String && json["SkinId"].Value.StartsWith("NumberLong"))
                                _skinId = long.Parse(json["SkinId"].Value.Substring(11, json["SkinId"].Value.Length - 12));
                            else
                                _skinId = json["SkinId"].AsLong;
                        }
                        break;
                    case PropertyIds.IS_ENTER_INVENTORY:
                        if (json.HasKey("IsEnterInventory"))
                        {
                            _isEnterInventory = json["IsEnterInventory"].AsBool;
                        }
                        break;
                    case PropertyIds.BELONG_ROLE_ID:
                        if (json.HasKey("BelongRoleId"))
                        {
                            _belongRoleId = json["BelongRoleId"].AsULong;
                        }
                        break;
                    case PropertyIds.COUNT:
                        if (json.HasKey("Count"))
                        {
                            _count = json["Count"].AsInt;
                        }
                        break;
                    case PropertyIds.FREQUENCY:
                        if (json.HasKey("Frequency"))
                        {
                            _frequency = json["Frequency"].AsInt;
                        }
                        break;
                    case PropertyIds.IS_RF_DETONATION:
                        if (json.HasKey("IsRFDetonation"))
                        {
                            _isRFDetonation = json["IsRFDetonation"].AsBool;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.BIZ_ID:
                    return $"{pathString}.BizId Set to {reader.ReadInt64()}";
                case PropertyIds.ID:
                    return $"{pathString}.Id Set to {reader.ReadInt64()}";
                case PropertyIds.INDEX:
                    return $"{pathString}.Index Set to {reader.ReadInt64()}";
                case PropertyIds.PARENT_ID:
                    return $"{pathString}.ParentId Set to {reader.ReadInt64()}";
                case PropertyIds.CONDITION:
                    return $"{pathString}.Condition Set to {reader.ReadSingle()}";
                case PropertyIds.MAX_CONDITION:
                    return $"{pathString}.MaxCondition Set to {reader.ReadSingle()}";
                case PropertyIds.OWNER_ENTITY_ID:
                    return $"{pathString}.OwnerEntityId Set to {reader.ReadInt64()}";
                case PropertyIds.IS_LOCKED:
                    return $"{pathString}.IsLocked Set to {reader.ReadBoolean()}";
                case PropertyIds.SKIN_ID:
                    return $"{pathString}.SkinId Set to {reader.ReadInt64()}";
                case PropertyIds.IS_ENTER_INVENTORY:
                    return $"{pathString}.IsEnterInventory Set to {reader.ReadBoolean()}";
                case PropertyIds.BELONG_ROLE_ID:
                    return $"{pathString}.BelongRoleId Set to {reader.ReadUInt64()}";
                case PropertyIds.COUNT:
                    return $"{pathString}.Count Set to {reader.ReadInt32()}";
                case PropertyIds.FREQUENCY:
                    return $"{pathString}.Frequency Set to {reader.ReadInt32()}";
                case PropertyIds.IS_RF_DETONATION:
                    return $"{pathString}.IsRFDetonation Set to {reader.ReadBoolean()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("BizId", DebugDetailHelper.ToDetailObject(this.BizId));
            output.Add("Id", DebugDetailHelper.ToDetailObject(this.Id));
            output.Add("Index", DebugDetailHelper.ToDetailObject(this.Index));
            output.Add("ParentId", DebugDetailHelper.ToDetailObject(this.ParentId));
            output.Add("Condition", DebugDetailHelper.ToDetailObject(this.Condition));
            output.Add("MaxCondition", DebugDetailHelper.ToDetailObject(this.MaxCondition));
            output.Add("OwnerEntityId", DebugDetailHelper.ToDetailObject(this.OwnerEntityId));
            output.Add("IsLocked", DebugDetailHelper.ToDetailObject(this.IsLocked));
            output.Add("SkinId", DebugDetailHelper.ToDetailObject(this.SkinId));
            output.Add("IsEnterInventory", DebugDetailHelper.ToDetailObject(this.IsEnterInventory));
            output.Add("BelongRoleId", DebugDetailHelper.ToDetailObject(this.BelongRoleId));
            output.Add("Count", DebugDetailHelper.ToDetailObject(this.Count));
            output.Add("Frequency", DebugDetailHelper.ToDetailObject(this.Frequency));
            output.Add("IsRFDetonation", DebugDetailHelper.ToDetailObject(this.IsRFDetonation));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.BIZ_ID:
                    propertyName = "BizId";
                    propertyValue = _bizId.ToString();
                    propertyValueIsDefault = _bizId == default;
                    break;
                case PropertyIds.ID:
                    propertyName = "Id";
                    propertyValue = _id.ToString();
                    propertyValueIsDefault = _id == default;
                    break;
                case PropertyIds.INDEX:
                    propertyName = "Index";
                    propertyValue = _index.ToString();
                    propertyValueIsDefault = _index == default;
                    break;
                case PropertyIds.PARENT_ID:
                    propertyName = "ParentId";
                    propertyValue = _parentId.ToString();
                    propertyValueIsDefault = _parentId == default;
                    break;
                case PropertyIds.CONDITION:
                    propertyName = "Condition";
                    propertyValue = _condition.ToString();
                    propertyValueIsDefault = _condition == default;
                    break;
                case PropertyIds.MAX_CONDITION:
                    propertyName = "MaxCondition";
                    propertyValue = _maxCondition.ToString();
                    propertyValueIsDefault = _maxCondition == default;
                    break;
                case PropertyIds.OWNER_ENTITY_ID:
                    propertyName = "OwnerEntityId";
                    propertyValue = _ownerEntityId.ToString();
                    propertyValueIsDefault = _ownerEntityId == default;
                    break;
                case PropertyIds.IS_LOCKED:
                    propertyName = "IsLocked";
                    propertyValue = _isLocked.ToString();
                    propertyValueIsDefault = _isLocked == default;
                    break;
                case PropertyIds.SKIN_ID:
                    propertyName = "SkinId";
                    propertyValue = _skinId.ToString();
                    propertyValueIsDefault = _skinId == default;
                    break;
                case PropertyIds.IS_ENTER_INVENTORY:
                    propertyName = "IsEnterInventory";
                    propertyValue = _isEnterInventory.ToString();
                    propertyValueIsDefault = _isEnterInventory == default;
                    break;
                case PropertyIds.BELONG_ROLE_ID:
                    propertyName = "BelongRoleId";
                    propertyValue = _belongRoleId.ToString();
                    propertyValueIsDefault = _belongRoleId == default;
                    break;
                case PropertyIds.COUNT:
                    propertyName = "Count";
                    propertyValue = _count.ToString();
                    propertyValueIsDefault = _count == default;
                    break;
                case PropertyIds.FREQUENCY:
                    propertyName = "Frequency";
                    propertyValue = _frequency.ToString();
                    propertyValueIsDefault = _frequency == default;
                    break;
                case PropertyIds.IS_RF_DETONATION:
                    propertyName = "IsRFDetonation";
                    propertyValue = _isRFDetonation.ToString();
                    propertyValueIsDefault = _isRFDetonation == default;
                    break;
                default: break;
            }
        }
    }
}