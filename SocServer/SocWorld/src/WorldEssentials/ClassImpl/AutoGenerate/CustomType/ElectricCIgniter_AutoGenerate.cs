using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Construction;
namespace WizardGames.Soc.Common.CustomType
{
    public partial class ElectricCIgniter
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(ElectricCIgniter));
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public new const int CLASS_HASH = 805641050;
#else
        public new const int CLASS_HASH = 805641050;
#endif
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public ElectricCIgniter(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public ElectricCIgniter(bool useless, JSONNode json) : base(useless, json) { }
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int PART_WIRE_CONNECTION = 0;
            public const int LIGHT_STATE = 1;
        }
#pragma warning restore CS0108
        public static new readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.PART_WIRE_CONNECTION, PropertyIds.LIGHT_STATE
        };
        public static new readonly int[] PropertyTypeArray = new int[]
        {
            12, 1
        };
        public static new readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.PART_WIRE_CONNECTION, PropertyIds.LIGHT_STATE
        };
        public static new readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.PART_WIRE_CONNECTION, PropertyIds.LIGHT_STATE
        };
        public static new readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.PART_WIRE_CONNECTION, PropertyIds.LIGHT_STATE
        };
        public static new readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.PART_WIRE_CONNECTION, PropertyIds.LIGHT_STATE
        };
        public static new readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.CUSTOM_TYPE << 16) | PropertyIds.PART_WIRE_CONNECTION, (TypeDefine.INT << 16) | PropertyIds.LIGHT_STATE
        };
        public static new readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.PART_WIRE_CONNECTION, PropertyIds.LIGHT_STATE
        };
        public static new readonly int[] CustomHashValueArray = new int[]
        {
            982021763, 0
        };
        public static new readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.All
        };
        public static new readonly bool[] SyncDelayArray = new bool[]
        {
            false, false
        };
        public static new readonly int[] LodArray = new int[]
        {
        };
        public static new readonly string[] PropNameArray = new string[]
        {
            "PartWireConnection", "LightState"
        };
        public static new readonly int[] PropId2Index = new int[]
        {
            0 + 10000, 0
        };
        public static new readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.PART_WIRE_CONNECTION
        };
        public new const int VALUE_TYPE_COUNT = 1;
        public new const int REF_TYPE_COUNT = 1;
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.PART_WIRE_CONNECTION:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _partWireConnection?.ClearParentInfo();
                            PartWireConnection newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<PartWireConnection>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 982021763, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                            var oldValue = _partWireConnection;
                            _partWireConnection = newValue;
                            _partWireConnection?.SetParentInfo(this, PropertyIds.PART_WIRE_CONNECTION);
                            TryInvokeFieldChange(0, oldValue, _partWireConnection);
                            return this;
                        }
                        else
                        {
                            return _partWireConnection.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.LIGHT_STATE:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                        TriggerPropertyChangeInSyncUpdate(ref ctx, newValue);
                    var oldValue = _lightState;
                    _lightState = newValue;
                    TryInvokeFieldChange(1, oldValue, _lightState);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.PART_WIRE_CONNECTION:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _partWireConnection = null;
                        }
                        else
                        {
                            _partWireConnection = (PartWireConnection)CustomTypeHelper.DeserializeCustomTypeBase<PartWireConnection>(ref reader, ESerializeMode.Db);
                            _partWireConnection?.SetParentInfo(this, PropertyIds.PART_WIRE_CONNECTION);
                        }
                    }
                    else
                    {
                        return _partWireConnection.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.LIGHT_STATE:
                {
                    _lightState = reader.ReadInt32();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.PART_WIRE_CONNECTION:
                {
                    _partWireConnection = valueUnion.CustomTypeValue as PartWireConnection;
                    break;
                }
                case PropertyIds.LIGHT_STATE:
                {
                    _lightState = valueUnion.SimpleValue.IntValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.PART_WIRE_CONNECTION:
                    {
                        _partWireConnection?.ClearParentInfo();
                        _partWireConnection = CustomTypeHelper.DeserializeCustomTypeBase<PartWireConnection>(ref reader, mode);
                        _partWireConnection?.SetParentInfo(this, PropertyIds.PART_WIRE_CONNECTION);
                    }
                    break;
                case PropertyIds.LIGHT_STATE:
                    _lightState = reader.ReadInt32();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.PART_WIRE_CONNECTION:
                        if (_partWireConnection == null)
                            writer.WriteNil();
                        else
                            _partWireConnection.SerializeCore(ref writer, mode, 982021763);
                        break;
                    case PropertyIds.LIGHT_STATE:
                        writer.Write(_lightState);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.PART_WIRE_CONNECTION:
                        if (PartWireConnection != null) jsonObj["PartWireConnection"] = PartWireConnection.ToEJson();
                        break;
                    case PropertyIds.LIGHT_STATE:
                        jsonObj["LightState"] = LightState;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.PART_WIRE_CONNECTION:
                    {
                        _partWireConnection?.ClearParentInfo();
                        if (json.HasKey("PartWireConnection"))
                        {
                            _partWireConnection = CustomTypeFactory.ConstructCustomTypeByJson(json["PartWireConnection"]) as PartWireConnection;
                            _partWireConnection?.SetParentInfo(this, PropertyIds.PART_WIRE_CONNECTION);
                        }
                    }
                    break;
                    case PropertyIds.LIGHT_STATE:
                        if (json.HasKey("LightState"))
                        {
                            _lightState = json["LightState"].AsInt;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.PART_WIRE_CONNECTION:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<PartWireConnection>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.PartWireConnection Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".PartWireConnection");
                        return PartWireConnection.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.LIGHT_STATE:
                    return $"{pathString}.LightState Set to {reader.ReadInt32()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("PartWireConnection", DebugDetailHelper.ToDetailObject(this.PartWireConnection));
            output.Add("LightState", DebugDetailHelper.ToDetailObject(this.LightState));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.PART_WIRE_CONNECTION:
                    propertyName = "PartWireConnection";
                    propertyBase = _partWireConnection;
                    break;
                case PropertyIds.LIGHT_STATE:
                    propertyName = "LightState";
                    propertyValue = _lightState.ToString();
                    propertyValueIsDefault = _lightState == default;
                    break;
                default: break;
            }
        }
        public override JSONNode ConstructionExportedToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.PART_WIRE_CONNECTION:
                        if (PartWireConnection != null)
                        {
                            if (PartWireConnection is IConstructionExport iexport)
                            {
                                jsonObj["PartWireConnection"] = iexport.ConstructionExportedToEJson();
                            }
                            else
                            {
                                jsonObj["PartWireConnection"] = PartWireConnection.ToEJson();
                            }
                        }
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
    }
}