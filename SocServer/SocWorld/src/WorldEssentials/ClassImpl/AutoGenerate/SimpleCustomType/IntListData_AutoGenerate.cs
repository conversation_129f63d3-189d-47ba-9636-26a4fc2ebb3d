using MessagePack;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Utility;
using System.Collections.Generic;
using WizardGames.Soc.Share.Framework;
namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class IntListData : ISerializeType
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(IntListData));
        public IntListData(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public const int CLASS_HASH = 1840387162;
        public static int StaticClassHash = CLASS_HASH;
        public int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int INNER = 0;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.INNER
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            12
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "Inner"
        };
        public void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.INNER:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            Inner = null;
                            continue;
                        }
                        Inner = SimpleCustomTypeHelper.DeserializeBasicTypeList<int>(ref reader);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.INNER:
                            if (Inner == null)
                                writer.WriteNil();
                            else
                                Inner.SerializeBasicType(ref writer);
                            break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
    }
}