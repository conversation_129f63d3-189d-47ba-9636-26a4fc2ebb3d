using MessagePack;
using SimpleJSON;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListCreateListCfg : ValueListBoolCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListCreateListCfg));
        public BoolListCreateListCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListCreateListCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListCreateListCfg BoolListCreateListCfgConstructor() {  return new BoolListCreateListCfg(); }
        public BoolListCreateListCfg() : base() {}
        public new const int CLASS_HASH = 709071403;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int DEBUG_TYPE_NAME = 0;
            public const int LIST = 1;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.DEBUG_TYPE_NAME,PropertyIds.LIST
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            5,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "DebugTypeName","List"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.LIST:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            List = null;
                            continue;
                        }
                        List = SimpleCustomTypeHelper.DeserializeBasicTypeList<bool>(ref reader);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.LIST:
                            if (List == null)
                                writer.WriteNil();
                            else
                                List.SerializeBasicType(ref writer);
                            break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.LIST:
                        if (List != null) jsonObj["List"] = SimpleCustomTypeHelper.ToEJson(List);
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.LIST:
                    {
                        if (List != null)
                        {
                            List.Clear();
                        }
                        if (json.HasKey("List"))
                        {
                            SimpleCustomTypeHelper.BasicTypeFromEJson(ref List, json["List"]);
                        }
                    }
                    break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListCreateListDynamicCfg : ValueListBoolCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListCreateListDynamicCfg));
        public BoolListCreateListDynamicCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListCreateListDynamicCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListCreateListDynamicCfg BoolListCreateListDynamicCfgConstructor() {  return new BoolListCreateListDynamicCfg(); }
        public BoolListCreateListDynamicCfg() : base() {}
        public new const int CLASS_HASH = 650530394;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int DEBUG_TYPE_NAME = 0;
            public const int LIST = 1;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.DEBUG_TYPE_NAME,PropertyIds.LIST
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            5,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "DebugTypeName","List"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.LIST:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            List = null;
                            continue;
                        }
                        List = SimpleCustomTypeHelper.DeserializeList<ValueBoolCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.LIST:
                            if (List == null)
                                writer.WriteNil();
                            else
                                List.SerializeCore(ref writer, ESerializeMode.All, 100212134);
                            break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.LIST:
                        if (List != null) jsonObj["List"] = SimpleCustomTypeHelper.ToEJson(List);
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.LIST:
                    {
                        if (List != null)
                        {
                            List.Clear();
                        }
                        if (json.HasKey("List"))
                        {
                            SimpleCustomTypeHelper.FromEJson(ref List, json["List"]);
                        }
                    }
                    break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListGetCfg : ValueBoolCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListGetCfg));
        public BoolListGetCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListGetCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListGetCfg BoolListGetCfgConstructor() {  return new BoolListGetCfg(); }
        public BoolListGetCfg() : base() {}
        public new const int CLASS_HASH = 1733215215;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int DEBUG_TYPE_NAME = 0;
            public const int NAME = 1;
            public const int INDEX = 2;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.DEBUG_TYPE_NAME,PropertyIds.NAME,PropertyIds.INDEX
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            5,2,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "DebugTypeName","Name","Index"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                case PropertyIds.INDEX:
                    {
                        Index = CustomTypeHelper.DeserializeCustomTypeBase<ValueLongCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    case PropertyIds.INDEX:
                        if (Index == null)
                            writer.WriteNil();
                        else
                            Index.SerializeCore(ref writer, ESerializeMode.All, 172831555);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    case PropertyIds.INDEX:
                        if (Index != null) jsonObj["Index"] = Index.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    case PropertyIds.INDEX:
                        if (json.HasKey("Index"))
                        {
                            JSONNode element = json["Index"];
                            int hash = element["Hash"].AsInt;
                            var func = SimpleCustomTypeHelper.SimpleCustomTypeConstructors[hash];
                            ValueLongCfg v = func() as ValueLongCfg;
                            v.FromEJson(element);
                            Index = v;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListContainsCfg : ValueBoolCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListContainsCfg));
        public BoolListContainsCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListContainsCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListContainsCfg BoolListContainsCfgConstructor() {  return new BoolListContainsCfg(); }
        public BoolListContainsCfg() : base() {}
        public new const int CLASS_HASH = 531899384;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int DEBUG_TYPE_NAME = 0;
            public const int NAME = 1;
            public const int ITEM = 2;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.DEBUG_TYPE_NAME,PropertyIds.NAME,PropertyIds.ITEM
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            5,2,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "DebugTypeName","Name","Item"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                case PropertyIds.ITEM:
                    {
                        Item = CustomTypeHelper.DeserializeCustomTypeBase<ValueBoolCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    case PropertyIds.ITEM:
                        if (Item == null)
                            writer.WriteNil();
                        else
                            Item.SerializeCore(ref writer, ESerializeMode.All, 100212134);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    case PropertyIds.ITEM:
                        if (Item != null) jsonObj["Item"] = Item.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    case PropertyIds.ITEM:
                        if (json.HasKey("Item"))
                        {
                            JSONNode element = json["Item"];
                            int hash = element["Hash"].AsInt;
                            var func = SimpleCustomTypeHelper.SimpleCustomTypeConstructors[hash];
                            ValueBoolCfg v = func() as ValueBoolCfg;
                            v.FromEJson(element);
                            Item = v;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListSetCfg : ActionCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListSetCfg));
        public BoolListSetCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListSetCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListSetCfg BoolListSetCfgConstructor() {  return new BoolListSetCfg(); }
        public BoolListSetCfg() : base() {}
        public new const int CLASS_HASH = 55781517;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ID = 0;
            public const int DEBUG_TYPE_NAME = 1;
            public const int INPUT0 = 2;
            public const int OUTPUT0 = 3;
            public const int NAME = 4;
            public const int INDEX = 5;
            public const int ITEM = 6;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ID,PropertyIds.DEBUG_TYPE_NAME,PropertyIds.INPUT0,PropertyIds.OUTPUT0,PropertyIds.NAME,PropertyIds.INDEX,PropertyIds.ITEM
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            1,5,1,1,2,12,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "Id","DebugTypeName","Input0","Output0","Name","Index","Item"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.ID:
                    Id = reader.ReadInt32();
                    break;
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.INPUT0:
                    Input0 = reader.ReadInt32();
                    break;
                case PropertyIds.OUTPUT0:
                    Output0 = reader.ReadInt32();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                case PropertyIds.INDEX:
                    {
                        Index = CustomTypeHelper.DeserializeCustomTypeBase<ValueLongCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                case PropertyIds.ITEM:
                    {
                        Item = CustomTypeHelper.DeserializeCustomTypeBase<ValueBoolCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.ID:
                            writer.Write(Id);
                            break;
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.INPUT0:
                            writer.Write(Input0);
                            break;
                        case PropertyIds.OUTPUT0:
                            writer.Write(Output0);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    case PropertyIds.INDEX:
                        if (Index == null)
                            writer.WriteNil();
                        else
                            Index.SerializeCore(ref writer, ESerializeMode.All, 172831555);
                        break;
                    case PropertyIds.ITEM:
                        if (Item == null)
                            writer.WriteNil();
                        else
                            Item.SerializeCore(ref writer, ESerializeMode.All, 100212134);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.INPUT0:
                        jsonObj["Input0"] = Input0;
                        break;
                    case PropertyIds.OUTPUT0:
                        jsonObj["Output0"] = Output0;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    case PropertyIds.INDEX:
                        if (Index != null) jsonObj["Index"] = Index.ToEJson();
                        break;
                    case PropertyIds.ITEM:
                        if (Item != null) jsonObj["Item"] = Item.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            Id = json["Id"].AsInt;
                        }
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.INPUT0:
                        if (json.HasKey("Input0"))
                        {
                            Input0 = json["Input0"].AsInt;
                        }
                        break;
                    case PropertyIds.OUTPUT0:
                        if (json.HasKey("Output0"))
                        {
                            Output0 = json["Output0"].AsInt;
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    case PropertyIds.INDEX:
                        if (json.HasKey("Index"))
                        {
                            JSONNode element = json["Index"];
                            int hash = element["Hash"].AsInt;
                            var func = SimpleCustomTypeHelper.SimpleCustomTypeConstructors[hash];
                            ValueLongCfg v = func() as ValueLongCfg;
                            v.FromEJson(element);
                            Index = v;
                        }
                        break;
                    case PropertyIds.ITEM:
                        if (json.HasKey("Item"))
                        {
                            JSONNode element = json["Item"];
                            int hash = element["Hash"].AsInt;
                            var func = SimpleCustomTypeHelper.SimpleCustomTypeConstructors[hash];
                            ValueBoolCfg v = func() as ValueBoolCfg;
                            v.FromEJson(element);
                            Item = v;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListCountCfg : ValueLongCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListCountCfg));
        public BoolListCountCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListCountCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListCountCfg BoolListCountCfgConstructor() {  return new BoolListCountCfg(); }
        public BoolListCountCfg() : base() {}
        public new const int CLASS_HASH = 1512314226;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int DEBUG_TYPE_NAME = 0;
            public const int NAME = 1;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.DEBUG_TYPE_NAME,PropertyIds.NAME
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            5,2
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "DebugTypeName","Name"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListAddCfg : ActionCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListAddCfg));
        public BoolListAddCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListAddCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListAddCfg BoolListAddCfgConstructor() {  return new BoolListAddCfg(); }
        public BoolListAddCfg() : base() {}
        public new const int CLASS_HASH = 181967653;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ID = 0;
            public const int DEBUG_TYPE_NAME = 1;
            public const int INPUT0 = 2;
            public const int OUTPUT0 = 3;
            public const int NAME = 4;
            public const int ITEM = 5;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ID,PropertyIds.DEBUG_TYPE_NAME,PropertyIds.INPUT0,PropertyIds.OUTPUT0,PropertyIds.NAME,PropertyIds.ITEM
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            1,5,1,1,2,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "Id","DebugTypeName","Input0","Output0","Name","Item"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.ID:
                    Id = reader.ReadInt32();
                    break;
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.INPUT0:
                    Input0 = reader.ReadInt32();
                    break;
                case PropertyIds.OUTPUT0:
                    Output0 = reader.ReadInt32();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                case PropertyIds.ITEM:
                    {
                        Item = CustomTypeHelper.DeserializeCustomTypeBase<ValueBoolCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.ID:
                            writer.Write(Id);
                            break;
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.INPUT0:
                            writer.Write(Input0);
                            break;
                        case PropertyIds.OUTPUT0:
                            writer.Write(Output0);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    case PropertyIds.ITEM:
                        if (Item == null)
                            writer.WriteNil();
                        else
                            Item.SerializeCore(ref writer, ESerializeMode.All, 100212134);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.INPUT0:
                        jsonObj["Input0"] = Input0;
                        break;
                    case PropertyIds.OUTPUT0:
                        jsonObj["Output0"] = Output0;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    case PropertyIds.ITEM:
                        if (Item != null) jsonObj["Item"] = Item.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            Id = json["Id"].AsInt;
                        }
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.INPUT0:
                        if (json.HasKey("Input0"))
                        {
                            Input0 = json["Input0"].AsInt;
                        }
                        break;
                    case PropertyIds.OUTPUT0:
                        if (json.HasKey("Output0"))
                        {
                            Output0 = json["Output0"].AsInt;
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    case PropertyIds.ITEM:
                        if (json.HasKey("Item"))
                        {
                            JSONNode element = json["Item"];
                            int hash = element["Hash"].AsInt;
                            var func = SimpleCustomTypeHelper.SimpleCustomTypeConstructors[hash];
                            ValueBoolCfg v = func() as ValueBoolCfg;
                            v.FromEJson(element);
                            Item = v;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListRemoveAtCfg : ActionCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListRemoveAtCfg));
        public BoolListRemoveAtCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListRemoveAtCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListRemoveAtCfg BoolListRemoveAtCfgConstructor() {  return new BoolListRemoveAtCfg(); }
        public BoolListRemoveAtCfg() : base() {}
        public new const int CLASS_HASH = 1351453883;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ID = 0;
            public const int DEBUG_TYPE_NAME = 1;
            public const int INPUT0 = 2;
            public const int OUTPUT0 = 3;
            public const int NAME = 4;
            public const int INDEX = 5;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ID,PropertyIds.DEBUG_TYPE_NAME,PropertyIds.INPUT0,PropertyIds.OUTPUT0,PropertyIds.NAME,PropertyIds.INDEX
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            1,5,1,1,2,12
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "Id","DebugTypeName","Input0","Output0","Name","Index"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.ID:
                    Id = reader.ReadInt32();
                    break;
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.INPUT0:
                    Input0 = reader.ReadInt32();
                    break;
                case PropertyIds.OUTPUT0:
                    Output0 = reader.ReadInt32();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                case PropertyIds.INDEX:
                    {
                        Index = CustomTypeHelper.DeserializeCustomTypeBase<ValueLongCfg>(ref reader, ESerializeMode.All);
                    }
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.ID:
                            writer.Write(Id);
                            break;
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.INPUT0:
                            writer.Write(Input0);
                            break;
                        case PropertyIds.OUTPUT0:
                            writer.Write(Output0);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    case PropertyIds.INDEX:
                        if (Index == null)
                            writer.WriteNil();
                        else
                            Index.SerializeCore(ref writer, ESerializeMode.All, 172831555);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.INPUT0:
                        jsonObj["Input0"] = Input0;
                        break;
                    case PropertyIds.OUTPUT0:
                        jsonObj["Output0"] = Output0;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    case PropertyIds.INDEX:
                        if (Index != null) jsonObj["Index"] = Index.ToEJson();
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            Id = json["Id"].AsInt;
                        }
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.INPUT0:
                        if (json.HasKey("Input0"))
                        {
                            Input0 = json["Input0"].AsInt;
                        }
                        break;
                    case PropertyIds.OUTPUT0:
                        if (json.HasKey("Output0"))
                        {
                            Output0 = json["Output0"].AsInt;
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    case PropertyIds.INDEX:
                        if (json.HasKey("Index"))
                        {
                            JSONNode element = json["Index"];
                            int hash = element["Hash"].AsInt;
                            var func = SimpleCustomTypeHelper.SimpleCustomTypeConstructors[hash];
                            ValueLongCfg v = func() as ValueLongCfg;
                            v.FromEJson(element);
                            Index = v;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}

namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class BoolListClearCfg : ActionCfg
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(BoolListClearCfg));
        public BoolListClearCfg(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public BoolListClearCfg(bool _, JSONNode json) { FromEJson(json); }
        public static BoolListClearCfg BoolListClearCfgConstructor() {  return new BoolListClearCfg(); }
        public BoolListClearCfg() : base() {}
        public new const int CLASS_HASH = 1246917348;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int ID = 0;
            public const int DEBUG_TYPE_NAME = 1;
            public const int INPUT0 = 2;
            public const int OUTPUT0 = 3;
            public const int NAME = 4;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.ID,PropertyIds.DEBUG_TYPE_NAME,PropertyIds.INPUT0,PropertyIds.OUTPUT0,PropertyIds.NAME
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            1,5,1,1,2
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "Id","DebugTypeName","Input0","Output0","Name"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.ID:
                    Id = reader.ReadInt32();
                    break;
                case PropertyIds.DEBUG_TYPE_NAME:
                    DebugTypeName = reader.ReadString();
                    break;
                case PropertyIds.INPUT0:
                    Input0 = reader.ReadInt32();
                    break;
                case PropertyIds.OUTPUT0:
                    Output0 = reader.ReadInt32();
                    break;
                case PropertyIds.NAME:
                    Name = reader.ReadInt64();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.ID:
                            writer.Write(Id);
                            break;
                        case PropertyIds.DEBUG_TYPE_NAME:
                            writer.Write(DebugTypeName);
                            break;
                        case PropertyIds.INPUT0:
                            writer.Write(Input0);
                            break;
                        case PropertyIds.OUTPUT0:
                            writer.Write(Output0);
                            break;
                        case PropertyIds.NAME:
                            writer.Write(Name);
                            break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        jsonObj["Id"] = Id;
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        jsonObj["DebugTypeName"] = DebugTypeName;
                        break;
                    case PropertyIds.INPUT0:
                        jsonObj["Input0"] = Input0;
                        break;
                    case PropertyIds.OUTPUT0:
                        jsonObj["Output0"] = Output0;
                        break;
                    case PropertyIds.NAME:
                        jsonObj["Name"] = Name;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PropertyIdRegister.GetPropertyIdList(ESerializeMode.All, CLASS_HASH))
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.ID:
                        if (json.HasKey("Id"))
                        {
                            Id = json["Id"].AsInt;
                        }
                        break;
                    case PropertyIds.DEBUG_TYPE_NAME:
                        if (json.HasKey("DebugTypeName"))
                        {
                            DebugTypeName = json["DebugTypeName"];
                        }
                        break;
                    case PropertyIds.INPUT0:
                        if (json.HasKey("Input0"))
                        {
                            Input0 = json["Input0"].AsInt;
                        }
                        break;
                    case PropertyIds.OUTPUT0:
                        if (json.HasKey("Output0"))
                        {
                            Output0 = json["Output0"].AsInt;
                        }
                        break;
                    case PropertyIds.NAME:
                        if (json.HasKey("Name"))
                        {
                            if(json["Name"].Tag == JSONNodeType.String && json["Name"].Value.StartsWith("NumberLong"))
                                Name = long.Parse(json["Name"].Value.Substring(11, json["Name"].Value.Length - 12));
                            else
                                Name = json["Name"].AsLong;
                        }
                        break;
                    default: break;
                }
            }
        }
    }
}