using MessagePack;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Cache;
using WizardGames.Soc.Share.Framework;
using Share.Common.ObjPool;
namespace WizardGames.Soc.Common.SimpleCustom
{
    public partial class TrainBarricadeHpZeroEvent : AbstractLocationBasedEvent
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(TrainBarricadeHpZeroEvent));
        public TrainBarricadeHpZeroEvent(ref MessagePackReader reader, ESerializeMode mode) { DeserializeCore(ref reader, mode); }
        public new const int CLASS_HASH = 1798641620;
        public static new int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int POS_X = 0;
            public const int POS_Z = 1;
            public const int SOURCE_ID = 2;
        }
#pragma warning restore CS0108
        public new static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.POS_X,PropertyIds.POS_Z,PropertyIds.SOURCE_ID
        };
        public new static readonly int[] PropertyTypeArray = new int[]
        {
            3,3,2
        };
        public new static readonly string[] PropNameArray = new string[]
        {
            "PosX","PosZ","SourceId"
        };
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            foreach (var propId in PropertyInfoArray)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (propId)
                {
                case PropertyIds.POS_X:
                    PosX = reader.ReadSingle();
                    break;
                case PropertyIds.POS_Z:
                    PosZ = reader.ReadSingle();
                    break;
                case PropertyIds.SOURCE_ID:
                    SourceId = reader.ReadInt64();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (CLASS_HASH == templateHashValue) writer.Write(0);
            else writer.Write(CLASS_HASH);
            foreach (var propId in PropertyInfoArray)
            {
                switch (propId)
                {
                        case PropertyIds.POS_X:
                            writer.Write(PosX);
                            break;
                        case PropertyIds.POS_Z:
                            writer.Write(PosZ);
                            break;
                        case PropertyIds.SOURCE_ID:
                            writer.Write(SourceId);
                            break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
    }
}