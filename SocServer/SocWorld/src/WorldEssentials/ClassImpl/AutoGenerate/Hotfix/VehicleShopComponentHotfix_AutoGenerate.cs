using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Component
{
    public static partial class VehicleShopComponentHotfixWrapper
    {
        public static Func<VehicleShopComponent, PlayerEntity, int, uint, EOpCode> BuyImpl;



    }
}
namespace WizardGames.Soc.Common.Component
{
    public partial class VehicleShopComponent
    {
        public override partial EOpCode Buy(PlayerEntity player, int shopItemId, uint count) => WizardGames.Soc.Common.Component.VehicleShopComponentHotfixWrapper.BuyImpl.Invoke(this, player, shopItemId, count); // VehicleShopComponentHotfix.BuyVirtual

    }
}

