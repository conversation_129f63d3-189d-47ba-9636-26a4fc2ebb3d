using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using System.Collections.Generic;
using System.Text;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Managers;
using WizardGames.Soc.SocWorld.TerrianMeta;

namespace WizardGames.Soc.Common.Entity
{
    public static partial class BoybandGroupEntityHotfixWrapper
    {
        public static Action<BoybandGroupEntity> InitImpl;
        public static Action<BoybandGroupEntity, bool> PostInitImpl;
        public static Action<BoybandGroupEntity> CleanupImpl;
        public static Action<BoybandGroupEntity> OnEvacuationImpl;



    }
}
namespace WizardGames.Soc.Common.Entity
{
    public partial class BoybandGroupEntity
    {
        public override partial void Init() => WizardGames.Soc.Common.Entity.BoybandGroupEntityHotfixWrapper.InitImpl.Invoke(this); // BoybandGroupEntityHotfix.Init
        public override partial void PostInit(bool isLoadFromDb) => WizardGames.Soc.Common.Entity.BoybandGroupEntityHotfixWrapper.PostInitImpl.Invoke(this, isLoadFromDb); // BoybandGroupEntityHotfix.PostInit
        public override partial void Cleanup() => WizardGames.Soc.Common.Entity.BoybandGroupEntityHotfixWrapper.CleanupImpl.Invoke(this); // BoybandGroupEntityHotfix.Cleanup
        public override partial void OnEvacuation() => WizardGames.Soc.Common.Entity.BoybandGroupEntityHotfixWrapper.OnEvacuationImpl.Invoke(this); // BoybandGroupEntityHotfix.OnEvacuation

    }
}

