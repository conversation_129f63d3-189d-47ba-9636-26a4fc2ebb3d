using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;

namespace WizardGames.Soc.SocWorld.RuleGraph
{
    public static partial class WaitAndExeNodeHotfixWrapper
    {
        public static Action<WaitAndExeNode, FlowContext> OnStartImpl;
        public static Action<WaitAndExeNode, FlowContext> OnStopImpl;



    }
}
namespace WizardGames.Soc.Common.CustomType
{
    public partial class WaitAndExeNode
    {
        public override partial void OnStart(FlowContext context) => WizardGames.Soc.SocWorld.RuleGraph.WaitAndExeNodeHotfixWrapper.OnStartImpl.Invoke(this, context); // WaitAndExeNodeHotfix.OnStart
        public override partial void OnStop(FlowContext context) => WizardGames.Soc.SocWorld.RuleGraph.WaitAndExeNodeHotfixWrapper.OnStopImpl.Invoke(this, context); // WaitAndExeNodeHotfix.OnStop

    }
}

