using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.Soc.SocWorld;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.CustomType
{
    public static partial class DirectoryNodeHotfixWrapper
    {
        public static Func<DirectoryNode, NodeBase, EOpCode> AddChildNodeImpl;
        public static Action<DirectoryNode, NodeBase> RemoveChildNodeImpl;



    }
}
namespace WizardGames.Soc.Common.CustomType
{
    public partial class DirectoryNode
    {
        public override partial EOpCode AddChildNode(NodeBase child) => WizardGames.Soc.Common.CustomType.DirectoryNodeHotfixWrapper.AddChildNodeImpl.Invoke(this, child); // DirectoryNodeHotfix.AddChildNodeVirtual
        public override partial void RemoveChildNode(NodeBase child) => WizardGames.Soc.Common.CustomType.DirectoryNodeHotfixWrapper.RemoveChildNodeImpl.Invoke(this, child); // DirectoryNodeHotfix.RemoveChildNodeVirtual

    }
}

