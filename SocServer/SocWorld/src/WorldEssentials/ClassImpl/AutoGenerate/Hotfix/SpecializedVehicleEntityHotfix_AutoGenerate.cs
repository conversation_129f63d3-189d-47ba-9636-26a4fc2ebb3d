using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Utility;

namespace WizardGames.Soc.SocWorld.Entity
{
    public static partial class SpecializedVehicleEntityHotfixWrapper
    {
        public static Action<SpecializedVehicleEntity> InitImpl;



    }
}
namespace WizardGames.Soc.Common.Entity
{
    public partial class SpecializedVehicleEntity
    {
        public override partial void Init() => WizardGames.Soc.SocWorld.Entity.SpecializedVehicleEntityHotfixWrapper.InitImpl.Invoke(this); // SpecializedVehicleEntityHotfix.Init

    }
}

