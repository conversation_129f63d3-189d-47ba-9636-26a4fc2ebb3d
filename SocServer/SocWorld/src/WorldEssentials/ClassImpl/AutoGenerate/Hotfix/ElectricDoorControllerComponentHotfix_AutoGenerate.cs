using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using System.Collections.Generic;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Event;

namespace WizardGames.Soc.Common.Component
{
    public static partial class ElectricDoorControllerComponentHotfixWrapper
    {
        public static Action<ElectricDoorControllerComponent, bool> PostInitImpl;
        public static Action<ElectricDoorControllerComponent> CleanupImpl;



    }
}
namespace WizardGames.Soc.Common.Component
{
    public partial class ElectricDoorControllerComponent
    {
        public override partial void PostInit(bool isLoadFromDb) => WizardGames.Soc.Common.Component.ElectricDoorControllerComponentHotfixWrapper.PostInitImpl.Invoke(this, isLoadFromDb); // ElectricDoorControllerComponentHotfix.PostInit
        public override partial void Cleanup() => WizardGames.Soc.Common.Component.ElectricDoorControllerComponentHotfixWrapper.CleanupImpl.Invoke(this); // ElectricDoorControllerComponentHotfix.Cleanup

    }
}

