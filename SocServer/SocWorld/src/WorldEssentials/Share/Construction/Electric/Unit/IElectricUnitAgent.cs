using System;
using WizardGames.Soc.Common.Construction;

namespace WizardGames.Soc.Common.Electric
{
    public interface IElectricUnit
    {
        //IPartGoAgent PartGoAgent { get; }
    }

    public interface IElectricUnitData
    {
        long EntityId { get; }
        long TemplateId { get; }
        float GenericFloat1 { get; set; }
        float GenericFloat2 { get; set; }
        int GenericInt1 { get; set; }
        int GenericInt2 { get; set; }
        float PosX { get; }
        float PosY { get; }
        float PosZ { get; }
        int PartExistMode { get; set; }

        /// <summary>
        /// 只允许在Init时访问，是否正在恢复的建筑
        /// 目前实现方式有问题，刷死羊时建造可能导致状态不对，同时需要区分存档与死羊恢复
        /// </summary>
        CreatePartReason CreateReason { get; }
        //
        void SendElectricNetworkUpdateImmediate();
        void ElectricMarkDirty();

        [Obsolete("删除Flag清除报错临时")]
        bool HasFlag(ConstructionEntityFlags.Flags flag);
        //IPartGoAgent PartGoAgent { get; }

        void RegisterFlagChange(Action<int, int,bool> flagChangeAction);
        void UnRegisterFlagChange(Action<int, int,bool> flagChangeAction);

        void GetIOSlots(ref IOSlotBaseOld[] _inputs, ref IOSlotBaseOld[] _outputs);

        bool IsSaveLoadRecovering => CreateReason == CreatePartReason.RecoverFromSavedData;
        bool IsPartGroupRecovering
            => CreateReason == CreatePartReason.CreateFromGMLoadPartGroup
            || CreateReason == CreatePartReason.CreateFromDeadSheep
            || CreateReason == CreatePartReason.CreateFromBoomHomeLoadPartGroup
            || CreateReason == CreatePartReason.CreateFromConstructionBlueprint
            || CreateReason == CreatePartReason.CreateByTask
            || CreateReason == CreatePartReason.CreateByGraphNode;
        bool IsNormalRecovering => IsSaveLoadRecovering || IsPartGroupRecovering;
    }

    public static class ElectricUnitEx
    {
        public static bool IsNullOrEmpty(this IElectricUnit unit) => unit == null;
        public static bool IsNullOrEmpty(this IElectricUnitData data) => data == null;
    }
}
