using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Extension;

namespace WizardGames.Soc.Common.Component
{
    public static class FunctionConst
    {
        internal enum EPath
        {
            // 任务
            ExtraTaskType,
            ExplorationTask,
            TreasureTask,
            BeebuzzTask,
            MedalTask,
            DailyTask,

            // 建造扩展功能
            ConstructionExtras,
            Plunder,
            DeadSheep,
            RaidProtection,

            // 出生点功能
            SleepingBagReborn,
            NearbyReborn,
            OutpostReborn,
            RandomReborn,
            ArchiveReborn,

            // 声望
            Reputation,
            ReputationBadge,

            // 组队
            Team,
            TeamSendInvite,
            TeamAcceptInvite,
            TeamRefuseInvite,
            TeamHandoverCaptain,
            TeamLeave,
            TeamKick,
            TeamChangeDuty,
            TeamRecruitment,
            TeamWarZoneRecruitment,

            // 其它功能
            SafeBox,
            RandomAirdrop,
            NewbieProtection,
            TeamTechUnlockShare,
        }

#region 任务
        public static readonly FunctionName Task = new("", EPath.ExtraTaskType);
        public static readonly FunctionName ExplorationTask = new("IsExploration", EPath.ExtraTaskType, EPath.ExplorationTask);
        public static readonly FunctionName TreasureTask = new("IsTreasure", EPath.ExtraTaskType, EPath.TreasureTask);
        public static readonly FunctionName BeebuzzTask = new("IsBeeBuzz", EPath.ExtraTaskType, EPath.BeebuzzTask);
        // 勋章任务可以在大厅开关, 需要动态设置
        public static readonly FunctionName MedalTask = new FunctionName("", EPath.ExtraTaskType, EPath.MedalTask).EnableDynamicSwitch();
        public static readonly FunctionName DailyTask = new("", EPath.ExtraTaskType, EPath.DailyTask);
#endregion
        // 建造扩展功能
        public static readonly FunctionName ConstructionExtras = new("", EPath.ConstructionExtras);
        /// <summary>
        /// 抄家战报
        /// </summary>
        public static readonly FunctionName Plunder = new("EnablePlunder", EPath.ConstructionExtras, EPath.Plunder);
        public static readonly FunctionName DeadSheep = new("EnableDeadSheep", EPath.ConstructionExtras, EPath.DeadSheep);
        public static readonly FunctionName RaidProtection = new("EnableRaidProtection", EPath.ConstructionExtras, EPath.RaidProtection);

        // 出生点功能
        public static readonly FunctionName SleepingBagReborn = new FunctionName("EnableSleepBagReborn", EPath.SleepingBagReborn).EnableDynamicSwitch();
        public static readonly FunctionName NearbyReborn = new FunctionName("EnableNearbyReborn", EPath.NearbyReborn).EnableDynamicSwitch();
        public static readonly FunctionName OutpostReborn = new FunctionName("EnableOutpostReborn", EPath.OutpostReborn).EnableDynamicSwitch();
        public static readonly FunctionName RandomReborn = new FunctionName("EnableRandomReborn", EPath.RandomReborn).EnableDynamicSwitch();
        public static readonly FunctionName ArchiveReborn = new FunctionName("EnableArchiveReborn", EPath.ArchiveReborn).EnableDynamicSwitch();

        // 声望
        public static readonly FunctionName Reputation = new("IsActiveReputation", EPath.Reputation);
        public static readonly FunctionName ReputationBadge = new("IsActiveReputationBadge", EPath.Reputation, EPath.ReputationBadge);

        // 组队
        public static readonly FunctionName Team = new FunctionName("EnableTeam", EPath.Team).EnableDynamicSwitch();
        public static readonly FunctionName TeamSendInvite = new FunctionName("EnableSendInvite", EPath.Team, EPath.TeamSendInvite).EnableDynamicSwitch();
        public static readonly FunctionName TeamAcceptInvite = new FunctionName("EnableAcceptInvite", EPath.Team, EPath.TeamAcceptInvite).EnableDynamicSwitch();
        public static readonly FunctionName TeamRefuseInvite = new FunctionName("EnableRefuseInvite", EPath.Team, EPath.TeamRefuseInvite).EnableDynamicSwitch();
        public static readonly FunctionName TeamHandoverCaptain = new FunctionName("EnableHandoverCaptain", EPath.Team, EPath.TeamHandoverCaptain).EnableDynamicSwitch();
        public static readonly FunctionName TeamLeave = new FunctionName("EnableLeave", EPath.Team, EPath.TeamLeave).EnableDynamicSwitch();
        public static readonly FunctionName TeamKick = new FunctionName("EnableKick", EPath.Team, EPath.TeamKick).EnableDynamicSwitch();
        public static readonly FunctionName TeamChangeDuty = new FunctionName("EnableChangeDuty", EPath.Team, EPath.TeamChangeDuty).EnableDynamicSwitch();
        public static readonly FunctionName TeamRecruitment = new FunctionName("EnableTeamRecruitment", EPath.Team, EPath.TeamRecruitment).EnableDynamicSwitch();
        public static readonly FunctionName TeamWarZoneRecruitment = new FunctionName("EnableWarZoneTeamRecruitment", EPath.Team, EPath.TeamWarZoneRecruitment).EnableDynamicSwitch();


        // 其它功能
        public static readonly FunctionName SafeBox = new("Issafebox", EPath.SafeBox);
        public static readonly FunctionName RandomAirdrop = new FunctionName("IsAirDrop", EPath.RandomAirdrop).EnableDynamicSwitch();
        public static readonly FunctionName NewbieProtection = new("EnableNewbieProtection", EPath.NewbieProtection);
        public static readonly FunctionName TeamTechUnlockShare = new("EnableTeamTechUnlockShare", EPath.TeamTechUnlockShare);
    }

    public class FunctionName
    {
        public string TableColumnName { get; private set; }
        public List<long> Path { get; private set; }
        public bool DynamicSwitch { get; private set; }

        internal FunctionName(string tableColumnName, params FunctionConst.EPath[] path)
        {
            TableColumnName = tableColumnName;
            Path = new List<long>(path.Length);
            for (int i = 0; i < path.Length; i++)
            {
                Path.Add((long)path[i]);
            }
        }

        public FunctionName EnableDynamicSwitch()
        {
            DynamicSwitch = true;
            return this;
        }
    }

    public partial class FunctionSwitchComponent
    {
        public static FunctionSwitchComponent Instance;

#if !SOC_WORLD
        public override void PostInit()
        {
            Instance = this;
        }

        public override void Cleanup()
        {
            Instance = null;
        }
#endif



        public bool IsEnable(FunctionName configName)
        {
            NodeBase cur = Root;
            for (var i = 0; i < configName.Path.Count; i++)
            {
                var configNode = cur.GetChildNodeAs<DirectoryWithBoolNode>(configName.Path[i]);
                if (configNode == null)
                {
                    Logger.Error($"Trying to get config of {configName.TableColumnName}, path {configName.Path.Print()}, but node not found");
                    return false;
                }
                if (configNode.Value == false)
                {
                    return false;
                }
                cur = configNode;
            }
            return true;
        }


        public bool IsDisable(FunctionName configName) => !IsEnable(configName);
    }
}
