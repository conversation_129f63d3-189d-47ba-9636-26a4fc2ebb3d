using System;
using System.Collections;
using WizardGames.Soc.Common.Utility;
using System.Collections.Generic;

namespace WizardGames.Soc.Common.ObjPool
{
    public static partial class Pool
    {
        private static HashSet<Action> actionClears = new HashSet<Action>();

        #region c#对象池
        /// <summary>
        /// 获取一个C#对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <returns></returns>
        public static T Get<T>() where T : class, new()
        {
            return Singleton<ObjectPool<T>>.Instance.Get();
        }
        
        public static void ClearAllPools()
        {
            foreach (var clearFunc in actionClears)
            {
                clearFunc?.Invoke();
            }
            actionClears.Clear();
        }

        /// <summary>
        /// 放回一个C#对象, 为了保证正常监测对象池请保证该对象是从对象池中获取的对象
        /// </summary>
        /// <param name="element"></param>
        /// <typeparam name="T"></typeparam>
        public static void Release<T>(T element) where T : class, new()
        {
            Singleton<ObjectPool<T>>.Instance.Release(element);
        }
        
        /// <summary>
        /// 初始化对象池并返回
        /// </summary>
        /// <param name="config">对象池的配置</param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static ObjectPool<T> Init<T>(PoolConfig<T> config) where T : class, new()
        {
            var pool = new ObjectPool<T>(config);
            Singleton<ObjectPool<T>>.Instance = pool;
            return pool;
        }
        
        /// <summary>
        /// 清理对象池，清理对象池占用的内存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        public static void Clear<T>() where T : class, new()
        {
            Singleton<ObjectPool<T>>.Clear();
            actionClears.Remove(Singleton<ObjectPool<T>>.Clear);
        }
        
        /// <summary>
        /// 设置对象池Get的回调
        /// </summary>
        /// <param name="actionOnGet"></param>
        /// <typeparam name="T"></typeparam>
        public static void SetGetAct<T>(Action<T> actionOnGet) where T : class, new()
        {
            Singleton<ObjectPool<T>>.Instance.SetGetAct(actionOnGet);
        }
        
        /// <summary>
        /// 设置对象池Release的回调
        /// </summary>
        /// <param name="actionOnRelease"></param>
        /// <typeparam name="T"></typeparam>
        public static void SetReleaseAct<T>(Action<T> actionOnRelease) where T : class, new()
        {
            Singleton<ObjectPool<T>>.Instance.SetReleaseAct(actionOnRelease);
        }
        
        /// <summary>
        /// 设置对象池Destroy时的回调（对象池被clear或归还对象溢出对象池时触发）
        /// </summary>
        /// <param name="actionOnDestroy"></param>
        /// <typeparam name="T"></typeparam>
        public static void SetDestroyAct<T>(Action<T> actionOnDestroy) where T : class, new()
        {
            Singleton<ObjectPool<T>>.Instance.SetDestroyAct(actionOnDestroy);
        }

#if UNITY_EDITOR
        /// <summary>
        /// 统计对象池使用情况，开启ObjPoolInspect才会调用
        /// 正常运行时不要使用该方法，避免产生反射带来的性能问题
        /// </summary>
        /// <returns></returns>
        public static Dictionary<Type, int> GetPoolObjectCount()
        {
            Dictionary<Type, int> poolObjectCount = new();
            foreach (var clearAction in actionClears)
            {
                Type instType = clearAction.Method.DeclaringType;
                if (instType == null)
                    continue;

                object inst = instType.GetMethod("get_Instance")?.Invoke(null, null);
                Type objType = instType.GetGenericArguments()?[0]?.GetGenericArguments()?[0];
                if (inst != null && objType != null)
                {
                    var cntMethod = instType.GetGenericArguments()?[0]?.GetMethod("get_Count");
                    if (cntMethod == null)
                        continue;
                    var cnt = (int)cntMethod.Invoke(inst, null);
                    poolObjectCount[objType] = cnt;
                }
            }
            return poolObjectCount;
        }
#endif

        #endregion


        #region 容器对象池
        /// <summary>
        /// 获取对象池中获取容器,为了减少GC需要指定容器的容量
        /// </summary>
        /// <param name="capacity">容器容量</param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T GetColl<T>(int capacity = 10) where T : class, ICollection, new()
        {
            return Singleton<CollectionPool<T>>.Instance.Get(capacity);
        }
        
        /// <summary>
        /// 归还容器到对象池中，会根据归还时的容器容量来进行归还
        /// </summary>
        /// <param name="element">归还对象，目前只支持常用的几种容器类型，后续需要的话需要手动扩展</param>
        /// <typeparam name="T"></typeparam>
        public static void ReleaseColl<T>(T element, int capacity = 10) where T : class, ICollection, new()
        {
            Singleton<CollectionPool<T>>.Instance.Release(element, capacity);
        }
        
        /// <summary>
        /// 初始化对象池容器
        /// </summary>
        /// <param name="config">对象池配置，容器对象池可配置CollectionStep</param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static CollectionPool<T> InitColl<T>(PoolConfig<T> config) where T : class, ICollection, new()
        {
            var pool = new CollectionPool<T>(config);
            Singleton<CollectionPool<T>>.Instance = pool;
            return pool;
        }
        
        /// <summary>
        /// 清理容器对象池
        /// </summary>
        /// <typeparam name="T"></typeparam>
        public static void ClearColl<T>() where T : class, ICollection, new()
        {
            
            Singleton<CollectionPool<T>>.Instance.Clear();
            Singleton<CollectionPool<T>>.Clear();
        }
        
        /// <summary>
        /// 设置容器对象池的Get回调
        /// </summary>
        /// <param name="action"></param>
        /// <typeparam name="T"></typeparam>
        public static void SetCollGetAct<T>(Action<T> action) where T : class, ICollection, new()
        {
            Singleton<CollectionPool<T>>.Instance.SetGetAct(action);
        }
        
        /// <summary>
        /// 设置容器对象池的Release回调
        /// </summary>
        /// <param name="action"></param>
        /// <typeparam name="T"></typeparam>
        public static void SetCollReleaseAct<T>(Action<T> action) where T : class, ICollection, new()
        {
            Singleton<CollectionPool<T>>.Instance.SetReleaseAct(action);
        }
        
        /// <summary>
        /// 设置容器对象池的Destroy回调，在清理对象池或归还对象溢出时会触发
        /// </summary>
        /// <param name="action"></param>
        /// <typeparam name="T"></typeparam>
        public static void SetCollDestroyAct<T>(Action<T> action) where T : class, ICollection, new()
        {
            Singleton<CollectionPool<T>>.Instance.SetDestroyAct(action);
        }

        #endregion

        #region 简单单例类
        static class Singleton<T> where T: class, IDisposable, new()
        {
            private static object resourceLock = new object();
            private static T instance;

            public static T Instance
            {
                get
                {
                    if (instance != null)
                    {
                        return instance;
                    }

                    lock (resourceLock)
                    {
                        instance = new T();//SocUtility.New<T>();
                        actionClears.Add(Clear);
                        return instance;
                    }
                }
                set
                {
                    if (instance == null)
                    {
                        instance = value;
                        actionClears.Add(Clear);
                    }
                    else
                    {
                        throw new InvalidOperationException("Trying to set an initialized Singleton");
                    }
                }
            }

            public static void Clear()
            {
                if (instance != null)
                {
                    instance.Dispose();
                    instance = null;
                }
            }
        }
        #endregion


    }
}