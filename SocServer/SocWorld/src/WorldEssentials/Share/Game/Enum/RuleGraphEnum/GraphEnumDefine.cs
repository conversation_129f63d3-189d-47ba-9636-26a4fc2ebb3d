using WizardGames.Soc.Common.RuleGraph;

namespace WizardGames.Soc.Common.SimpleCustom
{
    [GraphType]
    public enum ECompareType
    {
        Less = 0,
        LessAndEqual,
        Equal,
        Greater,
        GreaterAndEqual,
        NotEqual,

    }

    [GraphType]
    public enum EMultiLangStrType
    {
        NORMAL = 0,
        MULTI_LANG = 1
    }

    [GraphType]
    public enum ETableType
    {
       DATA,
       CONFIG
    }

    // [GraphType(NeedGenerate = false)]
    [GraphType]
    public enum EEventParamsType
    {
        Bool,
        Long,
        Float,
        String,
    }
    
    public enum EVariableType
    {
        Int,
        Ulong,
        
        Bool,
        Long,
        Float,
        String,
        Vector3,
        Object, 
    }

    [GraphType]
    public enum EDataSetType
    {
        None,
        Long,
        Float,
        String,
        Vector3,
        Object,
    }

    [GraphType]
    public enum EStatPanelDisplayableMode
    {
        Personal = 0,
        Team = 1,
        TeamPoints = 2
    }

    [GraphType]
    public enum EGameResultType
    {
        Draw = 0,
        Victory = 1,
        Defeat = 2,
        TimeOver = 3,
    }

    public enum ContextLifeCycle
    {
        None = 0,
        Start = 1,
        Resume = 2,
        Pause = 3,
        Stop = 4,
        Exception = 5,
    }

    [GraphType]
    public enum EProgressBarType
    {
        RaidAttack = 0,
        RaidDefense = 1,
        RaidSymmetry = 2,
    }

    [GraphType]
    public enum EGivePlayerItemType
    {
        Born = 0,
        Other = 1,
    }


    [GraphType]
    public enum EServerType
    {
        Warzone, // 战区
        Room, // 房间
        UGC, // 用户自定义内容
    }
    
    [GraphType]
    public enum ESystemVarRoot
    {
        PlayerData=1,
        TeamData=2,
        GlobalData=3
    }
    
    [GraphType]
    public enum ESystemVarPropertyId
    {
        TeamId = 1,
        PlayerId = 2,
        Hp = 3,
        Name = 4,
    }
}
