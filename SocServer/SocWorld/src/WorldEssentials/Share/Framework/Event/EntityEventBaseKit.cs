#if !SOC_WORLD
using System;
using System.Collections.Generic;
using WizardGames.Soc.Share.Framework.Event;

namespace WizardGames.Soc.Share.Framework.Event
{
    public class EntityEventBaseKit : EventBaseKit
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(EntityEventBaseKit));

        /// <summary>
        /// 监听其他entity的事件
        /// </summary>
        public Dictionary<long, HashSet<long>> SubscriberEntities { get; protected set; }

        public EntityEventBaseKit(long ownerId)
        {
            OwnerId = ownerId;
            handlers = new();
            Subscribers = new();
            SubscriberEntities = new();
        }

        public override long AddTargetEvent<TEventInstance>(long targetEntityId, Action<TEventInstance> callback)
        {
            var taretEntity = EntityManager.Instance.GetEntity(targetEntityId);
            if (taretEntity == null)
            {
                logger.WarnFormat("ent:{0}, fail to get mgr", taretEntity);
                return 0;
            }

            var eventSeq = taretEntity.AddEvent(callback);
            if (!Subscribers.TryGetValue(targetEntityId, out var records))
            {
                records = new HashSet<long>();
                Subscribers.Add(targetEntityId, records);
            }
            records.Add(eventSeq);
            return eventSeq;
        }

        public override void RemoveTargetEvent(long targetEntityId, long eventSeq)
        {
            if (!Subscribers.TryGetValue(targetEntityId, out var records))
            {
                logger.ErrorFormat("ent:{0}, not subscriber target entity", targetEntityId);
                return;
            }

            if (!records.Contains(eventSeq))
            {
                logger.ErrorFormat("seq:{0}, not record event seq", eventSeq);
                return;
            }
            records.Remove(eventSeq);

            var targetEntity = EntityManager.Instance.GetEntity(targetEntityId);
            if (targetEntity == null)
            {
                return;
            }

            targetEntity.SafeRemoveEvent(ref eventSeq);
        }

        /// <summary>
        /// collection可以监听三种target事件分别为：collection, entity, worldMgr
        /// collection上由于common代码的原因，collection侧在本类删除。其他两种在world中的静态方法删除
        /// </summary>
        public override void ClearEvents()
        {
            SubscriberEntities.Clear();
            foreach (var (targetEntityId, records) in Subscribers)
            {
                var targetEntity = EntityManager.Instance.GetEntity(targetEntityId);
                if (targetEntity != null)
                {
                    foreach (var record in records)
                    {
                        targetEntity.SafeRemoveEvent(record);
                    }
                }
            }
            Subscribers.Clear();
            handlers.ClearEvents();
        }
    }
}
#endif