using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.Entity
{
    [Entity(SyncRange = ESyncRange.LocalOnly, Persistent = EPersistentType.None)]
    public partial class SpawnGroupJunkpileEntity : SpawnGroupEntity
    {
        [CustomField(IsPersistent = true)]
        protected long _junkpileRuleId;
        [CustomField(IsPersistent = true)]
        protected bool _haveDestroyRes;

        [CustomField(IsPersistent = true)]
        protected float _rotateX;
        [CustomField(IsPersistent = true)]
        protected float _rotateY;
        [CustomField(IsPersistent = true)]
        protected float _rotateZ;
        [CustomField(IsPersistent = true)]
        protected float _rotateW;

        [CustomField(IsPersistent = true)]
        protected float _posX;
        [CustomField(IsPersistent = true)]
        protected float _posY;
        [CustomField(IsPersistent = true)]
        protected float _posZ;
    }
}
