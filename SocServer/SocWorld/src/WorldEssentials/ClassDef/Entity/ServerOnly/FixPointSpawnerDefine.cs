using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.Entity
{
    [Entity(SyncRange = ESyncRange.All, Persistent = EPersistentType.Normal, DefaultConstructor = false)]
    public partial class FixPointSpawner : EntityBase, IGlobalSyncEntity
    {
        /// <summary>
        /// 男团刷新组是否存在
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_GLOBAL_SYNC)]
        private bool _boybandExist;

        /// <summary>
        /// 蜂鸣小队组是否存在
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_GLOBAL_SYNC)]
        private bool _beeBuzzExist;

        /// <summary>
        /// 巡逻entity
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_GLOBAL_SYNC)]
        protected BasicTypeHashSet<long> _patrolEntityIds;

        /// <summary>
        /// 即将发生的事件,key是MarkerType
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_GLOBAL_SYNC)]
        private BasicValueDictionary<int, uint> _comingEvents;
        /// <summary>
        /// 正在发生事件,key是MarkerType value 是结束时间戳
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_GLOBAL_SYNC)]
        private BasicValueDictionary<int, uint> _endingEvents;

        /// <summary>
        /// 商店使用的刷新点
        /// </summary>
        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        protected BasicTypeHashSet<int> _shopUsePointIds;

        /// <summary>
        /// POI事件池Id -> POI事件字典
        /// </summary>
        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        protected CustomValueDictionary<int, RuleEventDict> _poolEvents;
        /// <summary>
        /// 遗迹ID -> 怪物数量
        /// </summary>
        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        private BasicValueDictionary<int, int> _monsterCount;

        /// <summary>
        /// 遗迹内任务刷的Entity
        /// </summary>
        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        protected CustomValueDictionary<int, TaskMonument> _taskMonuments;
    }
}
