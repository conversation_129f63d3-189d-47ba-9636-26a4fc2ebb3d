using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Common.Framework.Const;

namespace WizardGames.Soc.Common.CustomType
{
    [CustomType(TreatAsSimpleCustomType = true)]
    public partial class CustomVector3 : CustomTypeBase, IVector3
    {
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected float _x;

        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected float _y;

        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected float _z;
    }

    [CustomType(TreatAsSimpleCustomType = true)]
    public partial class CustomVector3List : CustomTypeBase
    {
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected CustomTypeList<CustomVector3> _list;

        public CustomVector3List(float x, float y, float z)
        {
            List = new()
            {
                new(x, y, z)
            };
        }

        public CustomVector3List()
        {
            List = new();
        }
    }
}
