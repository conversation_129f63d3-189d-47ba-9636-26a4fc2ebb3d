using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.CustomType
{
    /// <summary>
    /// 在物品栏中的建筑物Entity
    /// </summary>
    [CustomType]
    public partial class ConstructionItemCustom : EmbeddedCustomBase, IHeldItemEntity
    {
        /// <summary>
        /// 所属玩家EntityId
        /// </summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected long _ownerEntityId;

        /// <summary>
        /// 物品唯一Id
        /// </summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected long _itemUid;

        /// <summary>
        /// 表格Id
        /// </summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected long _tableId;

        /// <summary>
        /// 数量
        /// </summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected int _amount;

        /// <summary>
        /// 耐久度
        /// </summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected float _condition;

        /// <summary>
        /// 最大耐久度
        /// </summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected float _maxCondition;

        // <summary>
        /// 槽位
        ///</summary>
        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected int _position;

        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected int _templateId;

        [CustomField(FieldLod = CustomLod.LOD_MEDIUM, IsPersistent = true, SyncRange = ESyncRange.All)]
        protected long _skinId;
    }
}
