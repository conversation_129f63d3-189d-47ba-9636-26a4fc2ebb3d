using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.CustomType
{
    /// <summary>
    /// 物品Node基类
    /// </summary>
    [CustomType]
    public abstract partial class BaseItemNode : NodeBase
    {
        /// <summary>
        /// 耐久值
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, FieldLod = CustomLod.LOD_50M, IsPersistent = true)]
        protected float _condition;

        /// <summary>
        /// 最大耐久值
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, FieldLod = CustomLod.LOD_50M, IsPersistent = true)]
        protected float _maxCondition;

        /// <summary>
        /// 如果该物品有对应的EntityCustomType，此处记录该EntityCustomType的根Entity的EntityId，否则为0
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, FieldLod = CustomLod.LOD_50M, IsPersistent = true)]
        protected long _ownerEntityId;

        /// <summary>
        /// 是否已被锁住
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, FieldLod = CustomLod.LOD_50M, IsPersistent = true)]
        protected bool _isLocked;

        /// <summary>
        /// 是否已被锁住
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, FieldLod = CustomLod.LOD_50M, IsPersistent = true)]
        protected long _skinId;

        [CustomField(SyncRange = ESyncRange.All, FieldLod = CustomLod.LOD_50M, IsPersistent = true)]
        protected bool _isEnterInventory;

        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        public ulong _belongRoleId;
    }
}
